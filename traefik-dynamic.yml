# Dynamic configuration for Traefik

# TLS configuration for internal self-signed certificates
tls:
  options:
    default:
      minVersion: "VersionTLS12"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_RSA_WITH_AES_128_GCM_SHA256"
  certificates:
    - certFile: /etc/traefik/certs/traefik.crt
      keyFile: /etc/traefik/certs/traefik.key

# HTTP middlewares
http:
  middlewares:
    # Security headers middleware
    security-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          X-Content-Type-Options: "nosniff"
          X-Frame-Options: "DENY"
          X-XSS-Protection: "1; mode=block"
          Strict-Transport-Security: "max-age=31536000; includeSubDomains"

    # WebSocket headers middleware
    websocket-headers:
      headers:
        customRequestHeaders:
          Connection: "Upgrade"
          Upgrade: "websocket"
          X-Forwarded-Proto: "https"
          X-Forwarded-For: ""
          X-Real-IP: ""

    # CORS middleware for API
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        accessControlAllowOriginList:
          - "https://care.continuia.ai"
          - "https://desk.continuia.ai"
        accessControlAllowHeaders:
          - "Content-Type"
          - "Authorization"
          - "X-Requested-With"
        accessControlExposeHeaders:
          - "Content-Length"
        accessControlAllowCredentials: true
        accessControlMaxAge: 100
        addVaryHeader: true
