---
type: "always_apply"
description: "Coding Guidelinesr for IDE"
---

# Golden Foundations

## Hard Rules
- **NEVER** create temp files, scratch dirs, scaffolds, or playgrounds in repo.  
- **NEVER** execute database migrations in any environment. You may author migration files only.  
- **NEVER** expose or log secrets, tokens, keys, PHI/PII.  
- **NEVER** run tasks on host. All builds, tests, and tools **must** run in containers.  
- **NEVER** reduce security posture, disable checks, or add TODOs that defer security fixes.  
- **ALWAYS** instrument all code paths with OpenTelemetry (OTel).  
- **NEVER** collapse boundaries between dev/test/prod.  

## Safe Defaults
- Make smallest possible secure change.  
- Match prevailing local style.  
- Prefer explicit, documented config.  
- Block and report if unsafe.  

# Build & Runtime Discipline

## Containers
- Assume host volumes are not mounted; rebuild required for changes.  
- All commands run inside `docker compose run --rm <svc> <cmd>`.  
- Reject any bypass of containerization.  

## Change Discipline
- Default posture: minimal diff, reversible edits.  
- Avoid cross-cutting changes unless requested.  
- Stop and issue Blocking Report if rules prevent delivery.  

## File Boundaries
- May edit: explicitly referenced files and direct neighbors (tests, configs).  
- Must not edit: CI/CD templates, infra manifests, secrets, or policy unless task specifies.  
- Must not leave: commented-out code or `.tmp`, `.bak`, “playground” artifacts.  

# Configuration & Database Rules

## Configuration
- Always read config from env vars.  
- Never hardcode values or provide insecure defaults.  
- Never print secrets or sensitive payloads in logs.  

## Database & Migrations
- You may generate migration files using the repo’s tool.  
- Do not execute migrations, run raw DDL/DML, or perform backfills.  
- For non-trivial schema changes, provide rollback notes and runtime impact.  

# Testing & Observability

## Testing & Linting
- All tests, lint, and type checks must run inside containers.  
- Maintain or improve coverage; ≥80% on changed code if no baseline exists.  
- Add focused unit tests; no snapshot dumps.  

## Observability (OTel-First)
- Always create root spans, propagate W3C context, and correlate logs.  
- Default collector endpoint = `null`; emit to console in dev/test.  
- Override collector only via env or docker-compose.  
- Never hardcode endpoints.  

# Reliability, Security & Prohibited Behaviors

## Reliability
- Avoid N+1 queries, infinite loops, or unbounded retries.  
- Respect timeouts and retry strategies.  
- Any cache/queue must define eviction and failure handling.  

## Prohibited Behaviors
- Running host commands (npm, tests, DB clients).  
- Introducing prototypes or scratch dirs in repo.  
- Copy-pasting third-party code without license/attribution check.  
- Disabling OTel.  
- Silent dependency churn unrelated to task.  

# Checklists & Escalation

## Definition of Done
- [ ] Minimal diff; no temp artifacts.  
- [ ] All commands containerized; rebuild used.  
- [ ] No secrets in code/logs.  
- [ ] Tests/lint pass; coverage maintained.  
- [ ] Migration files authored only, never executed.  
- [ ] Security posture unchanged or improved.  
- [ ] Docs updated if config/behavior changed.  
- [ ] OTel spans/logs present with trace IDs.  

## Escalation Protocol
If rules prevent task completion:
- Stop immediately.  
- Output a **Decision Log** with:  
  - **Problem** (one sentence)  
  - **Options** (2–3 trade-offs)  
  - **Recommendation** (pick one)  
  - **Rollback** (how to revert)  

