# Version control
.git
.gitignore
.github
.gitlab
.gitlab-ci.yml

# Node.js / JavaScript / TypeScript
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.pnpm-store
.npm
.yarn
.yarnrc.yml
.pnp.*

# Environment variables and secrets
.env
.env.*
!.env.example
.envrc
*.pem
*.key

# Build outputs
dist
dist-ssr
build
out
.next
.nuxt
.output
.cache
.parcel-cache
.rollup.cache
.vite

# Testing
coverage
.nyc_output

# Python
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.venv
venv/
ENV/
env/
*.egg-info/

# Docker
.dockerignore
Dockerfile*
docker-compose*
docker-compose.*.yml

# Data volumes and local storage
.data
.data/
*.sqlite3
*.db

# Logs
logs
*.log

# IDE and editors
.idea
.vscode
*.swp
*.swo
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Project specific
.gitlab/runner-cache
.DS_Store
Thumbs.db

# Testing
coverage
.nyc_output
jest.config.js
test
tests
__tests__

# Documentation
README.md
CHANGELOG.md
docs
*.md

# Logs
logs
*.log

# TypeScript
*.tsbuildinfo
tsconfig.json
tsconfig.*.json

# Misc
.cache
.temp
.tmp
