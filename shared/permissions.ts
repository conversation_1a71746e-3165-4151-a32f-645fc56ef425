// Shared permission logic for frontend and backend
// This ensures consistent authorization checks across the application

// Permission structure: resource:action or resource:modifier
// Examples: 'cases:read', 'patient:own', 'doctor:assigned'

export interface Permission {
  resource: string;
  action: string;
  modifier?: string;
}

export interface UserContext {
  id: string;
  role: 'patient' | 'doctor' | 'agent' | 'admin';
  email: string;
}

export interface ResourceContext {
  id?: string;
  patientId?: string;
  doctorId?: string;
  assignedAgentId?: string;
  // Note: assignedDoctorId has been removed in favor of multi-doctor assignment system
  // Doctors are now assigned via the case_doctors table
  status?: string;
  [key: string]: any;
}

export class PermissionService {
  // Parse permission string into structured format
  static parsePermission(permission: string): Permission {
    const [resource, actionWithModifier] = permission.split(':');
    if (!actionWithModifier) {
      return { resource, action: 'access' };
    }
    
    const [action, modifier] = actionWithModifier.includes('/') 
      ? actionWithModifier.split('/') 
      : [actionWithModifier, undefined];
      
    return { resource, action, modifier };
  }
  
  // Check if user has a specific role
  static hasRole(user: UserContext, role: string): boolean {
    return user.role === role;
  }
  
  // Check if user has any of the specified roles
  static hasAnyRole(user: UserContext, roles: string[]): boolean {
    return roles.includes(user.role);
  }
  
  // Check if user is the owner of a resource
  static isOwner(user: UserContext, resource: ResourceContext): boolean {
    if (user.role === 'patient') {
      return resource.patientId === user.id;
    } else if (user.role === 'doctor') {
      return resource.doctorId === user.id;
    }
    return false;
  }
  
  // Check if user is assigned to a resource
  static isAssigned(user: UserContext, resource: ResourceContext): boolean {
    if (user.role === 'agent') {
      return resource.assignedAgentId === user.id;
    }
    return false;
  }
  
  // Check if a case is in draft status
  static isDraft(resource: ResourceContext): boolean {
    return resource.status === 'draft';
  }
  
  // Check if user can access a resource based on permission string
  static canAccessResource(user: UserContext, permission: string, resource?: ResourceContext): boolean {
    const { resource: resourceType, action, modifier } = this.parsePermission(permission);
    
    // Check if the permission exists in the matrix
    if (!PERMISSION_MATRIX[resourceType] || !PERMISSION_MATRIX[resourceType][action]) {
      return false;
    }
    
    const allowedRoles = PERMISSION_MATRIX[resourceType][action];
    
    // Check each allowed role
    for (const allowedRole of allowedRoles) {
      // Handle role with modifier (e.g., 'patient:own')
      if (allowedRole.includes(':')) {
        const [role, requiredModifier] = allowedRole.split(':');
        
        // Check if user has the required role
        if (user.role === role) {
          // Handle the modifier
          switch (requiredModifier) {
            case 'own':
              // For list operations without specific resource, we allow the request to proceed
              // The route handler will filter results based on ownership
              if (!resource) {
                return true;
              }
              return this.isOwner(user, resource);
            case 'assigned':
              // For list operations without specific resource, we allow the request to proceed
              // The route handler will filter results based on assignment
              if (!resource) {
                return true;
              }
              return this.isAssigned(user, resource);
            case 'draft':
              if (!resource) {
                return false;
              }
              return this.isOwner(user, resource) && this.isDraft(resource);
            default:
              // Check if user has the specific role
              return this.hasRole(user, role);
          }
        }
      } else {
        // Handle simple role (e.g., 'admin')
        if (this.hasRole(user, allowedRole)) {
          return true;
        }
      }
    }
    
    return false;
  }
}

// Export permission matrix for consistency between frontend and backend
export const PERMISSION_MATRIX = {
  cases: {
    create: ['patient'],
    read: ['patient:own', 'doctor:assigned', 'admin', 'agent'],
    update: ['patient:own:draft', 'doctor:assigned', 'admin'],
    delete: ['admin'],
    assign: ['admin', 'agent'],
    submit: ['patient:own'],
    complete: ['doctor:assigned', 'admin']
  },
  appointments: {
    create: ['patient', 'doctor', 'admin', 'agent'],
    read: ['patient:own', 'doctor:own', 'admin', 'agent'],
    update: ['patient:own', 'doctor:own', 'admin'],
    cancel: ['patient:own', 'doctor:own', 'admin'],
    delete: ['admin']
  },
  documents: {
    create: ['patient', 'doctor', 'admin'],
    read: ['patient:own', 'doctor:assigned', 'admin', 'agent'],
    update: ['patient:own', 'admin'],
    delete: ['patient:own', 'admin']
  },
  users: {
    create: ['admin'],
    read: ['patient:own', 'doctor:own', 'agent:own', 'admin'],
    update: ['patient:own', 'doctor:own', 'agent:own', 'admin'],
    delete: ['patient:own', 'doctor:own', 'agent:own', 'admin'],
    assign_role: ['admin'],
    impersonate: ['admin']
  },
  dashboard: {
    read: ['patient', 'doctor', 'agent', 'admin']
  }
};
