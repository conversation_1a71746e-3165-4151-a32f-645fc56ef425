const fs = require('fs');
const { execSync } = require('child_process');

// Compile TypeScript files to JavaScript
console.log('Compiling shared TypeScript files...');

// Compile permissions.ts to permissions.js
try {
  execSync('npx tsc permissions.ts --outDir . --target ES2022 --module ESNext --moduleResolution node --allowSyntheticDefaultImports --esModuleInterop --skipLibCheck --declaration false', { 
    cwd: './shared',
    stdio: 'inherit'
  });
  console.log('Successfully compiled permissions.ts to permissions.js');
} catch (error) {
  console.error('Error compiling permissions.ts:', error.message);
  process.exit(1);
}

console.log('Build complete!');
