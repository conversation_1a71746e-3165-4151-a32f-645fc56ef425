{"name": "@continuia/shared", "version": "1.0.0", "description": "Shared utilities, types, and services for Continuia Healthcare Platform", "main": "index.js", "type": "module", "exports": {"./services/api": "./services/api.ts", "./constants/theme": "./constants/theme.ts", "./types/case": "./types/case.ts", "./utils/utils": "./utils/utils.ts", "./permissions": "./permissions.ts"}, "scripts": {"build": "tsc", "type-check": "tsc --noEmit"}, "dependencies": {"clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5.0.0"}}