# PostgreSQL Configuration for Docker Development
# Simplified configuration for Docker container

#------------------------------------------------------------------------------
# CONNECTIONS AND AUTHENTICATION
#------------------------------------------------------------------------------

listen_addresses = '*'			# Listen on all addresses for Docker
port = 5432				# Standard PostgreSQL port
max_connections = 100			# Sufficient for development

#------------------------------------------------------------------------------
# RESOURCE USAGE
#------------------------------------------------------------------------------

shared_buffers = 128MB			# Memory for shared buffers
effective_cache_size = 1GB		# Estimate of available OS cache
work_mem = 4MB				# Memory for sorts and joins
maintenance_work_mem = 64MB		# Memory for maintenance operations

#------------------------------------------------------------------------------
# WRITE-AHEAD LOG
#------------------------------------------------------------------------------

wal_level = replica			# Minimal for development
fsync = on				# Keep on for data safety
synchronous_commit = on			# Keep on for consistency

#------------------------------------------------------------------------------
# LOGGING
#------------------------------------------------------------------------------

log_destination = 'stderr'		# Log to stderr for Docker
logging_collector = off			# Don't collect logs to files
log_min_messages = notice		# Log important messages
log_min_error_statement = error		# Log error statements
log_line_prefix = '%m [%p] %q%u@%d '	# Useful prefix for development

#------------------------------------------------------------------------------
# CLIENT CONNECTION DEFAULTS
#------------------------------------------------------------------------------

datestyle = 'iso, mdy'
timezone = 'UTC'			# Use UTC for consistency
default_text_search_config = 'pg_catalog.english'

#------------------------------------------------------------------------------
# LOCK MANAGEMENT
#------------------------------------------------------------------------------

deadlock_timeout = 1s
max_locks_per_transaction = 64

#------------------------------------------------------------------------------
# VERSION COMPATIBILITY
#------------------------------------------------------------------------------

array_nulls = on
standard_conforming_strings = on
