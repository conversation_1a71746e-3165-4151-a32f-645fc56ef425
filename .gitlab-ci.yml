stages:
  - build

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

services:
  - docker:24.0.5-dind

before_script:
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

build-api:
  stage: build
  image: docker:24.0.5
  script:
    - cd api
    - docker build -t $CI_REGISTRY_IMAGE/api:$CI_COMMIT_REF_SLUG-latest .
    - docker push $CI_REGISTRY_IMAGE/api:$CI_COMMIT_REF_SLUG-latest
  only:
    - branches

build-app:
  stage: build
  image: docker:24.0.5
  script:
    - cd app
    - docker build -t $CI_REGISTRY_IMAGE/app:$CI_COMMIT_REF_SLUG-latest .
    - docker push $CI_REGISTRY_IMAGE/app:$CI_COMMIT_REF_SLUG-latest
  only:
    - branches


build-care:
  stage: build
  image: docker:24.0.5
  script:
    - cd care
    - docker build -t $CI_REGISTRY_IMAGE/care:$CI_COMMIT_REF_SLUG-latest .
    - docker push $CI_REGISTRY_IMAGE/care:$CI_COMMIT_REF_SLUG-latest
  only:
    - branches


build-desk:
  stage: build
  image: docker:24.0.5
  script:
    - cd desk
    - docker build -t $CI_REGISTRY_IMAGE/desk:$CI_COMMIT_REF_SLUG-latest .
    - docker push $CI_REGISTRY_IMAGE/desk:$CI_COMMIT_REF_SLUG-latest
  only:
    - branches