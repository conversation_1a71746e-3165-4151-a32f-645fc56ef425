# Node.js / JavaScript / TypeScript
node_modules/
.pnp/
.pnp.js
.npm/
.yarn/
.yarnrc.yml
.pnpm-store/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
*.tsbuildinfo
.eslintcache

# Environment variables and secrets
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local
.envrc
*.pem
*.key
*.crt
*.cert

# Build outputs
/dist/
/dist-ssr/
/build/
/out/
/.next/
/.nuxt/
/.output/
/.vercel/
.cache/
.parcel-cache/
.rollup.cache/
.vite/

# Logs
logs/
*.log

# Testing
/coverage/
.nyc_output/
cypress/videos/
cypress/screenshots/
.jest-cache/

# Python
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.venv/
venv/
ENV/
env/
*.egg-info/
.eggs/
*.egg
.python-version
.pytest_cache/
pytest-report.html
tests/report/

# Docker
.data/
docker-compose.override.yml

# Database files
*.sqlite
*.sqlite3
*.db
*.bak

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Misc
.cache/
.temp/
.tmp/
tmp/
temp/
*.tmp
*.temp

# Project specific
/care/dist/
/care/build/
/desk/dist/
/desk/build/
/api/dist/
/api/build/
.netlify/
/api/.cache/
/api/.tmp/
/care/.vite/
/desk/.vite/

# GitLab
.gitlab/runner-cache/
.gitlab-ci-local/

# Generated files
public/dist/
storybook-static/
.storybook-out/
.docusaurus/
.serverless/
.webpack/
.turbo/


care/public/assets
desk/public/assets

.data
node_modules
app/node_modules
api/node_modules