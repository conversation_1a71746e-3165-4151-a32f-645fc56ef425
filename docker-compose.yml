services:
  # Traefik Reverse Proxy
  continuia_rp:
    image: traefik:v3.0
    container_name: continuia-traefik
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./traefik-dynamic.yml:/etc/traefik/traefik-dynamic.yml:ro
      - ./certs:/etc/traefik/certs:ro
    # No external ports - <PERSON><PERSON><PERSON> handles external SSL termination
    # Internal ports 80 and 443 are available within Docker network
    networks:
      - continuia
      - ui
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # API Backend Service
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: continuia-api
    restart: always
    ports:
      # Expose WebSocket port directly for development (bypassing Traefik)
      - "3002:3001"
    environment:
      - NODE_ENV=${NODE_ENV}
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - MINIO_ROOT_USER=${MINIO_ROOT_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_PORT=${MINIO_PORT}
      - MINIO_USE_SSL=${MINIO_USE_SSL}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-continuia}
      - POSTGRES_HOST=${POSTGRES_HOST:-postgres}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      # Email configuration for Mailpit
      - EMAIL_PROVIDER=smtp
      - SMTP_HOST=mailpit
      - SMTP_PORT=1025
      - SMTP_SECURE=false
      - SMTP_USER=
      - SMTP_PASS=
      - SMTP_FROM_NAME=Continuia Healthcare
      - SMTP_FROM_EMAIL=<EMAIL>
    volumes:
      # Mount source code for live development
      - ./api/src:/app/src
      - ./api/package.json:/app/package.json
      - ./api/package-lock.json:/app/package-lock.json
      - ./api/tsconfig.json:/app/tsconfig.json
      - ./api/drizzle.config.ts:/app/drizzle.config.ts
      - ./api/jest.config.js:/app/jest.config.js
      # Exclude node_modules to avoid conflicts
      - /app/node_modules
    labels:
      - "traefik.enable=true"
      # HTTP router (redirects to HTTPS)
      - "traefik.http.routers.api.rule=Host(`api.continuia.ai`) || Host(`api.continuia.health`)"
      - "traefik.http.routers.api.entrypoints=web"
      - "traefik.http.services.api.loadbalancer.server.port=3001"
      # WebSocket specific configuration
      - "traefik.http.services.api.loadbalancer.sticky=true"
      # HTTPS router with self-signed SSL
      - "traefik.http.routers.api-secure.rule=Host(`api.continuia.ai`) || Host(`api.continuia.health`)"
      - "traefik.http.routers.api-secure.entrypoints=websecure"
      - "traefik.http.routers.api-secure.tls=true"
      - "traefik.http.routers.api-secure.service=api"
      - "traefik.http.routers.api-secure.priority=50"
      # WebSocket specific router for /ws path
      - "traefik.http.routers.api-ws.rule=(Host(`api.continuia.ai`) || Host(`api.continuia.health`)) && PathPrefix(`/ws`)"
      - "traefik.http.routers.api-ws.entrypoints=websecure"
      - "traefik.http.routers.api-ws.tls=true"
      - "traefik.http.routers.api-ws.service=api"
      - "traefik.http.routers.api-ws.priority=100"
      - "traefik.http.routers.api-ws.middlewares=websocket-headers@file"
    depends_on:
      postgres:
        condition: service_healthy
      mailpit:
        condition: service_healthy
    networks:
      - continuia

  # Patient-facing Care App
  care:
    build:
      context: .
      dockerfile: ./care/Dockerfile
    container_name: continuia-care
    restart: always
    environment:
      - NODE_ENV=${NODE_ENV}
      - VITE_API_URL=${VITE_API_URL}
    volumes:
      # Mount source code for live development
      - ./care/src:/app/care/src
      - ./care/public:/app/care/public
      - ./care/index.html:/app/care/index.html
      - ./care/vite.config.ts:/app/care/vite.config.ts
      - ./care/tailwind.config.js:/app/care/tailwind.config.js
      - ./care/postcss.config.js:/app/care/postcss.config.js
      - ./care/tsconfig.json:/app/care/tsconfig.json
      - ./shared:/app/shared
      # Exclude node_modules to avoid conflicts
      - /app/care/node_modules
    labels:
      - "traefik.enable=true"
      # HTTP router (redirects to HTTPS)
      - "traefik.http.routers.care.rule=Host(`care.continuia.ai`) || Host(`care.continuia.health`)"
      - "traefik.http.routers.care.entrypoints=web"
      - "traefik.http.services.care.loadbalancer.server.port=3000"
      # HTTPS router with self-signed SSL
      - "traefik.http.routers.care-secure.rule=Host(`care.continuia.ai`) || Host(`care.continuia.health`)"
      - "traefik.http.routers.care-secure.entrypoints=websecure"
      - "traefik.http.routers.care-secure.tls=true"
      - "traefik.http.routers.care-secure.service=care"
      - "traefik.http.routers.care-secure.middlewares=security-headers@file"
    depends_on:
      - api
    networks:
      - continuia

  # Professional Desk App (Doctor/Admin/Agent)
  desk:
    build:
      context: .
      dockerfile: ./desk/Dockerfile
    container_name: continuia-desk
    restart: always
    environment:
      - NODE_ENV=${NODE_ENV}
      - VITE_API_URL=${VITE_API_URL}
      - VITE_WS_URL=${VITE_WS_URL}
    volumes:
      # Mount source code for live development
      - ./desk/src:/app/desk/src
      - ./desk/public:/app/desk/public
      - ./desk/index.html:/app/desk/index.html
      - ./desk/vite.config.ts:/app/desk/vite.config.ts
      - ./desk/tailwind.config.js:/app/desk/tailwind.config.js
      - ./desk/postcss.config.js:/app/desk/postcss.config.js
      - ./desk/tsconfig.json:/app/desk/tsconfig.json
      - ./shared:/app/shared
      # Exclude node_modules to avoid conflicts
      - /app/desk/node_modules
    labels:
      - "traefik.enable=true"
      # HTTP router (redirects to HTTPS)
      - "traefik.http.routers.desk.rule=Host(`desk.continuia.ai`) || Host(`desk.continuia.health`)"
      - "traefik.http.routers.desk.entrypoints=web"
      - "traefik.http.services.desk.loadbalancer.server.port=3000"
      # HTTPS router with self-signed SSL
      - "traefik.http.routers.desk-secure.rule=Host(`desk.continuia.ai`) || Host(`desk.continuia.health`)"
      - "traefik.http.routers.desk-secure.entrypoints=websecure"
      - "traefik.http.routers.desk-secure.tls=true"
      - "traefik.http.routers.desk-secure.service=desk"
      - "traefik.http.routers.desk-secure.middlewares=security-headers@file"
    depends_on:
      - api
    networks:
      - continuia

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: continuia-postgres
    restart: always
    environment:
      # Parse DATABASE_URL to extract individual components
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-continuia}
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - continuia
    volumes:
      - ./.data/postgres-data:/var/lib/postgresql/data/pgdata
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d continuia"]
      interval: 10s
      timeout: 5s
      retries: 5
      
  # Mailpit Email Testing Service
  mailpit:
    image: axllent/mailpit:latest
    container_name: continuia-mailpit
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mailpit.rule=Host(`mailpit.continuia.ai`) || Host(`mailpit.continuia.health`)"
      - "traefik.http.routers.mailpit.entrypoints=web"
      - "traefik.http.services.mailpit.loadbalancer.server.port=8025"
    networks:
      - continuia

  minio:
    image: minio/minio:RELEASE.2025-07-23T15-54-02Z
    container_name: continuia-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio.rule=Host(`minio.continuia.ai`) || Host(`minio.continuia.health`)"
      - "traefik.http.routers.minio.entrypoints=web"
      - "traefik.http.services.minio.loadbalancer.server.port=9000"
    volumes:
      - ./.data/minio-data:/data
    networks:
      - continuia
    command: server /data

  # Dozzle - Docker Log Viewer
  dozzle:
    image: amir20/dozzle:latest
    container_name: continuia-dozzle
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - continuia
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dozzle.rule=Host(`logs.continuia.ai`) || Host(`logs.continuia.health`)"
      - "traefik.http.routers.dozzle.entrypoints=web"
      - "traefik.http.routers.dozzle-secure.rule=Host(`logs.continuia.ai`) || Host(`logs.continuia.health`)"
      - "traefik.http.routers.dozzle-secure.entrypoints=websecure"
      - "traefik.http.routers.dozzle-secure.tls=true"
      - "traefik.http.routers.dozzle-secure.middlewares=security-headers@file"
      - "traefik.http.services.dozzle.loadbalancer.server.port=8080"
    environment:
      - DOZZLE_NO_ANALYTICS=true
      - DOZZLE_LEVEL=info
      - DOZZLE_TAILSIZE=300

networks:
  ui:
    external: true
  continuia:
    external: true
