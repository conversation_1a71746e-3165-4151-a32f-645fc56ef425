# Traefik Configuration for Continuia Healthcare Platform
api:
  dashboard: true
  insecure: true

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
  file:
    filename: /etc/traefik/traefik-dynamic.yml
    watch: true

# Enable ping for health checks
ping: {}

# Logging
log:
  level: DEBUG

# Using self-signed certificates for internal SSL
# Certificates are provided via dynamic configuration
