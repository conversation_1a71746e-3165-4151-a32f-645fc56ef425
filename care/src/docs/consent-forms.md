# Consent Forms with <PERSON><PERSON> and Versioning

This document explains how to use the consent forms feature in My.Continuia, which supports Markdown editing and versioning capabilities.

## Overview

The consent forms feature allows administrators to create, edit, and manage consent form templates using Markdown. Each template can have multiple versions, providing a complete history of changes over time. Patients can view and consent to the latest versions of forms.

## Database Schema

The consent forms feature uses three main tables:

1. **consent_form_templates** - Stores the base templates for consent forms
2. **consent_form_versions** - Stores different versions of consent form templates with Markdown content
3. **user_consents** - Tracks which users have consented to which form versions

## API Endpoints

### Admin Endpoints

- `GET /api/consent-forms/templates` - Get all consent form templates
- `GET /api/consent-forms/templates/:id` - Get a specific template with its versions
- `POST /api/consent-forms/templates` - Create a new template with initial version
- `PUT /api/consent-forms/templates/:id` - Update a template's metadata
- `POST /api/consent-forms/templates/:id/versions` - Create a new version for a template

### User Endpoints

- `GET /api/consent-forms/versions/:id` - Get a specific version of a consent form
- `POST /api/consent-forms/consent` - Record a user's consent to a form version
- `GET /api/consent-forms/user/:userId/consents` - Get all consents for a specific user

## Admin Workflow

### Creating a Consent Form

1. Navigate to **Admin Panel** > **Consent Forms** in the sidebar
2. Click the **Create New Template** button
3. Fill in the template information:
   - **Title** - Name of the consent form
   - **Description** - Optional description of the form's purpose
4. Edit the content using the Markdown editor:
   - Use the **Edit** tab for writing Markdown
   - Use the **Preview** tab to see how it will look to users
   - Use the **Split View** tab to edit and preview simultaneously
5. Click **Create Template** to save

### Editing a Consent Form

1. Navigate to **Admin Panel** > **Consent Forms** in the sidebar
2. Find the template you want to edit and click the **Edit** button
3. Update the template information and content as needed
4. Add **Version Notes** to describe what changed in this version
5. Click **Save Changes** to create a new version

### Viewing Version History

1. Navigate to **Admin Panel** > **Consent Forms** in the sidebar
2. Find the template you want to view and click the **View** button
3. Use the version selector to switch between different versions
4. View version details including creation date, author, and notes

## Patient Workflow

### Viewing and Consenting to Forms

1. Patients will be directed to consent forms when needed
2. The form is displayed with Markdown rendered as formatted text
3. Patients must check the consent checkbox to indicate agreement
4. After consenting, a record is created with timestamp and metadata

## Markdown Features

The Markdown editor supports standard Markdown syntax:

- **Headers**: Use `#`, `##`, `###` for different heading levels
- **Formatting**: Use `*italic*`, `**bold**`, `~~strikethrough~~`
- **Lists**: Use `- ` or `1. ` for bullet or numbered lists
- **Links**: Use `[text](url)` for hyperlinks
- **Tables**: Use Markdown table syntax
- **Code blocks**: Use triple backticks for code blocks

## Versioning

Each time a consent form is edited, a new version is created instead of overwriting the existing content. This provides:

1. **Audit Trail** - Complete history of all changes
2. **Legal Protection** - Record of exactly what each user consented to
3. **Transparency** - Ability to see how forms have evolved over time

## Security Considerations

- Only administrators can create and edit consent form templates
- Patients can only view active consent forms they need to sign
- All consent actions are recorded with IP address and user agent
- Markdown content is sanitized to prevent XSS attacks

## Best Practices

1. **Clear Titles** - Use descriptive titles that clearly identify the purpose
2. **Version Notes** - Always include detailed notes about what changed in each version
3. **Formatting** - Use Markdown formatting to make forms easy to read
4. **Previewing** - Always preview forms before publishing to ensure proper formatting
5. **Legal Review** - Have legal counsel review consent forms before use
