// API Service Layer for Continuia Platform
// Handles all backend communication with proper error handling and HIPAA compliance

interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

class ApiError extends Error {
  code?: string
  details?: any

  constructor(message: string, code?: string, details?: any) {
    super(message)
    this.name = 'ApiError'
    this.code = code
    this.details = details
  }
}

class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string = (import.meta.env.VITE_API_URL as string) || 'https://api.continuia.ai') {
    this.baseURL = baseURL
    this.token = localStorage.getItem('auth_token')
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    // Refresh token from localStorage in case it was updated
    this.token = localStorage.getItem('auth_token')
    
    // Prepare headers - don't set Content-Type for FormData (browser will set multipart/form-data with boundary)
    const headers: Record<string, string> = {
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
      ...(options.headers as Record<string, string> || {}),
    };
    
    // Only set Content-Type to application/json if body is not FormData
    if (!(options.body instanceof FormData)) {
      headers['Content-Type'] = 'application/json';
    }
    
    const config: RequestInit = {
      headers,
      credentials: 'include', // Include cookies in requests
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      const data = await response.json()

      if (!response.ok) {
  
        throw new ApiError(data.message || 'API request failed', data.code, data)
      }

        return data
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error(`API Error [${endpoint}]:`, error)
      throw error
    }
  }

  setToken(token: string) {
    this.token = token
    localStorage.setItem('auth_token', token)
  }

  clearToken() {
    this.token = null
    localStorage.removeItem('auth_token')
  }

  // Authentication APIs
  async login(email: string, password: string) {
    const response = await this.request<{ user: any; token: string }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
    
    // The response from this.request() already contains the backend data
    // Backend returns: { message, user, token }
    // We need to extract and set the token, then return in expected format
    if (response.token) {
      this.setToken(response.token)
    }
    
    // Return in format expected by frontend: { data: { user, token } }
    return { 
      data: {
        user: response.user,
        token: response.token
      }
    }
  }

  async register(userData: any) {
    const response = await this.request<{ user: any; token: string }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
    
    // The response from this.request() already contains the backend data
    // Backend returns: { message, user, token }
    // We need to extract and set the token, then return in expected format
    if (response.token) {
      this.setToken(response.token)
    }
    
    // Return in format expected by frontend: { data: { user, token } }
    return { 
      data: {
        user: response.user,
        token: response.token
      }
    }
  }

  async logout() {
    try {
      await this.request('/auth/logout', { method: 'POST' })
    } finally {
      this.clearToken()
    }
  }

  async getCurrentUser() {
    const response = await this.request<any>('/auth/me')
    // API returns { message, data: user }, but frontend expects { data: user }
    return { data: response.data }
  }

  // User Profile APIs
  async getUserProfile() {
    const response = await this.request<any>('/profile');
    
    if (response.success !== false) {
      return response;
    }
  }

  async updateUserProfile(data: any) {
    const response = await this.request('/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return response;
  }

  async uploadProfilePhoto(file: File) {
    const formData = new FormData();
    formData.append('photo', file);
    
    const response = await this.request('/storage/profile-photo', {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - let browser set it with boundary for FormData
    });
    return response;
  }

  getProfilePhotoUrl(userId: string): string {
    return `${this.baseURL}/storage/profile-photo/${userId}`;
  }

  // Case Management APIs
  async getCases(params?: { 
    status?: string
    priority?: string
    search?: string
    page?: number
    limit?: number
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/cases${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await this.request<{cases: any[], total: number}>(endpoint)
    return { data: response.cases, total: response.total }
  }

  async getCase(id: string) {
    return this.request<any>(`/cases/${id}`)
  }

  async createCase(caseData: any) {
    return this.request<any>('/cases', {
      method: 'POST',
      body: JSON.stringify(caseData),
    })
  }

  async updateCase(id: string, updates: any) {
    return this.request<any>(`/cases/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    })
  }

  async assignCase(caseId: string, doctorId: string) {
    return this.request<any>(`/cases/${caseId}/assign`, {
      method: 'POST',
      body: JSON.stringify({ doctorId }),
    })
  }

  async acceptCase(caseId: string) {
    return this.request<any>(`/cases/${caseId}/accept`, {
      method: 'POST',
      body: JSON.stringify({}),
    })
  }

  async getDoctorAcceptanceStatus(caseId: string) {
    return this.request<{
      isDoctor: boolean;
      isAssigned: boolean;
      hasAccepted: boolean;
      message: string;
    }>(`/cases/${caseId}/doctor-acceptance`)
  }

  // Structured Case Content APIs
  async getCaseStructuredContent(caseId: string, section?: string) {
    const endpoint = section
      ? `/cases/${caseId}/structured-content/${section}`
      : `/cases/${caseId}/structured-content`;
    const response = await this.request<any>(endpoint);
    
    // Handle the response format - extract content if it's nested
    if (response && typeof response === 'object') {
      if (response.content) {
        return response.content;
      } else if (response.structuredContent) {
        return response.structuredContent;
      } else if (section && response[section]) {
        return response[section];
      }
    }
    
    return response || '';
  }


  async updateCaseStructuredContent(caseId: string, section: string, data: {
    content: string;
    // rawContent field removed
    lastUpdated?: string;
    authorId?: string;
    authorName?: string;
  }) {
    return this.request<any>(`/cases/${caseId}/structured-content/${section}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // Note Types API methods
  async getNoteTypes() {
    return this.request<any>('/note-types');
  }

  async getNoteType(key: string) {
    return this.request<any>(`/note-types/${key}`);
  }

  async createNoteType(noteTypeData: any) {
    return this.request<any>('/note-types', {
      method: 'POST',
      body: JSON.stringify(noteTypeData)
    });
  }

  async updateNoteType(id: string, noteTypeData: any) {
    return this.request<any>(`/note-types/${id}`, {
      method: 'PUT',
      body: JSON.stringify(noteTypeData)
    });
  }

  async deactivateNoteType(id: string) {
    return this.request<any>(`/note-types/${id}`, {
      method: 'DELETE'
    });
  }

  // Case Notes APIs - Enhanced for structured content and multi-doctor support
  async getCaseNotes(caseId: string, options?: {
    noteType?: string;
    authorId?: string;
    caseDoctorId?: string;
  }) {
    // If noteType is specified, use the case-notes API which returns { notes, total }
    if (options?.noteType) {
      const params = new URLSearchParams();
      params.append('type', options.noteType);
      if (options?.authorId) params.append('authorId', options.authorId);
      if (options?.caseDoctorId) params.append('caseDoctorId', options.caseDoctorId);
      
      const queryString = params.toString();
      return this.request<any>(`/case-notes/${caseId}/notes${queryString ? '?' + queryString : ''}`);
    }
    
    // Otherwise use the cases API which returns { caseId, notesCount, clinicalData, lastUpdated }
    return this.request<any>(`/cases/${caseId}/notes`);
  }

  async createCaseNote(noteData: {
    caseId: string;
    noteType: string;
    structuredContent: Record<string, any>;
    // rawContent field removed
    caseDoctorId?: string;
  }) {
    const { caseId, ...notePayload } = noteData;
    return this.request<any>(`/cases/${caseId}/notes`, {
      method: 'POST',
      body: JSON.stringify(notePayload),
    })
  }

  async updateCaseNote(caseId: string, noteId: string, noteData: {
    structuredContent?: Record<string, any>;
    // rawContent field removed
    noteType?: string;
  }) {
    return this.request<any>(`/cases/${caseId}/notes/${noteId}`, {
      method: 'PUT',
      body: JSON.stringify(noteData),
    })
  }

  async deleteCaseNote(noteId: string) {
    return this.request<any>(`/case-notes/${noteId}`, {
      method: 'DELETE',
    })
  }

  async updateCaseNotes(noteId: string, noteData: {
    structuredContent?: Record<string, any>;
    // rawContent field removed
  }) {
    return this.request<any>(`/notes/${noteId}`, {
      method: 'PUT',
      body: JSON.stringify(noteData),
    })
  }

  // Doctor APIs
  async getDoctors(params?: {
    specialization?: string
    availability?: string
    search?: string
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/users/doctors${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any[]>(endpoint)
  }

  async getDoctorCases(doctorId?: string) {
    // For doctors, use the regular getCases endpoint which already filters by doctor role
    // The backend automatically returns only assigned cases for doctors
    if (doctorId) {
      // If a specific doctor ID is provided, this would be for admin/agent use
      return this.request<any[]>(`/doctors/${doctorId}/cases`)
    } else {
      // For the current doctor, directly call the cases endpoint to preserve doctor-specific fields
      // The backend will automatically filter to show only assigned cases and include doctorAcceptanceStatus
      const response = await this.request<{cases: any[], total: number}>('/cases')
      return response.cases || []
    }
  }

  // Medical Opinion APIs
  async createMedicalOpinion(caseId: string, opinion: any) {
    return this.request<any>(`/cases/${caseId}/opinions`, {
      method: 'POST',
      body: JSON.stringify(opinion),
    })
  }

  async getMedicalOpinions(caseId: string) {
    return this.request<any[]>(`/cases/${caseId}/opinions`)
  }

  async updateMedicalOpinion(caseId: string, opinionId: string, updates: any) {
    return this.request<any>(`/cases/${caseId}/opinions/${opinionId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    })
  }

  // Document Management APIs
  async uploadDocument(caseId: string | null, file: File, metadata?: any) {
    const formData = new FormData()
    formData.append('file', file)
    
    // Add metadata fields individually (backend expects them as form fields, not JSON)
    if (metadata) {
      if (metadata.title) formData.append('title', metadata.title)
      if (metadata.description) formData.append('description', metadata.description)
      if (metadata.documentType) formData.append('documentType', metadata.documentType)
    } else {
      // Ensure we always have a title and documentType (required by backend)
      formData.append('title', file.name.split('.')[0] || 'Untitled')
      formData.append('documentType', 'medical_history') // Using valid enum value from backend
    }
    
    // Only include caseId if it's a non-empty string (backend expects a valid UUID)
    if (caseId && caseId.trim() !== '') {
      formData.append('caseId', caseId)
    }

    return this.request<any>('/storage/upload', {
      method: 'POST',
      body: formData,
      // Don't override headers - let request method handle Authorization header
      // Browser will automatically set Content-Type for FormData
    })
  }

  async getDocuments(caseId?: string) {
    // Get all user documents (both standalone and case-attached) from generic endpoint
    // If caseId is provided, filter documents for that specific case with proper permission checks
    const endpoint = caseId ? `/storage/documents?caseId=${encodeURIComponent(caseId)}` : '/storage/documents'
    return this.request<any>(endpoint)
  }

  async getDocumentInfo(documentId: string) {
    // Returns document metadata and secure stream URL
    return this.request<{ document: any }>(`/storage/documents/${documentId}/info`)
  }

  getDocumentStreamUrl(documentId: string) {
    // Returns the secure streaming URL with auth token
    return `${this.baseURL}/storage/documents/${documentId}/stream`
  }

  async streamDocument(documentId: string): Promise<Blob> {
    // Stream document as blob with proper authentication
    const url = `${this.baseURL}/storage/documents/${documentId}/stream`
    
    const headers: Record<string, string> = {
      'Accept': '*/*',
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(url, {
      method: 'GET',
      headers,
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Failed to stream document: ${response.status} ${response.statusText} - ${errorText}`)
    }

    return response.blob()
  }

  async deleteDocument(documentId: string) {
    return this.request<{ success: boolean }>(`/storage/documents/${documentId}`, {
      method: 'DELETE'
    })
  }

  async downloadDocument(documentId: string) {
    const response = await fetch(`${this.baseURL}/documents/${documentId}/download`, {
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
    })
    
    if (!response.ok) {
      throw new Error('Failed to download document')
    }
    
    return response.blob()
  }

  // User Management APIs (Admin only)
  async getUsers(params?: {
    role?: string
    status?: string
    search?: string
    page?: number
    limit?: number
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any[]>(endpoint)
  }

  async updateUserStatus(userId: string, status: string) {
    return this.request<any>(`/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    })
  }

  // Removed: updateUserVerification method - backend endpoint no longer exists
  // This functionality has been removed as the verification status field does not exist in the users table

  async deleteUser(userId: string) {
    return this.request<any>(`/users/${userId}`, {
      method: 'DELETE',
    })
  }

  // User Impersonation APIs (Admin only)
  async impersonateUser(userId: string) {
    // Direct return from request without wrapping in ApiResponse
    const result = await fetch(`${this.baseURL}/users/${userId}/impersonate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
    })
    
    const data = await result.json()
    
    if (!result.ok) {
      throw new ApiError(data.message || 'API request failed', data.code, data)
    }
    
    return data
  }

  async endImpersonation() {
    // Get the original admin token
    const adminToken = localStorage.getItem('admin_token')
    if (!adminToken) {
      throw new Error('No admin token found')
    }
    
    // Restore the admin token
    this.setToken(adminToken)
    
    // Clear impersonation data
    localStorage.removeItem('impersonating')
    localStorage.removeItem('impersonated_user_id')
    localStorage.removeItem('impersonated_user_name')
    localStorage.removeItem('impersonated_user_role')
    localStorage.removeItem('admin_token')
    
    return { success: true }
  }

  isImpersonating() {
    return localStorage.getItem('impersonating') === 'true'
  }

  getImpersonatedUserInfo() {
    if (!this.isImpersonating()) {
      return null
    }
    
    return {
      id: localStorage.getItem('impersonated_user_id'),
      name: localStorage.getItem('impersonated_user_name'),
      role: localStorage.getItem('impersonated_user_role')
    }
  }

  // Appointment APIs
  async getAppointments(params?: {
    status?: string
    date?: string
    doctorId?: string
    patientId?: string
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/appointments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any[]>(endpoint)
  }

  async createAppointment(appointmentData: any) {
    return this.request<any>('/appointments', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    })
  }

  async updateAppointment(appointmentId: string, updates: any) {
    return this.request<any>(`/appointments/${appointmentId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    })
  }

  // Case Discussions APIs
  async getCaseDiscussions(caseId: string) {
    return this.request<any[]>(`/cases/${caseId}/discussions`)
  }

  async sendCaseDiscussion(caseId: string, content: string, isVisibleToPatient: boolean = true) {
    return this.request<any>(`/cases/${caseId}/discussions`, {
      method: 'POST',
      body: JSON.stringify({ content, isVisibleToPatient }),
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  async uploadCaseDiscussionAttachment(caseId: string, content: string, file: File, isVisibleToPatient: boolean = true) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('content', content)
    formData.append('isVisibleToPatient', isVisibleToPatient.toString())
    
    return this.request<any>(`/cases/${caseId}/discussions/upload`, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type for FormData (browser will set multipart/form-data with boundary)
    })
  }

  async updateCaseDiscussion(caseId: string, discussionId: string, content: string) {
    return this.request<any>(`/cases/${caseId}/discussions/${discussionId}`, {
      method: 'PATCH',
      body: JSON.stringify({ content }),
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  async deleteCaseDiscussion(caseId: string, discussionId: string) {
    return this.request<any>(`/cases/${caseId}/discussions/${discussionId}`, {
      method: 'DELETE'
    })
  }
  
  // Legacy Messages APIs (deprecated)
  async getCaseMessages(caseId: string) {
    return this.getCaseDiscussions(caseId);
  }

  async sendMessage(caseId: string, message: string, attachments?: File[]) {
    if (attachments && attachments.length > 0) {
      return this.uploadCaseDiscussionAttachment(caseId, message, attachments[0]);
    } else {
      return this.sendCaseDiscussion(caseId, message);
    }
  }

  // Analytics and Reporting APIs (Admin/Agent)
  async getDashboardStats() {
    return this.request<any>('/dashboard/stats')
  }

  async getReports(type: string, params?: any) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/analytics/reports/${type}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any>(endpoint)
  }

  // Audit Trail APIs (HIPAA Compliance)
  async getAuditLogs(params?: {
    userId?: string
    action?: string
    resource?: string
    startDate?: string
    endDate?: string
    page?: number
    limit?: number
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/audit/logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any[]>(endpoint)
  }

  // Legal Compliance APIs
  async getLegalTemplates() {
    return this.request<any>('/legal-compliance/templates')
  }

  async getLegalTemplate(id: string) {
    return this.request<any>(`/legal-compliance/templates/${id}`)
  }

  async createLegalTemplate(templateData: any) {
    return this.request<any>('/legal-compliance/templates', {
      method: 'POST',
      body: JSON.stringify(templateData),
    })
  }

  async updateLegalTemplate(id: string, templateData: any) {
    return this.request<any>(`/legal-compliance/templates/${id}`, {
      method: 'PUT',
      body: JSON.stringify(templateData),
    })
  }

  async deleteLegalTemplate(id: string) {
    return this.request<any>(`/legal-compliance/templates/${id}`, {
      method: 'DELETE',
    })
  }

  async createLegalTemplateVersion(id: string, versionData: any) {
    return this.request<any>(`/legal-compliance/templates/${id}/versions`, {
      method: 'POST',
      body: JSON.stringify(versionData),
    })
  }

  // Consent Form APIs
  async getConsentFormVersion(id: string) {
    return this.request<any>(`/consent-forms/versions/${id}`)
  }

  async submitConsent(consentData: any) {
    return this.request<any>('/consent-forms/consent', {
      method: 'POST',
      body: JSON.stringify(consentData),
    })
  }

  async deleteConsentTemplate(id: string) {
    return this.request<any>(`/consent-forms/templates/${id}`, {
      method: 'DELETE',
    })
  }

  // CRM APIs
  async getCrmOrganizations(params?: {
    search?: string
    status?: string
    page?: number
    limit?: number
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/crm/organizations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any>(endpoint)
  }

  async getCrmOrganization(id: string) {
    return this.request<any>(`/crm/organizations/${id}`)
  }

  async createCrmOrganization(organizationData: any) {
    return this.request<any>('/crm/organizations', {
      method: 'POST',
      body: JSON.stringify(organizationData),
    })
  }

  async updateCrmOrganization(id: string, organizationData: any) {
    return this.request<any>(`/crm/organizations/${id}`, {
      method: 'PUT',
      body: JSON.stringify(organizationData),
    })
  }

  async deleteCrmOrganization(id: string) {
    return this.request<any>(`/crm/organizations/${id}`, {
      method: 'DELETE',
    })
  }

  async getCrmContacts(params?: {
    organizationId?: string
    search?: string
    status?: string
    page?: number
    limit?: number
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/crm/contacts${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any>(endpoint)
  }

  async getCrmContact(id: string) {
    return this.request<any>(`/crm/contacts/${id}`)
  }

  async createCrmContact(contactData: any) {
    return this.request<any>('/crm/contacts', {
      method: 'POST',
      body: JSON.stringify(contactData),
    })
  }

  async updateCrmContact(id: string, contactData: any) {
    return this.request<any>(`/crm/contacts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(contactData),
    })
  }

  async deleteCrmContact(id: string) {
    return this.request<any>(`/crm/contacts/${id}`, {
      method: 'DELETE',
    })
  }

  async getCrmTouchpoints(params?: {
    contactId?: string
    organizationId?: string
    page?: number
    limit?: number
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/crm/touchpoints${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any>(endpoint)
  }

  async createCrmTouchpoint(touchpointData: any) {
    return this.request<any>('/crm/touchpoints', {
      method: 'POST',
      body: JSON.stringify(touchpointData),
    })
  }

  // Enhanced Appointment APIs
  async getAppointmentDetails(appointmentId: string) {
    return this.request<any>(`/appointments/${appointmentId}`)
  }

  async deleteAppointment(appointmentId: string) {
    return this.request<any>(`/appointments/${appointmentId}`, {
      method: 'DELETE',
    })
  }

  async getCaseAppointments(caseId: string) {
    return this.request<any>(`/cases/${caseId}/appointments`)
  }

  async getUserAppointments(userId: string) {
    return this.request<any>(`/appointments?userId=${userId}`)
  }

  // Doctor Credentials APIs
  async getDoctorCredentials(doctorId?: string) {
    const endpoint = doctorId ? `/doctor/credentials?doctorId=${doctorId}` : '/doctor/credentials'
    return this.request<any>(endpoint)
  }

  async createDoctorCredential(credentialData: {
    credentialType: string
    credentialNumber: string
    issuingAuthority: string
    issuedDate: string
    expirationDate?: string
    notes?: string
    metadata?: string
    doctorId?: string
  }) {
    return this.request<any>('/doctor/credentials', {
      method: 'POST',
      body: JSON.stringify(credentialData),
    })
  }

  async updateDoctorCredential(credentialId: string, updates: {
    credentialType?: string
    credentialNumber?: string
    issuingAuthority?: string
    issuedDate?: string
    expirationDate?: string
    notes?: string
    metadata?: string
  }) {
    return this.request<any>(`/doctor/credentials/${credentialId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    })
  }

  async uploadCredentialDocument(credentialId: string, file: File, metadata: {
    title: string
    description?: string
  }) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('title', metadata.title)
    if (metadata.description) {
      formData.append('description', metadata.description)
    }

    return this.request<any>(`/doctor/credentials/${credentialId}/documents`, {
      method: 'POST',
      body: formData,
    })
  }

  async getCredentialDocuments(credentialId: string) {
    return this.request<any>(`/doctor/credentials/${credentialId}/documents`)
  }

  getCredentialDocumentStreamUrl(credentialId: string, documentId: string) {
    // Returns the secure streaming URL for credential documents with auth token
    return `${this.baseURL}/doctor/credentials/${credentialId}/documents/${documentId}/stream`
  }

  // Doctor Profile URLs APIs
  async getDoctorProfileUrls(doctorId?: string) {
    const endpoint = doctorId ? `/doctor/profile-urls?doctorId=${doctorId}` : '/doctor/profile-urls'
    return this.request<any>(endpoint)
  }

  async createDoctorProfileUrl(urlData: {
    urlType: string
    url: string
    displayName?: string
    sortOrder?: number
    doctorId?: string
  }) {
    return this.request<any>('/doctor/profile-urls', {
      method: 'POST',
      body: JSON.stringify(urlData),
    })
  }

  async updateDoctorProfileUrl(urlId: string, updates: {
    urlType?: string
    url?: string
    displayName?: string
    sortOrder?: number
  }) {
    return this.request<any>(`/doctor/profile-urls/${urlId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    })
  }

  async deleteDoctorProfileUrl(urlId: string) {
    return this.request<any>(`/doctor/profile-urls/${urlId}`, {
      method: 'DELETE',
    })
  }

  // Admin Credential Management APIs
  async getAdminCredentials(params?: {
    status?: string
    credentialType?: string
    search?: string
    page?: number
    limit?: number
  }) {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    
    const endpoint = `/admin/credentials${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<any>(endpoint)
  }

  async verifyCredential(credentialId: string, notes?: string) {
    return this.request<any>(`/admin/credentials/${credentialId}/verify`, {
      method: 'PUT',
      body: JSON.stringify({ notes }),
    })
  }

  async rejectCredential(credentialId: string, rejectionReason: string) {
    return this.request<any>(`/admin/credentials/${credentialId}/reject`, {
      method: 'PUT',
      body: JSON.stringify({ rejectionReason }),
    })
  }

  // Unified Notes APIs - Simple approach for all note types
  async getUnifiedNote(caseId: string, noteType: string) {
    return this.request<any>(`/unified-notes/${caseId}/notes/${noteType}`)
  }

  async saveUnifiedNote(caseId: string, noteType: string, content: string) {
    return this.request<any>(`/unified-notes/${caseId}/notes/${noteType}`, {
      method: 'POST',
      body: JSON.stringify({ content }),
    })
  }

  async getAllUnifiedNotes(caseId: string, noteType?: string) {
    const endpoint = noteType
      ? `/unified-notes/${caseId}/notes?noteType=${noteType}`
      : `/unified-notes/${caseId}/notes`;
    return this.request<any>(endpoint)
  }

  // Get all notes for a case in bulk (optimized single API call)
  async getAllNotesBulk(caseId: string) {
    return this.request<{
      caseId: string;
      notesByType: {[noteType: string]: any};
      availableNoteTypes: string[];
      total: number;
    }>(`/unified-notes/${caseId}/notes-bulk`)
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient()

// Export the class for testing or custom instances
export { ApiClient }

// Export types for use in components
export type { ApiResponse, ApiError }
