/* Import fonts first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  
  body {
    @apply bg-gradient-to-br from-primary-50 via-white to-primary-100 text-gray-900;
    font-family: 'Inter', sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    min-height: 100vh;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary-600 ring-offset-2;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary-200 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-300;
  }
}

@layer components {
  /* Button variants - updated to match Continuia website */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium px-6 py-3 rounded-full transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-white hover:bg-primary-50 text-primary-600 font-medium px-6 py-3 rounded-full border-2 border-primary-200 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-md hover:shadow-lg hover:border-primary-300;
  }

  .btn-outline {
    @apply bg-transparent hover:bg-primary-50 text-primary-600 font-medium px-6 py-3 rounded-full border-2 border-primary-500 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 hover:bg-primary-500 hover:text-white;
  }

  /* Card styles - updated to match Continuia website */
  .card {
    @apply bg-white rounded-2xl shadow-lg border border-primary-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1;
  }

  .card-header {
    @apply px-6 py-5 border-b border-primary-100;
  }

  .card-body {
    @apply px-6 py-6;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-primary-100 bg-primary-50 rounded-b-2xl;
  }
  
  /* Feature cards with colored backgrounds */
  .feature-card {
    @apply rounded-2xl p-6 transition-all duration-300 hover:shadow-md;
  }
  
  .feature-card-purple {
    @apply feature-card bg-background-feature-purple;
  }
  
  .feature-card-pink {
    @apply feature-card bg-background-feature-pink;
  }
  
  .feature-card-blue {
    @apply feature-card bg-background-feature-blue;
  }
  
  .feature-card-green {
    @apply feature-card bg-background-feature-green;
  }
  
  .feature-card-orange {
    @apply feature-card bg-background-feature-orange;
  }

  /* Form styles - updated to match Continuia website */
  .form-input {
    @apply w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1.5;
  }

  .form-error {
    @apply text-sm text-error-600 mt-1;
  }

  .form-help {
    @apply text-sm text-gray-500 mt-1;
  }

  /* Status badges - updated to match Continuia website */
  .badge {
    @apply inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-accent {
    @apply badge bg-accent-100 text-accent-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply bg-error-100 text-error-800;
  }

  .badge-info {
    @apply bg-primary-100 text-primary-800;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Healthcare specific styles */
  .medical-card {
    @apply card border-l-4 border-l-primary-500;
  }

  .urgent-indicator {
    @apply bg-error-500 text-white px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide;
  }

  .priority-high {
    @apply border-l-error-500;
  }

  .priority-medium {
    @apply border-l-warning-500;
  }

  .priority-low {
    @apply border-l-success-500;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  .animate-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  /* Healthcare specific utilities */
  .hipaa-secure {
    @apply border-2 border-dashed border-primary-300 bg-primary-50;
  }

  .audit-trail {
    @apply text-xs text-gray-500 font-mono;
  }
}
