import { Routes, Route, Navigate } from 'react-router-dom'

// Import components
import { AuthProvider } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Layout } from '@/components/layout/Layout'

// Auth Pages
import { LoginPage } from '@/pages/auth/LoginPage'
import { RegisterPage } from '@/pages/auth/RegisterPage'

// Dashboard Pages
import { DashboardPage } from '@/pages/DashboardPage'

// Patient Pages
import { NewCase } from '@/pages/patient/NewCase'
import { CaseDetail } from '@/components/shared/CaseDetail'
import { UserProfile } from '@/pages/patient/UserProfile'
import PatientConsentForm from '@/pages/patient/ConsentForm'
import { Appointments } from '@/components/shared/Appointments'
import { AppointmentDetails } from '@/components/shared/AppointmentDetails'
import { AuditLogs } from '@/components/shared/AuditLogs'

// Document Pages
import Documents from '@/pages/Documents'
import { UploadDocuments } from '@/pages/UploadDocuments'

// Shared Components
import { Cases } from '@/components/shared/Cases'

// Utility Pages
import { NotFoundPage } from '@/pages/NotFoundPage'

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-background">
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          
          {/* Protected Dashboard */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout>
                  <DashboardPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Patient Cases Routes */}
          <Route
            path="/cases"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <Cases />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/cases/:id"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <CaseDetail />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/patient/cases/new"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <NewCase />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Patient Appointments */}
          <Route
            path="/appointments"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <Appointments 
                    userRole="patient"
                    title="My Appointments"
                    description="View and manage your upcoming and past appointments"
                  />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/appointments/:appointmentId"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <AppointmentDetails userRole="patient" showActions={false} />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Patient Activity */}
          <Route
            path="/patient/activity"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <AuditLogs 
                    title="My Activity Log"
                    description="View your account activity and changes"
                  />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Profile */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <UserProfile />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Consent Form Routes */}
          <Route
            path="/patient/consent/:id"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <PatientConsentForm />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Document Routes */}
          <Route
            path="/documents"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <Documents />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/documents/upload"
            element={
              <ProtectedRoute requiredRole="patient">
                <Layout>
                  <UploadDocuments />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Fallback routes */}
          <Route path="/404" element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </div>
    </AuthProvider>
  )
}

export default App
