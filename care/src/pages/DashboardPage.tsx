import { useAuth } from '@/hooks/useAuth'
import { PatientDashboard } from '@/pages/patient/PatientDashboard'

export function DashboardPage() {
  const { user } = useAuth()

  // Care app is only for patients
  if (user?.role === 'patient') {
    return <PatientDashboard />
  }

  // If non-patient user somehow accesses care app, show message
  return (
    <div className="text-center py-12">
      <h3 className="text-lg font-medium text-gray-900">Access Restricted</h3>
      <p className="mt-2 text-sm text-gray-500">
        This application is for patients only. Please use the appropriate portal for your role.
      </p>
    </div>
  )
}
