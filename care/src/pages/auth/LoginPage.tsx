import React, { useState } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import logoImage from '../../assets/images/logo-400x400.png';
import { 
  Stethoscope, 
  FileText, 
  Clock, 
  MessageCircle, 
  Shield, 
  Users,
  CheckCircle,
  Heart
} from 'lucide-react'

export function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const { login } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const from = location.state?.from?.pathname || '/'

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      await login(email, password)
      navigate(from, { replace: true })
    } catch (error) {
      setError('Invalid email or password')
    } finally {
      setIsLoading(false)
    }
  }

  const handleQuickLogin = async (userType: string) => {
    setIsLoading(true)
    setError('')
    const userEmail = `${userType.toLowerCase()}@continuia.ai`
    const userPassword = 'Continuia'

    try {
      await login(userEmail, userPassword)
      navigate(from, { replace: true })
    } catch (error) {
      setError(`Failed to login as ${userType}`)
    } finally {
      setIsLoading(false)
    }
  }

  const features = [
    {
      icon: Stethoscope,
      title: "Share Your Story",
      description: "Begin by sharing your medical journey with us. Our secure, HIPAA-compliant platform makes it easy to upload your medical records.",
      color: "bg-primary-50 text-primary-600"
    },
    {
      icon: FileText,
      title: "Expert Analysis",
      description: "Your case is carefully matched with board-certified specialists who have deep expertise in your specific condition.",
      color: "bg-primary-50 text-primary-600"
    },
    {
      icon: Clock,
      title: "Continuia Insights",
      description: "Receive a comprehensive report written in clear, understandable language with actionable recommendations.",
      color: "bg-primary-50 text-primary-600"
    },
    {
      icon: MessageCircle,
      title: "72hrs Average Delivery",
      description: "Get your expert medical opinion delivered quickly so you can make informed decisions about your health.",
      color: "bg-primary-50 text-primary-600"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100">
      {/* Header with Logo */}
      <header className="relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center">
            <img
              src="/logo-horizontal.png"
              alt="Continuia Logo"
              className="h-12 w-auto"
            />
          </div>
        </div>
      </header>

      {/* Main Hero Section - Matching Website Layout */}
      <main className="relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
            
            {/* Left Side - Hero Content (matches website) */}
            <div className="space-y-8">
              {/* Emoji + Customer Count */}
              <div className="flex items-center space-x-2 text-gray-600">
                <span className="text-2xl">🎉</span>
                <span className="font-medium">15,000+ Happy Customers</span>
              </div>

              {/* Main Headline */}
              <div>
                <h1 className="text-6xl font-bold text-gray-900 leading-tight mb-6">
                  When<br />
                  Medical<br />
                  <span className="text-primary-500">Decisions</span><br />
                  Matter<br />
                  Most
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                  Connect with world-class specialists who provide expert second opinions,
                  ensuring you make informed decisions about your health journey.
                </p>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-primary-500 hover:bg-primary-600 text-white font-semibold px-8 py-4 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center justify-center">
                  <span className="mr-2">✚</span>
                  Get Expert Opinion
                </button>
                <button className="bg-white hover:bg-primary-50 text-primary-600 font-semibold px-8 py-4 rounded-full border-2 border-primary-200 hover:border-primary-300 transition-all duration-200 shadow-md hover:shadow-lg">
                  Learn How It Works
                </button>
              </div>

              {/* Trust Indicators */}
              <div className="flex items-center space-x-8 text-gray-600">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="font-medium">Board-certified specialists</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span className="font-medium">AI-enhanced analysis</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span className="font-medium">Global expertise</span>
                </div>
              </div>
            </div>

            {/* Right Side - Login Form (replaces website image) */}
            <div className="flex justify-center lg:justify-end">
              <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-md border border-primary-100">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-3">Welcome Back</h2>
                  <p className="text-lg text-gray-600">Sign in to access your medical consultations</p>
                </div>

                <form className="space-y-6" onSubmit={handleSubmit}>
                  {error && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm">
                      {error}
                    </div>
                  )}
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="email" className="block text-base font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        className="w-full px-4 py-4 text-base border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                      />
                    </div>
                    <div>
                      <label htmlFor="password" className="block text-base font-medium text-gray-700 mb-2">
                        Password
                      </label>
                      <input
                        id="password"
                        name="password"
                        type="password"
                        autoComplete="current-password"
                        required
                        className="w-full px-4 py-4 text-base border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                        placeholder="Enter your password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-primary-500 hover:bg-primary-600 text-white font-semibold py-4 px-4 text-lg rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    {isLoading ? 'Signing in...' : 'Get Expert Opinion'}
                  </button>
                  
                  {/* Quick Login Options */}
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300" />
                    </div>
                    <div className="relative flex justify-center text-base">
                      <span className="px-2 bg-white text-gray-500">Quick Access</span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3">
                    <button
                      type="button"
                      onClick={() => handleQuickLogin('Patient')}
                      disabled={isLoading}
                      className="px-4 py-3 bg-primary-50 text-primary-700 hover:bg-primary-100 rounded-full transition-all duration-200 font-medium text-base shadow-md hover:shadow-lg"
                    >
                      Patient
                    </button>
                  </div>

                  <div className="text-center">
                    <p className="text-base text-gray-600">
                      New to Continuia?{' '}
                      <Link
                        to="/register"
                        className="font-medium text-primary-600 hover:text-primary-500 transition-colors"
                      >
                        Create an account
                      </Link>
                    </p>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
