import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  ArrowLeft,
  Upload,
  X,
  FileText,
  Image,
  Save,
  Send,
  Plus,
  Trash2
} from 'lucide-react'

interface CaseFormData {
  title: string
  description: string
  symptoms: string
  medicalHistory: string
  currentMedications: string[]
  allergies: string[]
  priority: 'low' | 'medium' | 'high' | 'urgent'
  specialization: string
  documents: File[]
}

const specializations = [
  'Cardiology',
  'Dermatology',
  'Endocrinology',
  'Gastroenterology',
  'Neurology',
  'Oncology',
  'Orthopedics',
  'Pediatrics',
  'Psychiatry',
  'Pulmonology',
  'Radiology',
  'Rheumatology',
  'Urology',
  'General Medicine',
  'Other'
]

export function NewCase() {
  const { user: _user } = useAuth()
  const navigate = useNavigate()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<CaseFormData>({
    title: '',
    description: '',
    symptoms: '',
    medicalHistory: '',
    currentMedications: [''],
    allergies: [''],
    priority: 'medium',
    specialization: '',
    documents: []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: keyof CaseFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleArrayFieldChange = (field: 'currentMedications' | 'allergies', index: number, value: string) => {
    const newArray = [...formData[field]]
    newArray[index] = value
    setFormData(prev => ({ ...prev, [field]: newArray }))
  }

  const addArrayField = (field: 'currentMedications' | 'allergies') => {
    setFormData(prev => ({ ...prev, [field]: [...prev[field], ''] }))
  }

  const removeArrayField = (field: 'currentMedications' | 'allergies', index: number) => {
    if (formData[field].length > 1) {
      const newArray = formData[field].filter((_, i) => i !== index)
      setFormData(prev => ({ ...prev, [field]: newArray }))
    }
  }

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const newFiles = Array.from(files).filter(file => {
        // Validate file type and size
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']
        const maxSize = 10 * 1024 * 1024 // 10MB
        return allowedTypes.includes(file.type) && file.size <= maxSize
      })
      setFormData(prev => ({ ...prev, documents: [...prev.documents, ...newFiles] }))
    }
  }

  const removeDocument = (index: number) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.filter((_, i) => i !== index)
    }))
  }

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}

    if (step === 1) {
      if (!formData.title.trim()) newErrors.title = 'Case title is required'
      if (!formData.description.trim()) {
        newErrors.description = 'Case description is required'
      } else if (formData.description.trim().length < 10) {
        newErrors.description = 'Description must be at least 10 characters'
      }
      if (!formData.specialization) newErrors.specialization = 'Please select a specialization'
    }

    if (step === 2) {
      if (!formData.symptoms.trim()) newErrors.symptoms = 'Please describe your symptoms'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handleBack = () => {
    setCurrentStep(prev => prev - 1)
  }

  const handleSaveDraft = async () => {
    setIsSubmitting(true)
    try {
      // Prepare case data for API - allow incomplete data for drafts
      const caseData = {
        title: formData.title,
        description: formData.description,
        symptoms: formData.symptoms,
        medicalHistory: formData.medicalHistory,
        currentMedications: formData.currentMedications.filter(med => med.trim()).join(', '),
        urgencyLevel: formData.priority,
        specialtyRequired: formData.specialization,
        isDraft: true, // Flag to indicate this is a draft with relaxed validation
      }

      // const _response = await apiClient.createCase(caseData)
      await apiClient.createCase(caseData)
      
      // Navigate to cases list
      navigate('/patient/cases')
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error saving draft:', error)
      // You might want to show a toast notification here
      alert('Failed to save draft. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return

    setIsSubmitting(true)
    let caseId = null;
    let uploadErrors = false;
    
    try {
      // Prepare case data for API
      const caseData = {
        title: formData.title,
        description: formData.description,
        symptoms: formData.symptoms,
        medicalHistory: formData.medicalHistory,
        currentMedications: formData.currentMedications.filter(med => med.trim()).join(', '),
        urgencyLevel: formData.priority,
        specialtyRequired: formData.specialization,
      }

      const response = await apiClient.createCase(caseData)
      
      // Store the case ID for navigation if document uploads fail
      if (response.data?.case?.id) {
        caseId = response.data.case.id;
      }
      
      // If we have documents to upload, upload them after case creation
      if (formData.documents.length > 0 && caseId) {
        const uploadPromises: Promise<any>[] = [];
        const failedUploads: string[] = [];
        
        // First, create an array of upload promises
        for (const document of formData.documents) {
          const uploadPromise = apiClient.uploadDocument(caseId, document)
            .catch(docError => {
              // TODO: Replace with proper error reporting
  console.error('Error uploading document:', docError);
              failedUploads.push(document.name);
              return null; // Return null for failed uploads
            });
          
          uploadPromises.push(uploadPromise);
        }
        
        // Wait for all uploads to complete (success or failure)
        await Promise.all(uploadPromises);
        
        // If any uploads failed, show a warning but still navigate
        if (failedUploads.length > 0) {
          uploadErrors = true;
          console.warn(`${failedUploads.length} documents failed to upload:`, failedUploads);
          // Show alert with specific file names that failed
          alert(`Case created successfully, but ${failedUploads.length} document(s) failed to upload: ${failedUploads.join(', ')}. \n\nYou can try uploading them again from the case details page.`);
        }
      }
      
      // Navigate to the specific case detail page instead of the list
      if (caseId) {
        navigate(`/cases/${caseId}`);
      } else {
        // Fallback to cases list if we don't have a case ID
        navigate('/cases');
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error submitting case:', error);
      
      if (caseId && uploadErrors) {
        // Case was created but document uploads failed
        alert(`Your case was created successfully, but there was an issue uploading documents. You can try uploading them again from the case details page.`);
        navigate(`/cases/${caseId}`);
      } else {
        // Case creation failed
        alert('Failed to submit case. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700">
          Case Title *
        </label>
        <input
          type="text"
          id="title"
          value={formData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
            errors.title ? 'border-red-300' : ''
          }`}
          placeholder="Brief title for your case"
        />
        {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Case Description *
        </label>
        <textarea
          id="description"
          rows={4}
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
            errors.description ? 'border-red-300' : ''
          }`}
          placeholder="Provide a detailed description of your medical concern and what you're seeking a second opinion about..."
        />
        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
      </div>

      <div>
        <label htmlFor="specialization" className="block text-sm font-medium text-gray-700">
          Medical Specialization *
        </label>
        <select
          id="specialization"
          value={formData.specialization}
          onChange={(e) => handleInputChange('specialization', e.target.value)}
          className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
            errors.specialization ? 'border-red-300' : ''
          }`}
        >
          <option value="">Select a specialization</option>
          {specializations.map(spec => (
            <option key={spec} value={spec}>{spec}</option>
          ))}
        </select>
        {errors.specialization && <p className="mt-1 text-sm text-red-600">{errors.specialization}</p>}
      </div>

      <div>
        <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
          Priority Level
        </label>
        <select
          id="priority"
          value={formData.priority}
          onChange={(e) => handleInputChange('priority', e.target.value as any)}
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="low">Low - Routine consultation</option>
          <option value="medium">Medium - Standard case</option>
          <option value="high">High - Important case</option>
          <option value="urgent">Urgent - Time-sensitive</option>
        </select>
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <label htmlFor="symptoms" className="block text-sm font-medium text-gray-700">
          Current Symptoms *
        </label>
        <textarea
          id="symptoms"
          rows={4}
          value={formData.symptoms}
          onChange={(e) => handleInputChange('symptoms', e.target.value)}
          className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
            errors.symptoms ? 'border-red-300' : ''
          }`}
          placeholder="Describe your current symptoms"
        />
        {errors.symptoms && <p className="mt-1 text-sm text-red-600">{errors.symptoms}</p>}
      </div>

      <div>
        <label htmlFor="medicalHistory" className="block text-sm font-medium text-gray-700">
          Relevant Medical History
        </label>
        <textarea
          id="medicalHistory"
          rows={4}
          value={formData.medicalHistory}
          onChange={(e) => handleInputChange('medicalHistory', e.target.value)}
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
          placeholder="Previous medical history"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Current Medications
        </label>
        {formData.currentMedications.map((medication, index) => (
          <div key={index} className="flex items-center space-x-2 mb-2">
            <input
              type="text"
              value={medication}
              onChange={(e) => handleArrayFieldChange('currentMedications', index, e.target.value)}
              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              placeholder="Medication name and dosage"
            />
            {formData.currentMedications.length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayField('currentMedications', index)}
                className="p-2 text-red-500 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayField('currentMedications')}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Medication
        </button>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Allergies
        </label>
        {formData.allergies.map((allergy, index) => (
          <div key={index} className="flex items-center space-x-2 mb-2">
            <input
              type="text"
              value={allergy}
              onChange={(e) => handleArrayFieldChange('allergies', index, e.target.value)}
              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              placeholder="Allergy or adverse reaction"
            />
            {formData.allergies.length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayField('allergies', index)}
                className="p-2 text-red-500 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayField('allergies')}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Allergy
        </button>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Upload Medical Documents
        </label>
        <p className="text-sm text-gray-500 mb-4">
          Upload relevant medical records, test results, images, or reports (PDF, JPG, PNG - max 10MB each)
        </p>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
          <input
            type="file"
            multiple
            accept=".pdf,.jpg,.jpeg,.png"
            onChange={(e) => handleFileUpload(e.target.files)}
            className="hidden"
            id="file-upload"
          />
          <label htmlFor="file-upload" className="cursor-pointer">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <span className="mt-2 block text-sm font-medium text-gray-900">
              Click to upload files
            </span>
            <span className="mt-1 block text-sm text-gray-500">
              or drag and drop
            </span>
          </label>
        </div>

        {formData.documents.length > 0 && (
          <div className="mt-4 space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Uploaded Files:</h4>
            {formData.documents.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div className="flex items-center space-x-3">
                  {file.type.startsWith('image/') ? (
                    <Image className="h-5 w-5 text-gray-400" />
                  ) : (
                    <FileText className="h-5 w-5 text-gray-400" />
                  )}
                  <span className="text-sm text-gray-900">{file.name}</span>
                  <span className="text-xs text-gray-500">
                    ({(file.size / 1024 / 1024).toFixed(1)} MB)
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => removeDocument(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )

  const steps = [
    { id: 1, name: 'Basic Information', description: 'Case details and specialization' },
    { id: 2, name: 'Medical Information', description: 'Symptoms and medical history' },
    { id: 3, name: 'Documents', description: 'Upload supporting documents' }
  ]

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/patient/cases')}
          className="p-2 text-gray-400 hover:text-gray-500 rounded-md"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create New Case</h1>
          <p className="text-sm text-gray-500">Get a second medical opinion from our specialists</p>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white shadow rounded-lg p-6">
        <nav aria-label="Progress">
          <ol className="flex items-center">
            {steps.map((step, stepIdx) => (
              <li key={step.id} className={`${stepIdx !== steps.length - 1 ? 'flex-1' : ''}`}>
                <div className="flex items-center">
                  <div className="relative flex items-center justify-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        step.id < currentStep
                          ? 'bg-primary-600 text-white'
                          : step.id === currentStep
                          ? 'bg-primary-100 text-primary-600 border-2 border-primary-600'
                          : 'bg-gray-100 text-gray-400'
                      }`}
                    >
                      {step.id}
                    </div>
                  </div>
                  <div className="ml-3 min-w-0">
                    <p className={`text-sm font-medium ${
                      step.id <= currentStep ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {step.name}
                    </p>
                    <p className="text-xs text-gray-500">{step.description}</p>
                  </div>
                  {stepIdx !== steps.length - 1 && (
                    <div className="flex-1 ml-6">
                      <div className={`h-0.5 ${
                        step.id < currentStep ? 'bg-primary-600' : 'bg-gray-200'
                      }`} />
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ol>
        </nav>
      </div>

      {/* Form Content */}
      <div className="bg-white shadow rounded-lg p-6">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
      </div>

      {/* Actions */}
      <div className="flex justify-between">
        <div>
          {currentStep > 1 && (
            <button
              type="button"
              onClick={handleBack}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </button>
          )}
        </div>
        
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={handleSaveDraft}
            disabled={isSubmitting}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </button>
          
          {currentStep < 3 ? (
            <button
              type="button"
              onClick={handleNext}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700"
            >
              Next
            </button>
          ) : (
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Submit Case
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
