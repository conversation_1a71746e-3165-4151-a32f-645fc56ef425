import { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  ArrowLeft,
  User,
  Calendar,
  FileText,
  Activity,
  Shield,
  AlertCircle,
  Clock,
  CheckCircle,
  Upload,
  Eye,
  Plus
} from 'lucide-react'
import {
  backgroundColors,
  buttonColors,
  textColors,
  typography,
  shadows,
  layouts,
  forms,
  spacing,
  sizes,
  animations,
  commonClasses
} from '@/constants/theme'
import { PatientDocuments } from '@/components/PatientDocuments'
import { InlineCaseDocumentViewer } from '@/components/InlineCaseDocumentViewer'

interface PatientCase {
  id: string
  patientId: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: string
  updatedAt: string
  assignedDoctorId?: string
  patientName?: string
  patientAge?: number
  patientGender?: string
}

export function PatientCaseDetail() {
  const { caseId } = useParams<{ caseId: string }>()
  const navigate = useNavigate()
  const { user } = useAuth()
  const [patientCase, setPatientCase] = useState<PatientCase | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'documents' | 'notes' | 'timeline'>('overview')
  const [selectedDocument, setSelectedDocument] = useState<any>(null)

  useEffect(() => {
    if (caseId) {
      loadPatientCase()
    }
  }, [caseId])

  const loadPatientCase = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getCase(caseId!)
      setPatientCase(response)
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load patient case:', error)
      setError('Failed to load patient case. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800'
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      case 'closed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open':
        return <AlertCircle className="h-4 w-4" />
      case 'in_progress':
        return <Clock className="h-4 w-4" />
      case 'resolved':
        return <CheckCircle className="h-4 w-4" />
      case 'closed':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            <div className={commonClasses.loading}>
              <div className={commonClasses.loadingSpinner}></div>
              <span className={`ml-4 ${typography.bodyLarge} ${textColors.secondary}`}>✨ Loading patient case...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !patientCase) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            <div className={commonClasses.errorAlert}>
              <div className="flex items-start space-x-4">
                <AlertCircle className={`${sizes.iconLarge} ${textColors.error} mt-1 flex-shrink-0`} />
                <div>
                  <h3 className={`${typography.h5} ${textColors.error} mb-2`}>⚠️ Something went wrong</h3>
                  <p className={`${typography.body} ${textColors.error}`}>{error || 'Patient case not found'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={layouts.page}>
      <div className={layouts.container}>
        <div className={layouts.pageContent}>
          {/* Header */}
          <div className={commonClasses.card}>
            <div className={spacing.cardPadding}>
              <div className="flex items-center justify-between mb-6">
                <button
                  onClick={() => navigate('/patient/cases')}
                  className={`${commonClasses.secondaryButton} ${animations.transitionAll}`}
                >
                  <ArrowLeft className={`${sizes.iconSmall} mr-2`} />
                  Back to Cases
                </button>
                
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-700">HIPAA Compliant</span>
                </div>
              </div>

              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h1 className={`${typography.h1} mb-4`}>
                    🏥 {patientCase.title}
                  </h1>
                  
                  <div className="flex items-center space-x-6 mb-6">
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(patientCase.status)}`}>
                        {getStatusIcon(patientCase.status)}
                        <span className="ml-1 capitalize">{patientCase.status.replace('_', ' ')}</span>
                      </span>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(patientCase.priority)}`}>
                        {patientCase.priority.toUpperCase()} Priority
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="flex items-center space-x-3">
                      <User className={`${sizes.iconMedium} text-primary-600`} />
                      <div>
                        <p className={`${typography.caption} ${textColors.muted}`}>Patient</p>
                        <p className={`${typography.body} font-medium`}>
                          {patientCase.patientName || `Patient ${patientCase.patientId}`}
                        </p>
                        {patientCase.patientAge && patientCase.patientGender && (
                          <p className={`${typography.caption} ${textColors.muted}`}>
                            {patientCase.patientAge} years, {patientCase.patientGender}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Calendar className={`${sizes.iconMedium} text-primary-600`} />
                      <div>
                        <p className={`${typography.caption} ${textColors.muted}`}>Created</p>
                        <p className={`${typography.body} font-medium`}>
                          {new Date(patientCase.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Activity className={`${sizes.iconMedium} text-primary-600`} />
                      <div>
                        <p className={`${typography.caption} ${textColors.muted}`}>Last Updated</p>
                        <p className={`${typography.body} font-medium`}>
                          {new Date(patientCase.updatedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  {patientCase.description && (
                    <div className="mt-6 p-4 bg-primary-50 rounded-xl">
                      <p className={`${typography.body} ${textColors.secondary}`}>
                        {patientCase.description}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className={commonClasses.card}>
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'overview', label: '📋 Overview', icon: FileText },
                  { id: 'documents', label: '📄 Documents', icon: FileText },
                  { id: 'notes', label: '📝 Clinical Notes', icon: FileText },
                  { id: 'timeline', label: '⏰ Timeline', icon: Activity }
                ].map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`py-4 px-2 border-b-2 font-medium text-sm ${
                        activeTab === tab.id
                          ? 'border-primary-500 text-primary-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      } transition-colors duration-200`}
                    >
                      <div className="flex items-center space-x-2">
                        <Icon className="h-4 w-4" />
                        <span>{tab.label}</span>
                      </div>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Case Summary and Patient Info */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className={commonClasses.card}>
                  <div className={spacing.cardPadding}>
                    <h3 className={`${typography.h3} mb-4`}>📊 Case Summary</h3>
                    <div className="space-y-4">
                      <div>
                        <p className={`${typography.label} mb-2`}>Case ID</p>
                        <p className={`${typography.body} font-mono`}>{patientCase.id}</p>
                      </div>
                      <div>
                        <p className={`${typography.label} mb-2`}>Patient ID</p>
                        <p className={`${typography.body} font-mono`}>{patientCase.patientId}</p>
                      </div>
                      <div>
                        <p className={`${typography.label} mb-2`}>Status</p>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(patientCase.status)}`}>
                          {getStatusIcon(patientCase.status)}
                          <span className="ml-1 capitalize">{patientCase.status.replace('_', ' ')}</span>
                        </span>
                      </div>
                      <div>
                        <p className={`${typography.label} mb-2`}>Priority</p>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(patientCase.priority)}`}>
                          {patientCase.priority.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={commonClasses.card}>
                  <div className={spacing.cardPadding}>
                    <h3 className={`${typography.h3} mb-4`}>🏥 Patient Information</h3>
                    <div className="space-y-4">
                      <div>
                        <p className={`${typography.label} mb-2`}>Name</p>
                        <p className={`${typography.body}`}>
                          {patientCase.patientName || 'Not specified'}
                        </p>
                      </div>
                      {patientCase.patientAge && (
                        <div>
                          <p className={`${typography.label} mb-2`}>Age</p>
                          <p className={`${typography.body}`}>{patientCase.patientAge} years</p>
                        </div>
                      )}
                      {patientCase.patientGender && (
                        <div>
                          <p className={`${typography.label} mb-2`}>Gender</p>
                          <p className={`${typography.body} capitalize`}>{patientCase.patientGender}</p>
                        </div>
                      )}
                      <div>
                        <p className={`${typography.label} mb-2`}>Patient ID</p>
                        <p className={`${typography.body} font-mono`}>{patientCase.patientId}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Inline Document Viewer */}
              <div>
                <h3 className={`${typography.h3} mb-4`}>📄 Case Documents & Review</h3>
                <InlineCaseDocumentViewer
                  patientId={patientCase.patientId}
                  caseId={patientCase.id}
                  onDocumentSelect={setSelectedDocument}
                />
              </div>
            </div>
          )}

          {activeTab === 'documents' && (
            <div>
              {/* Full Document Management Interface */}
              <PatientDocuments
                patientId={patientCase.patientId}
                caseId={patientCase.id}
                hideHeader={true}
                title="Patient Case Documents"
                subtitle="Medical documents related to this patient case"
                emptyStateTitle="No case documents yet"
                emptyStateDescription="Upload medical documents related to this patient case"
                showPatientInfo={false} // We already show patient info in the header
                auditLog={true}
                compactMode={true}
              />
            </div>
          )}

          {activeTab === 'notes' && (
            <div className={commonClasses.card}>
              <div className={spacing.cardPadding}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className={`${typography.h3}`}>📝 Clinical Notes</h3>
                  <button className={commonClasses.primaryButton}>
                    <Plus className={`${sizes.iconSmall} mr-2`} />
                    Add Note
                  </button>
                </div>
                <div className="text-center py-12">
                  <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <p className={`${typography.body} ${textColors.secondary}`}>
                    Clinical notes functionality would be implemented here
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className={commonClasses.card}>
              <div className={spacing.cardPadding}>
                <h3 className={`${typography.h3} mb-6`}>⏰ Case Timeline</h3>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <Plus className="h-4 w-4 text-primary-600" />
                    </div>
                    <div className="flex-1">
                      <p className={`${typography.body} font-medium`}>Case Created</p>
                      <p className={`${typography.caption} ${textColors.muted}`}>
                        {new Date(patientCase.createdAt).toLocaleString()}
                      </p>
                      <p className={`${typography.body} ${textColors.secondary} mt-1`}>
                        Patient case was created and assigned for review
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Activity className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className={`${typography.body} font-medium`}>Last Updated</p>
                      <p className={`${typography.caption} ${textColors.muted}`}>
                        {new Date(patientCase.updatedAt).toLocaleString()}
                      </p>
                      <p className={`${typography.body} ${textColors.secondary} mt-1`}>
                        Case information was last modified
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}