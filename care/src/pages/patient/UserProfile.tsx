import { useState, useEffect, useRef } from 'react'
import { apiClient } from '@/services/api'
import {
  User,
  Edit3,
  Save,
  X,
  Camera,
  Bell,
  Lock,
  Heart,
  AlertCircle,
  CheckCircle,
  Shield,
  Settings
} from 'lucide-react'

interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  dateOfBirth: string
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  phone: string
  address: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  emergencyContact: {
    name: string
    relationship: string
    phone: string
  }
  medicalInfo: {
    bloodType: string
    allergies: string[]
    chronicConditions: string[]
    currentMedications: string[]
    insuranceProvider: string
    insurancePolicyNumber: string
  }
  preferences: {
    notifications: {
      email: boolean
      sms: boolean
      push: boolean
    }
    privacy: {
      shareDataForResearch: boolean
      allowMarketingCommunications: boolean
    }
  }
  profileImage?: string
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
}

export function UserProfile() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [editedProfile, setEditedProfile] = useState<UserProfile | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState<'personal' | 'medical' | 'preferences' | 'security'>('personal')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [uploadingPhoto, setUploadingPhoto] = useState(false)
  const [profileImageBlobUrl, setProfileImageBlobUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfileImage = async (userId: string) => {
    try {
  
      const token = localStorage.getItem('auth_token')
      if (!token) {
  
        return
      }
      
      const response = await fetch(`/api/storage/profile-photo/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const blob = await response.blob()
        const blobUrl = URL.createObjectURL(blob)
        setProfileImageBlobUrl(blobUrl)
  
      } else {
  
        setProfileImageBlobUrl(null)
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('❌ Failed to load profile image:', error)
      setProfileImageBlobUrl(null)
    }
  }

  const loadProfile = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiClient.getUserProfile()
  
      
      // Handle different response formats from the API
      const profileData = (response as any).profile || response || {}
      
  
      
      const combinedProfile: UserProfile = {
        id: profileData.id || '',
        email: profileData.email || '',
        firstName: profileData.firstName || '',
        lastName: profileData.lastName || '',
        dateOfBirth: profileData.dateOfBirth ? new Date(profileData.dateOfBirth).toISOString().split('T')[0] : '',
        gender: profileData.gender || 'prefer_not_to_say',
        phone: profileData.phoneNumber || '',
        address: {
          street: profileData.address || '',
          city: profileData.city || '',
          state: profileData.state || '',
          zipCode: profileData.zipCode || '',
          country: profileData.country || ''
        },
        emergencyContact: {
          name: profileData.emergencyContactName || '',
          relationship: profileData.emergencyContactRelationship || '',
          phone: profileData.emergencyContactPhone || ''
        },
        medicalInfo: {
          bloodType: profileData.bloodType || '',
          allergies: profileData.allergies || [],
          chronicConditions: profileData.medicalConditions || [],
          currentMedications: profileData.medications || [],
          insuranceProvider: profileData.insuranceProvider || '',
          insurancePolicyNumber: profileData.insurancePolicyNumber || ''
        },
        preferences: {
          notifications: {
            email: profileData.notificationEmail !== undefined ? profileData.notificationEmail : true,
            sms: profileData.notificationSms !== undefined ? profileData.notificationSms : false,
            push: profileData.notificationPush !== undefined ? profileData.notificationPush : true
          },
          privacy: {
            shareDataForResearch: profileData.privacyShareDataForResearch !== undefined ? profileData.privacyShareDataForResearch : false,
            allowMarketingCommunications: profileData.privacyAllowMarketingCommunications !== undefined ? profileData.privacyAllowMarketingCommunications : false
          }
        },
        profileImage: profileData.profilePictureUrl,
        createdAt: profileData.profileCreatedAt || new Date().toISOString(),
        updatedAt: profileData.profileUpdatedAt || new Date().toISOString(),
        lastLoginAt: profileData.lastLoginAt
      }
      
  
      setProfile(combinedProfile)
      setEditedProfile(combinedProfile)
      
      // Load profile image if user has one
      if (profileData.id) {
        await loadProfileImage(profileData.id)
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('❌ Failed to load profile:', error)
      setError('Failed to load profile data')
      
      const fallbackProfile: UserProfile = {
        id: '',
        email: '',
        firstName: '',
        lastName: '',
        dateOfBirth: '',
        gender: 'prefer_not_to_say',
        phone: '',
        address: {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: ''
        },
        emergencyContact: {
          name: '',
          relationship: '',
          phone: ''
        },
        medicalInfo: {
          bloodType: '',
          allergies: [],
          chronicConditions: [],
          currentMedications: [],
          insuranceProvider: '',
          insurancePolicyNumber: ''
        },
        preferences: {
          notifications: {
            email: true,
            sms: false,
            push: true
          },
          privacy: {
            shareDataForResearch: false,
            allowMarketingCommunications: false
          }
        },
        profileImage: undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: undefined
      }
      
      setProfile(fallbackProfile)
      setEditedProfile(fallbackProfile)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!editedProfile) return
    
    try {
      setSaving(true)
      setError(null)
      
      // Prepare data for backend - only send fields that backend expects
      const updateData = {
        firstName: editedProfile.firstName,
        lastName: editedProfile.lastName,
        phoneNumber: editedProfile.phone,
        dateOfBirth: editedProfile.dateOfBirth ? new Date(editedProfile.dateOfBirth).toISOString() : undefined,
        gender: editedProfile.gender,
        address: editedProfile.address.street,
        city: editedProfile.address.city,
        state: editedProfile.address.state,
        country: editedProfile.address.country,
        zipCode: editedProfile.address.zipCode,
        emergencyContactName: editedProfile.emergencyContact.name,
        emergencyContactPhone: editedProfile.emergencyContact.phone,
        emergencyContactRelationship: editedProfile.emergencyContact.relationship,
        bio: '', // Add bio field if needed
        // Preferences fields
        notificationEmail: editedProfile.preferences.notifications.email,
        notificationSms: editedProfile.preferences.notifications.sms,
        notificationPush: editedProfile.preferences.notifications.push,
        privacyShareDataForResearch: editedProfile.preferences.privacy.shareDataForResearch,
        privacyAllowMarketingCommunications: editedProfile.preferences.privacy.allowMarketingCommunications,
        // Profile image
        profilePictureUrl: editedProfile.profileImage,
        // Medical information fields
        bloodType: editedProfile.medicalInfo.bloodType,
        insuranceProvider: editedProfile.medicalInfo.insuranceProvider,
        insurancePolicyNumber: editedProfile.medicalInfo.insurancePolicyNumber,
        allergies: editedProfile.medicalInfo.allergies,
        medications: editedProfile.medicalInfo.currentMedications,
        medicalConditions: editedProfile.medicalInfo.chronicConditions
      }
      
      // Remove undefined, null, and empty values
      const cleanedData = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined && value !== null && value !== '')
      );
      
  
      
      // Update profile via API
      await apiClient.updateUserProfile(cleanedData)
      
      // Reload the profile to get the latest data
      await loadProfile()
      
      setIsEditing(false)
      setSuccessMessage('Profile updated successfully!')
      setTimeout(() => setSuccessMessage(null), 3000)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to update profile:', error)
      setError('Failed to update profile. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setEditedProfile(profile)
    setIsEditing(false)
    setError(null)
  }

  const handleProfilePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
  
    const file = event.target.files?.[0]
    if (!file) {
  
      return
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
  
      setError('Please select a valid image file.')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
  
      setError('Image file must be less than 5MB.')
      return
    }

  

    try {
      setUploadingPhoto(true)
      setError(null)
  

      // Upload the photo
      const response = await apiClient.uploadProfilePhoto(file)
  
      
      // Update the profile image URL in the current profile
      if (profile && editedProfile) {
        const photoUrl = (response as any).photo?.url || (response as any).url || ''
        const updatedProfile = {
          ...profile,
          profileImage: photoUrl
        }
        const updatedEditedProfile = {
          ...editedProfile,
          profileImage: photoUrl
        }
        
        setProfile(updatedProfile)
        setEditedProfile(updatedEditedProfile)
        setSuccessMessage('Profile photo updated successfully!')
        setTimeout(() => setSuccessMessage(null), 3000)
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('❌ Profile Photo Upload: Upload failed:', error)
      // TODO: Replace with proper error reporting
  console.error('❌ Profile Photo Upload: Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        error: error
      })
      setError('Failed to upload profile photo. Please try again.')
    } finally {
  
      setUploadingPhoto(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    if (!editedProfile) return
    
    const keys = field.split('.')
    const updatedProfile = { ...editedProfile }
    let current: any = updatedProfile
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]]
    }
    current[keys[keys.length - 1]] = value
    
    setEditedProfile(updatedProfile)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const tabs = [
    { id: 'personal', label: 'Personal Info', icon: User },
    { id: 'medical', label: 'Medical Info', icon: Heart },
    { id: 'preferences', label: 'Preferences', icon: Bell },
    { id: 'security', label: 'Security', icon: Lock }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (error && !profile) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading profile</h3>
          <p className="mt-1 text-sm text-gray-500">{error}</p>
          <div className="mt-6">
            <button
              onClick={loadProfile}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!profile || !editedProfile) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success/Error Messages */}
        {successMessage && (
          <div className="mb-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                <p className="text-sm font-medium text-green-800">{successMessage}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Profile Header - Compact Design */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center">
                    {profileImageBlobUrl ? (
                      <img
                        src={profileImageBlobUrl}
                        alt={`${profile.firstName} ${profile.lastName}`}
                        className="h-16 w-16 rounded-full object-cover"
                      />
                    ) : (
                      <User className="h-8 w-8 text-primary-600" />
                    )}
                  </div>
                  {isEditing && (
                    <>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleProfilePhotoUpload}
                        className="hidden"
                      />
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploadingPhoto}
                        className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-primary-600 text-white flex items-center justify-center hover:bg-primary-700 disabled:opacity-50 shadow-sm transition-colors"
                        title="Upload profile photo"
                      >
                        {uploadingPhoto ? (
                          <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent" />
                        ) : (
                          <Camera className="h-3 w-3" />
                        )}
                      </button>
                    </>
                  )}
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    {profile.firstName} {profile.lastName}
                  </h1>
                  <p className="text-sm text-gray-500">{profile.email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                {isEditing ? (
                  <>
                    <button
                      onClick={handleCancel}
                      disabled={saving}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      <X className="h-4 w-4 mr-1" />
                      Cancel
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={saving}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
                    >
                      {saving ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      ) : (
                        <Save className="h-4 w-4 mr-1" />
                      )}
                      {saving ? 'Saving...' : 'Save'}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100"
                  >
                    <Edit3 className="h-4 w-4 mr-1" />
                    Edit
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs - Clean Design */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <nav className="flex border-b border-gray-200">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 flex items-center justify-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 bg-primary-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content Area - Clean and Compact */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            {/* Personal Info Tab */}
            {activeTab === 'personal' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                  <input
                    type="text"
                    value={editedProfile.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    disabled={!isEditing}
                    placeholder="First Name"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                  <input
                    type="text"
                    value={editedProfile.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Last Name"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                  <input
                    type="email"
                    value={editedProfile.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Email Address"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                  <input
                    type="tel"
                    value={editedProfile.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Phone Number"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                  <input
                    type="date"
                    value={editedProfile.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    disabled={!isEditing}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                  <select
                    value={editedProfile.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    disabled={!isEditing}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  >
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                    <option value="prefer_not_to_say">Prefer not to say</option>
                  </select>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                    <div className="sm:col-span-2">
                      <input
                        type="text"
                        value={editedProfile.address.street}
                        onChange={(e) => handleInputChange('address.street', e.target.value)}
                        disabled={!isEditing}
                        placeholder="Street Address"
                        className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                      />
                    </div>
                    <input
                      type="text"
                      value={editedProfile.address.city}
                      onChange={(e) => handleInputChange('address.city', e.target.value)}
                      disabled={!isEditing}
                      placeholder="City"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                    <input
                      type="text"
                      value={editedProfile.address.state}
                      onChange={(e) => handleInputChange('address.state', e.target.value)}
                      disabled={!isEditing}
                      placeholder="State/Province"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                    <input
                      type="text"
                      value={editedProfile.address.zipCode}
                      onChange={(e) => handleInputChange('address.zipCode', e.target.value)}
                      disabled={!isEditing}
                      placeholder="ZIP/Postal Code"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                    <input
                      type="text"
                      value={editedProfile.address.country}
                      onChange={(e) => handleInputChange('address.country', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Country"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
                    <input
                      type="text"
                      value={editedProfile.emergencyContact.name}
                      onChange={(e) => handleInputChange('emergencyContact.name', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Emergency Contact Name"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                    <input
                      type="text"
                      value={editedProfile.emergencyContact.relationship}
                      onChange={(e) => handleInputChange('emergencyContact.relationship', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Relationship"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                    <input
                      type="tel"
                      value={editedProfile.emergencyContact.phone}
                      onChange={(e) => handleInputChange('emergencyContact.phone', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Emergency Contact Phone"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Medical Info Tab */}
            {activeTab === 'medical' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                  <select
                    value={editedProfile.medicalInfo.bloodType}
                    onChange={(e) => handleInputChange('medicalInfo.bloodType', e.target.value)}
                    disabled={!isEditing}
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  >
                    <option value="">Select Blood Type</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </select>
                  <input
                    type="text"
                    value={editedProfile.medicalInfo.insuranceProvider}
                    onChange={(e) => handleInputChange('medicalInfo.insuranceProvider', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Insurance Provider"
                    className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                  <div className="sm:col-span-2">
                    <input
                      type="text"
                      value={editedProfile.medicalInfo.insurancePolicyNumber}
                      onChange={(e) => handleInputChange('medicalInfo.insurancePolicyNumber', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Insurance Policy Number"
                      className="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                </div>

                {/* Medical Lists */}
                {['allergies', 'chronicConditions', 'currentMedications'].map((field) => (
                  <div key={field} className="border-t border-gray-200 pt-4">
                    <div className="space-y-2">
                      {(editedProfile.medicalInfo as any)[field].map((item: string, index: number) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={item}
                            onChange={(e) => {
                              const newItems = [...(editedProfile.medicalInfo as any)[field]]
                              newItems[index] = e.target.value
                              handleInputChange(`medicalInfo.${field}`, newItems)
                            }}
                            disabled={!isEditing}
                            className="flex-1 border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500"
                            placeholder={`${field === 'allergies' ? 'Allergy' : field === 'chronicConditions' ? 'Chronic Condition' : 'Current Medication'}`}
                          />
                          {isEditing && (
                            <button
                              onClick={() => {
                                const newItems = (editedProfile.medicalInfo as any)[field].filter((_: any, i: number) => i !== index)
                                handleInputChange(`medicalInfo.${field}`, newItems)
                              }}
                              className="p-2 text-red-600 hover:text-red-800 rounded-md"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      ))}
                      {isEditing && (
                        <button
                          onClick={() => {
                            const newItems = [...(editedProfile.medicalInfo as any)[field], '']
                            handleInputChange(`medicalInfo.${field}`, newItems)
                          }}
                          className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                        >
                          + Add {field === 'allergies' ? 'Allergy' :
                                 field === 'chronicConditions' ? 'Condition' : 'Medication'}
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Preferences Tab */}
            {activeTab === 'preferences' && (
              <div className="space-y-3">
                {Object.entries(editedProfile.preferences.notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-2">
                    <span className="text-sm text-gray-900">
                      {key === 'email' ? 'Email Notifications' :
                       key === 'sms' ? 'SMS Notifications' : 'Push Notifications'}
                    </span>
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => handleInputChange(`preferences.notifications.${key}`, e.target.checked)}
                      disabled={!isEditing}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded disabled:opacity-50"
                    />
                  </div>
                ))}
                
                <div className="border-t border-gray-200 pt-3">
                  {Object.entries(editedProfile.preferences.privacy).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between py-2">
                      <span className="text-sm text-gray-900">
                        {key === 'shareDataForResearch' ? 'Share Data for Research' : 'Marketing Communications'}
                      </span>
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => handleInputChange(`preferences.privacy.${key}`, e.target.checked)}
                        disabled={!isEditing}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded disabled:opacity-50"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="space-y-3">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Lock className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-sm text-gray-900">Password</span>
                    </div>
                    <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      Change
                    </button>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Lock className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">Two-Factor Authentication</span>
                    </div>
                    <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      Enable
                    </button>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-500 mr-2" />
                      <span className="text-sm text-gray-900">
                        Last Login: {profile.lastLoginAt ? formatDate(profile.lastLoginAt) : 'Never'}
                      </span>
                    </div>
                    <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      View
                    </button>
                  </div>
                </div>

                <button className="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-900">Download Your Data</span>
                    <User className="h-4 w-4 text-gray-400" />
                  </div>
                </button>
                
                <button className="w-full text-left p-3 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-red-900">Delete Account</span>
                    <AlertCircle className="h-4 w-4 text-red-400" />
                  </div>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
