import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useNavigate } from 'react-router-dom'
import { apiClient } from '@/services/api'
import {
  Heart,
  FileText,
  Calendar,
  Plus,
  Upload,
  Clock
} from 'lucide-react'
import { DashboardCard } from '@/components/ui/dashboard-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { PageHeader } from '@/components/ui/page-header'
import { ErrorAlert } from '@/components/ui/error-alert'
import { layouts } from '@/constants/theme'

interface Case {
  id: string
  title: string
  description: string
  status: 'draft' | 'submitted' | 'in_review' | 'assigned' | 'completed' | 'cancelled'
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  specialtyRequired?: string
  createdAt: string
  updatedAt: string
}

interface Document {
  id: string
  title: string
  originalFileName: string
  documentType: string
  fileSize: number
  createdAt: string
  caseId?: string
}

interface DashboardStats {
  totalCases: number
  activeCases: number
  completedCases: number
  pendingReviews: number
  totalDocuments: number
  recentDocuments: number
}

export function PatientDashboard() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [cases, setCases] = useState<Case[]>([])
  const [documents, setDocuments] = useState<Document[]>([])
  const [stats, setStats] = useState<DashboardStats>({
    totalCases: 0,
    activeCases: 0,
    completedCases: 0,
    pendingReviews: 0,
    totalDocuments: 0,
    recentDocuments: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch cases using apiClient
      const casesResponse = await apiClient.getCases()
      setCases(casesResponse.data || [])

      // Fetch documents using apiClient
      const documentsResponse = await apiClient.getDocuments()
      setDocuments(documentsResponse.documents || [])

    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error fetching dashboard data:', err)
      setError('Failed to load dashboard data. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Calculate stats from fetched data
  useEffect(() => {
    const totalCases = cases.length
    const activeCases = cases.filter(c => ['submitted', 'in_review', 'assigned'].includes(c.status)).length
    const completedCases = cases.filter(c => c.status === 'completed').length
    const pendingReviews = cases.filter(c => c.status === 'submitted').length
    const totalDocuments = documents.length
    const recentDocuments = documents.filter(d => {
      const uploadDate = new Date(d.createdAt)
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return uploadDate > weekAgo
    }).length

    setStats({
      totalCases,
      activeCases,
      completedCases,
      pendingReviews,
      totalDocuments,
      recentDocuments
    })
  }, [cases, documents])



  if (loading) {
    return (
      <LoadingSpinner
        fullScreen
        text="Loading your health dashboard..."
        size="xl"
      />
    )
  }

  return (
    <div className={layouts.page}>
      <div className={`${layouts.container} ${layouts.pageContent}`}>
        {/* Error Alert */}
        {error && (
          <ErrorAlert
            variant="error"
            title="Dashboard Error"
            message={error}
            dismissible
            onDismiss={() => setError(null)}
          />
        )}

        {/* Welcome Section */}
        <PageHeader
          title={`Welcome back, ${user?.firstName || 'Patient'}! 👋`}
          description="Here's an overview of your health journey with Continuia."
        />

        {/* Stats Cards */}
        <div className={layouts.gridCols4}>
          <DashboardCard
            title="My Requests"
            value={stats.totalCases}
            description="Total requests created"
            icon={FileText}
            variant="gradient"
            color="blue"
          />

          <DashboardCard
            title="Requests in Progress"
            value={stats.activeCases}
            description="Being reviewed by doctors"
            icon={Heart}
            variant="gradient"
            color="green"
          />

          <DashboardCard
            title="Pending Reviews"
            value={stats.pendingReviews}
            description="Awaiting doctor review"
            icon={Clock}
            variant="gradient"
            color="yellow"
          />

          <DashboardCard
            title="My Documents"
            value={stats.totalDocuments}
            description="Medical records uploaded"
            icon={FileText}
            variant="gradient"
            color="purple"
          />
        </div>

        {/* Quick Actions */}
        <PageHeader
          title="🚀 What would you like to do today?"
          description="Choose an action to get started with your healthcare journey"
          centered={true}
        />

        <div className={layouts.gridCols3}>
          <button
            onClick={() => navigate('/patient/cases/new')}
            className="group bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border-2 border-blue-200 hover:border-blue-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <Plus className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-blue-900 mb-2">Create New Case</h4>
            <p className="text-sm text-blue-700 leading-relaxed">Start a new medical consultation and get expert opinions</p>
          </button>
          
          <button
            onClick={() => navigate('/documents')}
            className="group bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border-2 border-purple-200 hover:border-purple-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-purple-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <Upload className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-purple-900 mb-2">Upload Documents</h4>
            <p className="text-sm text-purple-700 leading-relaxed">Share your medical records and test results securely</p>
          </button>
          
          <button
            onClick={() => navigate('/appointments')}
            className="group bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border-2 border-green-200 hover:border-green-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <Calendar className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-green-900 mb-2">View Appointments</h4>
            <p className="text-sm text-green-700 leading-relaxed">Check your scheduled consultations and meetings</p>
          </button>
        </div>

        {/* Disclaimer Footer */}
        <ErrorAlert
          variant="warning"
          title="Medical Disclaimer"
          message="For second opinion and informational purposes only. The information provided through Continuia is not intended to replace professional medical advice, diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider with any questions you may have regarding a medical condition."
        />
      </div>
    </div>
  )
}
