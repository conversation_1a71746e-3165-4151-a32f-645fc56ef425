import React, { useState, useEffect } from 'react';
import { apiClient } from '@/services/api';
import { useParams, useNavigate } from 'react-router-dom';
import TextViewer from '@/components/ui/text-viewer';
import { CheckCircle2, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';

interface ConsentFormVersion {
  id: string;
  version: number;
  content: string;
  isActive: boolean;
  createdAt: string;
  template: {
    id: string;
    title: string;
  };
}

const PatientConsentForm: React.FC = () => {
  const { id } = useParams(); // This is the form version ID
  const navigate = useNavigate();
  
  const [hasConsented, setHasConsented] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const [data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Fetch consent form version data
  useEffect(() => {
    const fetchConsentFormVersion = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        const response = await apiClient.getConsentFormVersion(id);
        setData(response);
      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error fetching consent form version:', err);
        setError('Failed to load consent form. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchConsentFormVersion();
  }, [id]);
  
  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  const formVersion = data?.data?.version as ConsentFormVersion;
  
  if (!formVersion) {
    return (
      <div className="p-4">
        <div className="border rounded-lg shadow-sm">
          <div className="pt-6 p-6 text-center">
            <p className="text-gray-500">Consent form not found.</p>
            <button 
              onClick={() => navigate('/dashboard')}
              className="mt-4 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  const handleConsent = async () => {
    if (!hasConsented) {
      setError('You must check the consent box to proceed.');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      await apiClient.submitConsent({
        formVersionId: formVersion.id,
        metadata: {
          browser: navigator.userAgent,
          timestamp: new Date().toISOString(),
        },
      });
      
      setSuccess(true);
    } catch (err) {
      setError('An error occurred while recording your consent. Please try again.');
      // TODO: Replace with proper error reporting
  console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (success) {
    return (
      <div className="p-4">
        <div className="border rounded-lg shadow-sm max-w-3xl mx-auto">
          <div className="p-4 border-b">
            <h2 className="text-center text-green-600 flex items-center justify-center gap-2 text-xl font-semibold">
              <CheckCircle2 size={24} />
              Consent Recorded
            </h2>
          </div>
          <div className="p-6 text-center">
            <p>
              Thank you! Your consent to "{formVersion.template.title}" has been successfully recorded.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Recorded on {format(new Date(), 'PPP')}
            </p>
          </div>
          <div className="p-4 border-t bg-gray-50 flex justify-center">
            <button 
              onClick={() => navigate('/dashboard')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Return to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-4">
      <div className="border rounded-lg shadow-sm max-w-3xl mx-auto">
        <div className="p-4 border-b">
          <h2 className="text-xl font-semibold">{formVersion.template.title}</h2>
        </div>
        <div className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                <span className="font-medium">Error</span>
              </div>
              <p className="mt-1 text-sm">{error}</p>
            </div>
          )}
          
          <div className="prose max-w-none">
            <TextViewer content={formVersion.content} />
          </div>
          
          <div className="border-t pt-4">
            <div className="flex items-start space-x-2">
              <input 
                type="checkbox" 
                id="consent" 
                checked={hasConsented} 
                onChange={(e) => setHasConsented(e.target.checked)} 
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1"
              />
              <label
                htmlFor="consent"
                className="text-sm font-medium"
              >
                I have read and agree to the terms outlined in this consent form.
              </label>
            </div>
          </div>
        </div>
        <div className="p-4 border-t bg-gray-50 flex justify-between">
          <button 
            onClick={() => navigate('/dashboard')}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            onClick={handleConsent} 
            disabled={isSubmitting || !hasConsented}
            className={`px-4 py-2 rounded-md ${isSubmitting || !hasConsented ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'} text-white`}
          >
            {isSubmitting ? (
              <>
                <span className="animate-spin inline-block mr-2">⟳</span>
                Processing...
              </>
            ) : (
              'I Consent'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PatientConsentForm;
