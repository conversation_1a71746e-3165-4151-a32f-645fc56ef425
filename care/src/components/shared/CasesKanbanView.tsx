import React, { useState } from 'react'
import {
  Calendar,
  User,
  FileText,
  Stethoscope
} from 'lucide-react'

// Define types
interface Case {
  id: string
  title: string
  description: string
  status: 'draft' | 'submitted' | 'assigned' | 'in_review' | 'completed'
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  patientId: string
  patientName?: string
  // Note: assignedDoctorId has been removed in favor of multi-doctor assignment system
  // Doctors are now assigned via the case_doctors table
  assignedDoctorName?: string
  specialtyRequired?: string
  createdAt: string
  updatedAt: string
}

interface StatusConfig {
  [key: string]: {
    label: string
    color: string
    icon: React.ElementType
  }
}

interface UrgencyConfig {
  [key: string]: {
    label: string
    color: string
  }
}

interface CasesKanbanViewProps {
  cases: Case[]
  statusConfig: StatusConfig
  urgencyConfig: UrgencyConfig
  onCaseClick: (caseId: string) => void
  userRole: string
  onStatusChange?: (caseId: string, newStatus: Case['status']) => void
}

export function CasesKanbanView({
  cases,
  statusConfig,
  urgencyConfig,
  onCaseClick,
  userRole,
  onStatusChange
}: CasesKanbanViewProps) {
  const [draggingCaseId, setDraggingCaseId] = useState<string | null>(null)

  // Group cases by status
  const casesByStatus: Record<string, Case[]> = {
    draft: [],
    submitted: [],
    assigned: [],
    in_review: [],
    completed: []
  }

  cases.forEach(case_ => {
    if (casesByStatus[case_.status]) {
      casesByStatus[case_.status].push(case_)
    }
  })

  // Format date to readable format
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  // Handle drag start
  const handleDragStart = (caseId: string) => {
    setDraggingCaseId(caseId)
  }

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  // Handle drop
  const handleDrop = (e: React.DragEvent, targetStatus: Case['status']) => {
    e.preventDefault()
    if (draggingCaseId && onStatusChange) {
      onStatusChange(draggingCaseId, targetStatus)
      setDraggingCaseId(null)
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 overflow-x-auto">
      {Object.entries(casesByStatus).map(([status, statusCases]) => {
        const StatusIcon = statusConfig[status]?.icon || FileText
        
        return (
          <div
            key={status}
            className="bg-gray-50 rounded-lg p-4 min-w-[250px]"
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, status as Case['status'])}
          >
            <div className="flex items-center mb-4">
              <StatusIcon className="h-5 w-5 mr-2 text-gray-500" />
              <h3 className="font-medium text-gray-900">{statusConfig[status]?.label}</h3>
              <span className="ml-auto bg-gray-200 text-gray-700 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {statusCases.length}
              </span>
            </div>
            
            <div className="space-y-3">
              {statusCases.map(case_ => (
                <div
                  key={case_.id}
                  className="bg-white p-3 rounded-md shadow mb-2 cursor-move"
                  draggable={userRole === 'admin' || userRole === 'agent'}
                  onDragStart={() => handleDragStart(case_.id)}
                  onDoubleClick={() => onCaseClick(case_.id)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-sm line-clamp-2">{case_.title}</h4>
                    <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${urgencyConfig[case_.urgencyLevel]?.color}`}>
                      {urgencyConfig[case_.urgencyLevel]?.label}
                    </span>
                  </div>
                  
                  <p className="text-xs text-gray-500 mb-3 line-clamp-2">{case_.description}</p>
                  
                  <div className="flex flex-col space-y-1 text-xs">
                    <div className="flex items-center text-gray-600">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>{formatDate(case_.createdAt)}</span>
                    </div>
                    
                    {case_.specialtyRequired && (
                      <div className="flex items-center text-gray-600">
                        <Stethoscope className="h-3 w-3 mr-1" />
                        <span className="truncate">{case_.specialtyRequired}</span>
                      </div>
                    )}
                    
                    {case_.assignedDoctorName && (
                      <div className="flex items-center text-gray-600">
                        <User className="h-3 w-3 mr-1" />
                        <span className="truncate">{case_.assignedDoctorName}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {statusCases.length === 0 && (
                <div className="bg-white border border-dashed border-gray-300 rounded-md p-4 text-center text-gray-500 text-sm">
                  No cases
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
