import { useState, useEffect } from 'react'
import { X, Search, User, CheckCircle, AlertCircle, Shield, Clock } from 'lucide-react'
import { apiClient } from '@/services/api'

interface Specialization {
  assignment_id: string
  specialization_id: string
  specialization_name: string
  specialization_code?: string
  category_name?: string
  is_primary: boolean
  board_certified: boolean
  years_experience: number
}

interface Doctor {
  id: string
  firstName: string
  lastName: string
  email: string
  specialization?: string // Keep for backward compatibility
  specializations?: Specialization[]
  isActive: boolean
  profile?: {
    bio?: string
    phoneNumber?: string
    profilePictureUrl?: string
  }
}

interface DoctorAssignmentModalProps {
  caseId: string
  isOpen: boolean
  onClose: () => void
  onAssignmentChange: () => void
  currentAssignments: {
    id: string
    doctorId: string
    name: string
    specialization: string
  }[]
}

export function DoctorAssignmentModal({
  caseId,
  isOpen,
  onClose,
  onAssignmentChange,
  currentAssignments
}: DoctorAssignmentModalProps) {
  const [doctors, setDoctors] = useState<Doctor[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [actionInProgress, setActionInProgress] = useState(false)

  // Get current doctor IDs for checking if already assigned
  const assignedDoctorIds = currentAssignments.map(doc => doc.doctorId)

  useEffect(() => {
    if (isOpen) {
      fetchDoctors()
    }
  }, [isOpen])

  const fetchDoctors = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Use the doctors endpoint which is accessible to all authenticated users
      const response: any = await apiClient.getDoctors()
      
      // Handle different response formats
      let doctorsData: any[] = []
      if (Array.isArray(response)) {
        doctorsData = response
      } else if (response && response.data && Array.isArray(response.data)) {
        doctorsData = response.data
      } else if (response && Array.isArray(response.doctors)) {
        doctorsData = response.doctors
      } else {
        // TODO: Replace with proper error reporting
  console.error('Unexpected response format:', response)
        setError('Failed to load doctors. Please try again.')
        return
      }
      
      // Filter for active doctors only (all active doctors are eligible)
      // Transform the API response to match our Doctor interface
      const activeDoctors = doctorsData
        .filter((doctor: any) => doctor.isActive !== false) // Assume active if not explicitly inactive
        .map((doctor: any) => ({
          id: doctor.id,
          firstName: doctor.firstName,
          lastName: doctor.lastName,
          email: doctor.email,
          specialization: doctor.profile?.bio || doctor.specialization || 'General Medicine', // Backward compatibility
          specializations: doctor.specializations || [],
          profile: doctor.profile,
          isActive: true
        }))
      
      console.log(`Found ${activeDoctors.length} active doctors available for case assignment`)
      setDoctors(activeDoctors)
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error fetching doctors:', err)
      setError('Failed to load doctors. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const assignDoctor = async (doctorId: string) => {
    setActionInProgress(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Use the correct API method to assign a doctor
      await apiClient.assignCase(caseId, doctorId)
      setSuccess('Doctor assigned successfully')
      onAssignmentChange()
      
      // Update the local list of assigned doctor IDs
      assignedDoctorIds.push(doctorId)
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error assigning doctor:', err)
      setError('Failed to assign doctor. Please try again.')
    } finally {
      setActionInProgress(false)
    }
  }

  const removeDoctor = async (doctorId: string) => {
    setActionInProgress(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Since there's no direct API method for removing a doctor, we need to use fetch directly
      // Get the token from localStorage
      const token = localStorage.getItem('auth_token')
      
      const response = await fetch(`/api/cases/${caseId}/doctors/${doctorId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error('Failed to remove doctor')
      }
      setSuccess('Doctor removed successfully')
      onAssignmentChange()
      
      // Update the local list of assigned doctor IDs
      const index = assignedDoctorIds.indexOf(doctorId)
      if (index > -1) {
        assignedDoctorIds.splice(index, 1)
      }
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error removing doctor:', err)
      setError('Failed to remove doctor. Please try again.')
    } finally {
      setActionInProgress(false)
    }
  }

  // Filter doctors based on search term
  const filteredDoctors = doctors.filter(doctor => {
    const fullName = `${doctor.firstName} ${doctor.lastName}`.toLowerCase()
    const email = doctor.email.toLowerCase()
    const term = searchTerm.toLowerCase()
    
    // Search in specializations
    const specializationMatch = doctor.specializations?.some(spec =>
      spec.specialization_name.toLowerCase().includes(term) ||
      spec.category_name?.toLowerCase().includes(term) ||
      spec.specialization_code?.toLowerCase().includes(term)
    ) || false
    
    // Fallback to bio field for backward compatibility
    const bioMatch = doctor.profile?.bio?.toLowerCase().includes(term) || false
    const legacySpecMatch = doctor.specialization?.toLowerCase().includes(term) || false
    
    return fullName.includes(term) ||
           email.includes(term) ||
           specializationMatch ||
           bioMatch ||
           legacySpecMatch
  })

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Assign Doctors to Case</h2>
            <p className="text-sm text-gray-500 mt-1">All active doctors are eligible for case assignment</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        {/* Search */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search doctors by name, specialization, or email"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        {/* Status messages */}
        {error && (
          <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center text-red-800">
            <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
            <p>{error}</p>
          </div>
        )}
        
        {success && (
          <div className="mx-6 mt-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center text-green-800">
            <CheckCircle className="h-5 w-5 mr-2 flex-shrink-0" />
            <p>{success}</p>
          </div>
        )}
        
        {/* Doctor list */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <p className="ml-3 text-gray-600">Loading doctors...</p>
            </div>
          ) : filteredDoctors.length > 0 ? (
            <div className="space-y-3">
              {filteredDoctors.map(doctor => {
                const isAssigned = assignedDoctorIds.includes(doctor.id)
                
                return (
                  <div 
                    key={doctor.id} 
                    className={`flex items-center justify-between p-4 rounded-lg border ${
                      isAssigned ? 'border-primary-200 bg-primary-50' : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-center">
                      <User className={`h-6 w-6 mr-3 ${isAssigned ? 'text-primary-600' : 'text-gray-400'}`} />
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">{doctor.firstName} {doctor.lastName}</p>
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </span>
                        </div>
                        <div className="flex flex-wrap items-center gap-1 mt-1">
                          {doctor.specializations && doctor.specializations.length > 0 ? (
                            doctor.specializations.map((spec, index) => (
                              <span
                                key={spec.assignment_id || index}
                                className={`inline-flex items-center px-2 py-1 rounded-md text-sm font-medium ${
                                  spec.is_primary
                                    ? 'bg-blue-100 text-blue-800 ring-1 ring-blue-600'
                                    : 'bg-gray-100 text-gray-800'
                                }`}
                              >
                                {spec.specialization_name}
                                {spec.is_primary && (
                                  <span className="ml-1 text-blue-600">★</span>
                                )}
                              </span>
                            ))
                          ) : (
                            // Fallback to bio field or legacy specialization for backward compatibility
                            <span className="inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
                              {doctor.profile?.bio || doctor.specialization || 'General Medicine'}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => isAssigned ? removeDoctor(doctor.id) : assignDoctor(doctor.id)}
                      disabled={actionInProgress}
                      className={`px-4 py-2 rounded-md text-sm font-medium ${
                        isAssigned
                          ? 'bg-red-50 text-red-700 hover:bg-red-100'
                          : 'bg-primary-50 text-primary-700 hover:bg-primary-100'
                      } ${actionInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {isAssigned ? 'Remove' : 'Assign'}
                    </button>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <User className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">
                {searchTerm ? 'No doctors match your search' : 'No doctors available'}
              </p>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="border-t border-gray-200 px-6 py-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  )
}

export default DoctorAssignmentModal
