import React from 'react';
import { User } from 'lucide-react';

interface CollaboratorInfo {
  userId: string;
  userName: string;
  userColor: string;
  cursorPosition?: any;
}

interface UserPresenceProps {
  collaborators: CollaboratorInfo[];
  maxVisible?: number;
  className?: string;
}

export const UserPresence: React.FC<UserPresenceProps> = ({
  collaborators,
  maxVisible = 5,
  className = '',
}) => {
  if (collaborators.length === 0) {
    return null;
  }

  const visibleCollaborators = collaborators.slice(0, maxVisible);
  const hiddenCount = Math.max(0, collaborators.length - maxVisible);

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getContrastColor = (backgroundColor: string): string => {
    // Convert hex to RGB
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return black or white based on luminance
    return luminance > 0.5 ? '#000000' : '#FFFFFF';
  };

  return (
    <div className={`user-presence flex items-center space-x-2 ${className}`}>
      <div className="flex items-center -space-x-2">
        {visibleCollaborators.map((collaborator, index) => (
          <div
            key={collaborator.userId}
            className="relative group"
            style={{ zIndex: visibleCollaborators.length - index }}
          >
            <div
              className="w-8 h-8 rounded-full border-2 border-white flex items-center justify-center text-xs font-medium shadow-sm cursor-pointer transition-transform hover:scale-110"
              style={{
                backgroundColor: collaborator.userColor,
                color: getContrastColor(collaborator.userColor),
              }}
              title={`${collaborator.userName} is editing`}
            >
              {getInitials(collaborator.userName)}
            </div>
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-50">
              {collaborator.userName}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
            
            {/* Active indicator */}
            <div
              className="absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white"
              style={{ backgroundColor: collaborator.userColor }}
            >
              <div className="w-full h-full rounded-full animate-pulse bg-white opacity-50"></div>
            </div>
          </div>
        ))}
        
        {/* Show count of hidden collaborators */}
        {hiddenCount > 0 && (
          <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600 shadow-sm">
            +{hiddenCount}
          </div>
        )}
      </div>
      
      {/* Status text */}
      <div className="text-sm text-gray-600 hidden sm:block">
        {collaborators.length === 1 ? (
          <span>{collaborators[0].userName} is editing</span>
        ) : (
          <span>{collaborators.length} people editing</span>
        )}
      </div>
      
      {/* Mobile status */}
      <div className="text-xs text-gray-500 sm:hidden">
        <User className="h-4 w-4" />
      </div>
    </div>
  );
};
