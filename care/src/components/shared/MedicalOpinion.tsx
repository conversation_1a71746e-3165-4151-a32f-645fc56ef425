import React, { useState, useEffect } from 'react';
import { Steth<PERSON><PERSON>, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { apiClient } from '@/services/api'; 
import { CaseData } from '@/types/case';
import { useAuth } from '@/hooks/useAuth';

interface MedicalOpinionProps {
  caseData: CaseData;
  isNotesReadOnly: () => boolean;
  allNotes: { [noteType: string]: any };
  notesLoading: boolean;
  doctorAcceptanceStatus: {
    isDoctor: boolean;
    isAssigned: boolean;
    hasAccepted: boolean;
    message: string;
  } | null;
}

interface MedicalOpinionData {
  id: string;
  diagnosis: string;
  recommendations: string;
  treatmentPlan?: string;
  followUpInstructions?: string;
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent';
  confidenceLevel: 'low' | 'medium' | 'high';
  additionalTests?: string;
  referralSpecialty?: string;
  notes?: string;
  status: 'draft' | 'submitted' | 'reviewed' | 'approved';
  submittedAt?: string;
  doctorId: string;
  doctorName?: string;
  createdAt: string;
  updatedAt: string;
}

const MedicalOpinion: React.FC<MedicalOpinionProps> = ({
  caseData,
  isNotesReadOnly,
  allNotes,
  notesLoading
}) => {
  const { user } = useAuth();
  const [medicalOpinions, setMedicalOpinions] = useState<MedicalOpinionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (caseData?.id) {
      fetchMedicalOpinions();
    }
  }, [caseData?.id]);

  const fetchMedicalOpinions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getMedicalOpinions(caseData.id);
      
      // Filter out draft opinions for patients (backend should handle this, but double-check)
      const filteredOpinions = user?.role === 'patient' 
        ? response.filter((opinion: MedicalOpinionData) => opinion.status !== 'draft')
        : response;
        
      setMedicalOpinions(filteredOpinions || []);
    } catch (err) {
      console.error('Error fetching medical opinions:', err);
      setError('Failed to load medical opinions');
    } finally {
      setLoading(false);
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'reviewed': return 'bg-blue-100 text-blue-800';
      case 'submitted': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading || notesLoading) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="bg-red-50 border border-red-200 rounded-2xl p-8 max-w-md">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-900 mb-2">Error Loading Medical Opinion</h3>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // For doctors, show the note editor (same as desk app)
  if (user?.role === 'doctor') {
    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
            <div className="flex items-center">
              <Stethoscope className="h-6 w-6 mr-3 text-red-500" />
              <h2 className="text-xl font-bold text-gray-900">Medical Opinion</h2>
            </div>
            <div className="flex items-center space-x-3">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Doctor Only
              </span>
              <div className="text-sm text-gray-500">
                Auto-save enabled
              </div>
            </div>
          </div>
          <div className="flex-1 bg-white p-4">
            <p className="text-gray-600">
              Doctor interface for medical opinions would be implemented here.
              For now, please use the desk application to create medical opinions.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // For patients and other roles, show submitted medical opinions
  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 p-8">
        {medicalOpinions.length > 0 ? (
          <div className="space-y-6">
            {medicalOpinions.map((opinion) => (
              <div key={opinion.id} className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-primary-200 p-8 hover:shadow-2xl transition-all duration-300">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center">
                    <Stethoscope className="h-8 w-8 mr-4 text-primary-500" />
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">Medical Opinion</h3>
                      {opinion.doctorName && (
                        <p className="text-sm text-gray-600">by Dr. {opinion.doctorName}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(opinion.status)}`}>
                      {opinion.status.charAt(0).toUpperCase() + opinion.status.slice(1)}
                    </span>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getUrgencyColor(opinion.urgencyLevel)}`}>
                      {opinion.urgencyLevel.charAt(0).toUpperCase() + opinion.urgencyLevel.slice(1)} Priority
                    </span>
                  </div>
                </div>

                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">Diagnosis</h4>
                    <div className="prose prose-lg max-w-none text-gray-700">
                      {opinion.diagnosis}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">Recommendations</h4>
                    <div className="prose prose-lg max-w-none text-gray-700">
                      {opinion.recommendations}
                    </div>
                  </div>

                  {opinion.treatmentPlan && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Treatment Plan</h4>
                      <div className="prose prose-lg max-w-none text-gray-700">
                        {opinion.treatmentPlan}
                      </div>
                    </div>
                  )}

                  {opinion.followUpInstructions && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Follow-up Instructions</h4>
                      <div className="prose prose-lg max-w-none text-gray-700">
                        {opinion.followUpInstructions}
                      </div>
                    </div>
                  )}

                  {opinion.additionalTests && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Additional Tests</h4>
                      <div className="prose prose-lg max-w-none text-gray-700">
                        {opinion.additionalTests}
                      </div>
                    </div>
                  )}

                  {opinion.referralSpecialty && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Referral</h4>
                      <div className="prose prose-lg max-w-none text-gray-700">
                        Referral to: {opinion.referralSpecialty}
                      </div>
                    </div>
                  )}

                  {opinion.notes && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Additional Notes</h4>
                      <div className="prose prose-lg max-w-none text-gray-700">
                        {opinion.notes}
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200 text-sm text-gray-500">
                  <div className="flex items-center justify-between">
                    <div>
                      Confidence Level: <span className="font-medium">{opinion.confidenceLevel.charAt(0).toUpperCase() + opinion.confidenceLevel.slice(1)}</span>
                    </div>
                    <div>
                      {opinion.submittedAt ? (
                        <>Submitted: {new Date(opinion.submittedAt).toLocaleDateString()}</>
                      ) : (
                        <>Created: {new Date(opinion.createdAt).toLocaleDateString()}</>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-primary-200 p-12 hover:shadow-2xl transition-all duration-300 max-w-md">
              <div className="text-center">
                <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Stethoscope className="h-10 w-10 text-primary-500" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Medical Opinion</h3>
                <p className="text-lg text-gray-600 mb-6">The attending physician's comprehensive medical opinion will appear here once provided.</p>
                <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
                  <Clock className="h-4 w-4 mr-2" />
                  Awaiting Doctor's Assessment
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MedicalOpinion;
