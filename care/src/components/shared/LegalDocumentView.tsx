import React, { useState, useEffect } from 'react';
import { apiClient } from '@/services/api';
import TextViewer from '@/components/ui/text-viewer';
import { ArrowLeft, Edit, Clock, History } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { useAuth } from '@/hooks/useAuth';

// Type definitions
interface ConsentFormTemplate {
  id: string;
  title: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  creator?: {
    firstName: string;
    lastName: string;
  };
  versions: ConsentFormVersion[];
}

interface ConsentFormVersion {
  id: string;
  version: number;
  content: string;
  notes: string;
  isActive: boolean;
  createdAt: string;
  creator?: {
    firstName: string;
    lastName: string;
  };
}

interface LegalDocumentViewProps {
  documentId?: string;
  onBack?: () => void;
  showEditButton?: boolean;
  embedded?: boolean;
}

const LegalDocumentView: React.FC<LegalDocumentViewProps> = ({ 
  documentId, 
  onBack, 
  showEditButton = false, 
  embedded = false 
}) => {
  const { id: paramId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const id = documentId || paramId;
  
  const [selectedVersionId, setSelectedVersionId] = useState<string | null>(null);
  const [template, setTemplate] = useState<ConsentFormTemplate | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('preview');
  const [showVersionHistory, setShowVersionHistory] = useState<boolean>(false);
  
  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // @ts-ignore - apiClient.request is private but used throughout the codebase
        const response = await (apiClient as any).request(`/legal-compliance/templates/${id}`);
        
        if (response && (response as any).data) {
          setTemplate((response as any).data.template);
        } else {
          setError('Failed to load template data');
        }
      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error fetching consent form template:', err);
        setError('Failed to load template. Redirecting to document list...');
        
        // Redirect to list after a short delay
        setTimeout(() => {
          if (onBack) {
            onBack();
          } else {
            window.location.href = '/admin/legal-compliance';
          }
        }, 2000);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (id) {
      fetchTemplate();
    }
  }, [id]);
  
  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-4">
        <div className="border rounded-lg shadow-sm">
          <div className="pt-6 p-6 text-center">
            <p className="text-gray-500">{error}</p>
            <button 
              onClick={onBack || (() => navigate('/admin/legal-compliance'))}
              className="mt-4 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Back to Legal & Compliance
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  if (!template) {
    return (
      <div className="p-4">
        <div className="border rounded-lg shadow-sm">
          <div className="pt-6 p-6 text-center">
            <p className="text-gray-500">Document not found.</p>
            <button 
              onClick={onBack || (() => navigate('/admin/legal-compliance'))}
              className="mt-4 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Back to Legal & Compliance
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  const versions = template.versions || [];
  const latestVersion = versions.length > 0 ? versions[0] : null;
  const currentVersion = versions.find((v: ConsentFormVersion) => v.id.toString() === selectedVersionId) || latestVersion;
  
  // Role-based access control
  const isAdmin = user?.role === 'admin';
  const canViewAllVersions = isAdmin;
  
  // Debug logging
  )
  });
  
  // For non-admins, always show latest version unless a specific version is pre-selected
  const displayVersion = canViewAllVersions ? currentVersion : latestVersion;
  
  return (
    <div className="p-4">
      <div className="flex items-center gap-2 mb-6">
        {!embedded && (
          <button 
            onClick={onBack || (() => navigate('/admin/consent-forms'))}
            className="flex items-center gap-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
          >
            <ArrowLeft size={16} />
            Back to Legal & Compliance
          </button>
        )}
        <h1 className="text-2xl font-bold">
          {template.title}
        </h1>
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${template.isActive ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
          {template.isActive ? "Active" : "Inactive"}
        </span>
      </div>
      
      <div className="grid gap-6">
        {/* Template Information Card */}
        <div className="border rounded-lg shadow-sm">
          <div className="p-4 border-b">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-lg font-medium">Template Information</h2>
                <p className="text-sm text-gray-500">
                  Created by {template.creator?.firstName} {template.creator?.lastName} 
                  {' on '}{format(new Date(template.createdAt), 'PPP')}
                </p>
              </div>
              {showEditButton && (
                <button 
                  onClick={() => navigate(`/admin/legal-compliance/edit/${template.id}`)}
                  className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Edit size={16} />
                  Edit Document
                </button>
              )}
            </div>
          </div>
          <div className="p-4">
            <p className="text-gray-700">
              {template.description || "No description provided."}
            </p>
          </div>
        </div>
        
        {/* Content Card */}
        <div className="border rounded-lg shadow-sm">
          <div className="p-4 border-b">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium">Content</h2>
              <div className="flex items-center gap-2">
                {/* Show current version info */}
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock size={16} className="text-gray-500" />
                  <span>
                    Version {displayVersion?.version || 1} 
                    {displayVersion && ` (${format(new Date(displayVersion.createdAt), 'PP')})`}
                    {!canViewAllVersions && versions.length > 1 && " (Latest)"}
                  </span>
                </div>
                
                {/* Admin-only version history controls */}
                {canViewAllVersions && versions.length > 1 && (
                  <div className="flex items-center gap-2">
                    {!showVersionHistory ? (
                      <button
                        onClick={() => setShowVersionHistory(true)}
                        className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                      >
                        <History size={14} />
                        View All Versions ({versions.length})
                      </button>
                    ) : (
                      <div className="flex items-center gap-2">
                        <div className="relative">
                          <select
                            value={selectedVersionId || (latestVersion ? latestVersion.id.toString() : '')}
                            onChange={(e) => setSelectedVersionId(e.target.value)}
                            className="pl-3 pr-10 py-2 text-sm border rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-[200px]"
                          >
                            <option value="" disabled>Select version</option>
                            {versions.map((version: ConsentFormVersion) => (
                              <option key={version.id} value={version.id.toString()}>
                                Version {version.version} ({format(new Date(version.createdAt), 'PP')})
                              </option>
                            ))}
                          </select>
                          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                            <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            setShowVersionHistory(false);
                            setSelectedVersionId(null); // Reset to latest
                          }}
                          className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900"
                        >
                          ✕
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="p-4">
            {/* Tabs */}
            <div className="border-b mb-4">
              <div className="flex">
                <button 
                  onClick={() => setActiveTab('preview')}
                  className={`px-4 py-2 ${activeTab === 'preview' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  Preview
                </button>
                <button 
                  onClick={() => setActiveTab('details')}
                  className={`px-4 py-2 ${activeTab === 'details' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  Version Details
                </button>
              </div>
            </div>
            
            {/* Tab Content */}
            {activeTab === 'preview' && (
              <div>
                {displayVersion ? (
                  <TextViewer content={displayVersion.content} />
                ) : (
                  <p className="text-gray-500">No content available.</p>
                )}
              </div>
            )}
            
            {activeTab === 'details' && (
              <div>
                {displayVersion ? (
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium">Version {displayVersion.version}</h3>
                      <p className="text-sm text-gray-500">
                        Created {format(new Date(displayVersion.createdAt), 'PPP')} by{' '}
                        {displayVersion.creator?.firstName} {displayVersion.creator?.lastName}
                      </p>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium">Version Notes</h4>
                      <p className="text-sm mt-1">
                        {displayVersion.notes || "No notes provided for this version."}
                      </p>
                    </div>
                    
                    {displayVersion.isActive ? (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                        Active
                      </span>
                    ) : (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                        Inactive
                      </span>
                    )}
                    
                    {/* Show role-based access info */}
                    {!canViewAllVersions && versions.length > 1 && (
                      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-sm text-blue-800">
                          <strong>Note:</strong> You are viewing the latest version. 
                          Contact an administrator to access previous versions.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500">No version details available.</p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LegalDocumentView;
