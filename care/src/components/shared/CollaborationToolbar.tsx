import React from 'react';
import { Editor } from '@tiptap/react';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Table,
  Stethoscope,
  Pill,
  Activity,
  ClipboardList,
  Target,
  Undo,
  Redo,
  Type,
  AlignLeft,
  AlignCenter,
  AlignRight,
} from 'lucide-react';

interface CollaborationToolbarProps {
  editor: Editor;
  disabled?: boolean;
  className?: string;
}

interface ToolbarButton {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  action: () => void;
  isActive?: () => boolean;
  disabled?: boolean;
}

export const CollaborationToolbar: React.FC<CollaborationToolbarProps> = ({
  editor,
  disabled = false,
  className = '',
}) => {
  if (!editor) {
    return null;
  }

  const formatButtons: ToolbarButton[] = [
    {
      icon: Bold,
      title: 'Bold',
      action: () => editor.chain().focus().toggleBold().run(),
      isActive: () => editor.isActive('bold'),
    },
    {
      icon: Italic,
      title: 'Italic',
      action: () => editor.chain().focus().toggleItalic().run(),
      isActive: () => editor.isActive('italic'),
    },
    {
      icon: Underline,
      title: 'Underline',
      action: () => editor.chain().focus().toggleUnderline().run(),
      isActive: () => editor.isActive('underline'),
    },
  ];

  const structureButtons: ToolbarButton[] = [
    {
      icon: List,
      title: 'Bullet List',
      action: () => editor.chain().focus().toggleBulletList().run(),
      isActive: () => editor.isActive('bulletList'),
    },
    {
      icon: ListOrdered,
      title: 'Numbered List',
      action: () => editor.chain().focus().toggleOrderedList().run(),
      isActive: () => editor.isActive('orderedList'),
    },
    {
      icon: Quote,
      title: 'Quote',
      action: () => editor.chain().focus().toggleBlockquote().run(),
      isActive: () => editor.isActive('blockquote'),
    },
    {
      icon: Table,
      title: 'Insert Table',
      action: () => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run(),
    },
  ];

  const medicalButtons: ToolbarButton[] = [
    {
      icon: Stethoscope,
      title: 'Diagnosis Block (Cmd+Shift+D)',
      action: () => editor.chain().focus().insertMedicalBlock({ blockType: 'diagnosis' }).run(),
    },
    {
      icon: Pill,
      title: 'Medication Block (Cmd+Shift+M)',
      action: () => editor.chain().focus().insertMedicalBlock({ blockType: 'medication' }).run(),
    },
    {
      icon: Activity,
      title: 'Vital Signs Block (Cmd+Shift+V)',
      action: () => editor.chain().focus().insertMedicalBlock({ blockType: 'vitals' }).run(),
    },
    {
      icon: ClipboardList,
      title: 'Assessment Block (Cmd+Shift+A)',
      action: () => editor.chain().focus().insertMedicalBlock({ blockType: 'assessment' }).run(),
    },
    {
      icon: Target,
      title: 'Plan Block (Cmd+Shift+P)',
      action: () => editor.chain().focus().insertMedicalBlock({ blockType: 'plan' }).run(),
    },
  ];

  const historyButtons: ToolbarButton[] = [
    {
      icon: Undo,
      title: 'Undo',
      action: () => editor.chain().focus().undo().run(),
      disabled: !editor.can().undo(),
    },
    {
      icon: Redo,
      title: 'Redo',
      action: () => editor.chain().focus().redo().run(),
      disabled: !editor.can().redo(),
    },
  ];

  const headingButtons: ToolbarButton[] = [
    {
      icon: Type,
      title: 'Heading 1',
      action: () => editor.chain().focus().toggleHeading({ level: 1 }).run(),
      isActive: () => editor.isActive('heading', { level: 1 }),
    },
    {
      icon: Type,
      title: 'Heading 2',
      action: () => editor.chain().focus().toggleHeading({ level: 2 }).run(),
      isActive: () => editor.isActive('heading', { level: 2 }),
    },
    {
      icon: Type,
      title: 'Heading 3',
      action: () => editor.chain().focus().toggleHeading({ level: 3 }).run(),
      isActive: () => editor.isActive('heading', { level: 3 }),
    },
  ];

  const renderButtonGroup = (buttons: ToolbarButton[], groupName: string) => (
    <div key={groupName} className="flex items-center space-x-1 px-2 border-r border-gray-300 last:border-r-0">
      {buttons.map((button, index) => {
        const Icon = button.icon;
        const isActive = button.isActive?.() || false;
        const isDisabled = disabled || button.disabled || false;

        return (
          <button
            key={index}
            onClick={button.action}
            disabled={isDisabled}
            title={button.title}
            className={`
              p-2 rounded-md transition-colors duration-200
              ${isActive 
                ? 'bg-blue-100 text-blue-700 border border-blue-300' 
                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }
              ${isDisabled 
                ? 'opacity-50 cursor-not-allowed' 
                : 'cursor-pointer'
              }
            `}
          >
            <Icon className="h-4 w-4" />
          </button>
        );
      })}
    </div>
  );

  return (
    <div className={`collaboration-toolbar bg-white border border-gray-300 rounded-lg p-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {renderButtonGroup(historyButtons, 'history')}
          {renderButtonGroup(headingButtons, 'headings')}
          {renderButtonGroup(formatButtons, 'format')}
          {renderButtonGroup(structureButtons, 'structure')}
          {renderButtonGroup(medicalButtons, 'medical')}
        </div>

        {/* Medical terminology help */}
        <div className="text-xs text-gray-500 hidden md:block">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-1">@snomed:</span>
              Clinical terms
            </span>
            <span className="flex items-center">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs mr-1">@med:</span>
              Medications
            </span>
          </div>
        </div>
      </div>

      {/* Mobile help text */}
      <div className="mt-2 text-xs text-gray-500 md:hidden">
        <p>Use @snomed: for clinical terms, @med: for medications</p>
      </div>
    </div>
  );
};
