import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import { 
  Calendar,
  Clock,
  User,
  Video,
  MapPin,
  List,
  CalendarDays,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { statusColors, appointmentTypeColors } from '@/constants/theme'

interface Appointment {
  id: string
  patientId: string
  doctorId: string
  caseId?: string
  appointmentType: 'consultation' | 'follow_up' | 'review' | 'emergency'
  scheduledAt: string
  duration: number
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
  notes?: string
  meetingLink?: string
  createdAt: string
  updatedAt: string
  // Joined data
  doctor?: {
    id: string
    name: string
    specialization?: string
    profileImage?: string
  }
  patient?: {
    id: string
    name: string
    email?: string
    profileImage?: string
  }
  case?: {
    id: string
    title: string
    description: string
    status: string
    urgencyLevel: string
    specialtyRequired: string
  }
}

interface AppointmentsProps {
  // Optional props for different contexts
  userId?: string // If provided, filter appointments for specific user
  userRole?: 'patient' | 'doctor' | 'admin' | 'agent'
  caseId?: string // If provided, filter appointments for specific case
  showCreateButton?: boolean
  onAppointmentClick?: (appointmentId: string) => void
  title?: string
  description?: string
}

const statusConfig = {
  scheduled: { label: 'Scheduled', color: statusColors.scheduled, icon: Clock },
  confirmed: { label: 'Confirmed', color: statusColors.confirmed, icon: CheckCircle },
  in_progress: { label: 'In Progress', color: statusColors.in_progress, icon: Clock },
  completed: { label: 'Completed', color: statusColors.completed, icon: CheckCircle },
  cancelled: { label: 'Cancelled', color: statusColors.cancelled, icon: XCircle },
  no_show: { label: 'No Show', color: statusColors.no_show, icon: AlertCircle }
}

const typeConfig = {
  consultation: { label: 'Consultation', color: appointmentTypeColors.consultation },
  follow_up: { label: 'Follow-up', color: appointmentTypeColors.follow_up },
  review: { label: 'Review', color: appointmentTypeColors.review },
  emergency: { label: 'Emergency', color: appointmentTypeColors.emergency }
}

export function Appointments({
  userId,
  userRole,
  caseId,
  onAppointmentClick,
  title = "Appointments",
  description = "View and manage your appointments"
}: AppointmentsProps) {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('list')
  const [currentDate, setCurrentDate] = useState(new Date())

  // Determine effective user role and ID
  const effectiveUserRole = userRole || user?.role
  const effectiveUserId = userId || user?.id

  // Load appointments
  useEffect(() => {
    const loadAppointments = async () => {
      try {
        setLoading(true)
        setError(null)
        
        let response
        
        if (caseId) {
          // Load appointments for specific case
          // @ts-ignore - Using private request method for case appointments
          response = await (apiClient as any).request(`/cases/${caseId}/appointments`)
        } else if (userId && effectiveUserRole === 'admin') {
          // Admin viewing specific user's appointments
          // @ts-ignore - Using private request method for user appointments
          response = await (apiClient as any).request(`/appointments?userId=${userId}`)
        } else {
          // Default: get appointments for current user
          response = await apiClient.getAppointments()
        }
        
        // Handle response format
        let appointmentsData: Appointment[] = []
        if (Array.isArray(response)) {
          appointmentsData = response
        } else if (response && (response as any).appointments) {
          appointmentsData = (response as any).appointments
        } else if (response && (response as any).data) {
          appointmentsData = (response as any).data
        }
        
        setAppointments(appointmentsData)
        
      } catch (error: any) {
        // TODO: Replace with proper error reporting
  console.error('Error loading appointments:', error)
        setError('Failed to load appointments. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    if (effectiveUserId) {
      loadAppointments()
    }
  }, [effectiveUserId, userId, caseId, effectiveUserRole])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Group appointments by date for calendar view
  const groupAppointmentsByDate = () => {
    const grouped: { [key: string]: Appointment[] } = {}
    appointments.forEach(appointment => {
      const date = new Date(appointment.scheduledAt).toDateString()
      if (!grouped[date]) {
        grouped[date] = []
      }
      grouped[date].push(appointment)
    })
    return grouped
  }

  // Get appointments for current month (calendar view)
  // const getMonthAppointments = () => {
  //   const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
  //   const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
  //   
  //   return appointments.filter(appointment => {
  //     const appointmentDate = new Date(appointment.scheduledAt)
  //     return appointmentDate >= startOfMonth && appointmentDate <= endOfMonth
  //   })
  // }

  // Generate calendar days
  const generateCalendarDays = () => {
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
    // const _endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
    const startOfCalendar = new Date(startOfMonth)
    startOfCalendar.setDate(startOfCalendar.getDate() - startOfMonth.getDay())
    
    const days = []
    // const _monthAppointments = getMonthAppointments()
    const groupedAppointments = groupAppointmentsByDate()
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startOfCalendar)
      date.setDate(startOfCalendar.getDate() + i)
      
      const dateString = date.toDateString()
      const dayAppointments = groupedAppointments[dateString] || []
      
      days.push({
        date,
        appointments: dayAppointments,
        isCurrentMonth: date.getMonth() === currentDate.getMonth(),
        isToday: dateString === new Date().toDateString()
      })
    }
    
    return days
  }

  // Navigate calendar
  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(currentDate.getMonth() - 1)
    } else {
      newDate.setMonth(currentDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  // Handle double-click to view appointment details
  const handleAppointmentDoubleClick = (appointmentId: string) => {
    if (onAppointmentClick) {
      onAppointmentClick(appointmentId)
    } else {
      // Default navigation based on user role
      if (effectiveUserRole === 'patient') {
        navigate(`/appointments/${appointmentId}`)
      } else if (effectiveUserRole === 'doctor') {
        navigate(`/doctor/appointments/${appointmentId}`)
      } else {
        navigate(`/admin/appointments/${appointmentId}`)
      }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Appointments</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-8 sm:p-8">
              <div className="text-center">
                <h1 className="text-3xl font-bold text-gray-900 mb-3">
                  📅 {title}
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed mb-6">{description}</p>
                <div className="flex items-center justify-center space-x-4">
                  <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-2">
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'list'
                          ? 'bg-white text-green-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="List view"
                    >
                      <List className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setViewMode('calendar')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'calendar'
                          ? 'bg-white text-green-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="Calendar view"
                    >
                      <CalendarDays className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          {appointments.length === 0 ? (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="text-center py-16 px-8">
                <div className="mb-6">
                  <Calendar className="h-20 w-20 text-green-300 mx-auto mb-4" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  📅 No appointments found
                </h3>
                <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                  {caseId 
                    ? "No appointments are scheduled for this case yet. Check back later or contact your doctor."
                    : "You don't have any appointments scheduled yet. Your upcoming consultations will appear here."
                  }
                </p>
                <div className="mt-8 text-sm text-gray-500">
                  <p>👨‍⚕️ Your healthcare appointments and consultations will be displayed here</p>
                </div>
              </div>
            </div>
        ) : (
          <>
            {viewMode === 'list' ? (
              <AppointmentsList
                appointments={appointments}
                formatDate={formatDate}
                formatTime={formatTime}
                formatDateTime={formatDateTime}
                onDoubleClick={handleAppointmentDoubleClick}
                userRole={effectiveUserRole}
                navigate={navigate}
              />
            ) : (
              <AppointmentsCalendar
                appointments={appointments}
                currentDate={currentDate}
                onNavigateMonth={navigateMonth}
                generateCalendarDays={generateCalendarDays}
                formatTime={formatTime}
                onDoubleClick={handleAppointmentDoubleClick}
              />
            )}
          </>
        )}
        </div>
      </div>
    </div>
  )
}

// List View Component
function AppointmentsList({ 
  appointments, 
  formatDate, 
  formatTime, 
  formatDateTime: _formatDateTime,
  onDoubleClick,
  userRole,
  navigate
}: { 
  appointments: Appointment[]
  formatDate: (dateString: string) => string
  formatTime: (dateString: string) => string
  formatDateTime: (dateString: string) => string
  onDoubleClick: (appointmentId: string) => void
  userRole?: string
  navigate: (path: string) => void
}) {
  // Sort appointments by date (upcoming first)
  const sortedAppointments = [...appointments].sort((a, b) => 
    new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime()
  )

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
      {sortedAppointments.map((appointment) => {
        const StatusIcon = statusConfig[appointment.status as keyof typeof statusConfig].icon
        const isUpcoming = new Date(appointment.scheduledAt) > new Date()
        
        return (
          <div
            key={appointment.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
            onDoubleClick={() => onDoubleClick(appointment.id)}
            title="Double-click to view details"
          >
            {/* Header with status and type badges */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusConfig[appointment.status as keyof typeof statusConfig].color}`}>
                  <StatusIcon className="h-3 w-3 mr-1" />
                  {statusConfig[appointment.status as keyof typeof statusConfig].label}
                </span>
              </div>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${typeConfig[appointment.appointmentType as keyof typeof typeConfig].color}`}>
                {typeConfig[appointment.appointmentType as keyof typeof typeConfig].label}
              </span>
            </div>

            {/* Date and time */}
            <div className="space-y-2 mb-3">
              <div className="flex items-center text-sm text-gray-700">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span className="font-medium">{formatDate(appointment.scheduledAt)}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-2 text-gray-500" />
                {formatTime(appointment.scheduledAt)} • {appointment.duration} min
              </div>
            </div>
            
            {/* Doctor/Patient info */}
            {userRole !== 'patient' && appointment.patient && (
              <div className="flex items-center text-sm text-gray-600 mb-3">
                <User className="h-4 w-4 mr-2 text-gray-500" />
                <span className="truncate">{appointment.patient.name}</span>
              </div>
            )}
            
            {userRole === 'patient' && appointment.doctor && (
              <div className="mb-3">
                <div className="flex items-center text-sm text-gray-700">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  <span className="font-medium truncate">Dr. {appointment.doctor.name}</span>
                </div>
                {appointment.doctor.specialization && (
                  <div className="text-xs text-gray-500 ml-6 truncate">
                    {appointment.doctor.specialization}
                  </div>
                )}
              </div>
            )}
                    
                    {appointment.case && (
                      <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center text-sm font-medium text-blue-900 mb-1">
                              <MapPin className="h-4 w-4 mr-2" />
                              Related Case
                            </div>
                            <div className="text-sm text-blue-800 font-medium mb-1">
                              {appointment.case.title}
                            </div>
                            {appointment.case.description && (
                              <div className="text-xs text-blue-700 mb-2 line-clamp-2">
                                {appointment.case.description}
                              </div>
                            )}
                            <div className="flex items-center space-x-2">
                              {appointment.case.status && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  {appointment.case.status}
                                </span>
                              )}
                              {appointment.case.urgencyLevel && (
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  appointment.case.urgencyLevel === 'urgent' ? 'bg-red-100 text-red-800' :
                                  appointment.case.urgencyLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                                  appointment.case.urgencyLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-green-100 text-green-800'
                                }`}>
                                  {appointment.case.urgencyLevel}
                                </span>
                              )}
                            </div>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              // Navigate to case details using unified route
                              navigate(`/cases/${appointment.case?.id}`)
                            }}
                            className="text-blue-600 hover:text-blue-800 text-xs font-medium underline"
                          >
                            View Case
                          </button>
                        </div>
                      </div>
                    )}
            
            {/* Meeting link and notes */}
            <div className="mt-3">
              {appointment.meetingLink && isUpcoming && appointment.status !== 'cancelled' && (
                <div className="mb-4">
                  <a
                    href={appointment.meetingLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Video className="h-4 w-4 mr-2" />
                    Join Call
                  </a>
                </div>
              )}
              
              {appointment.notes && (
                <div className="text-sm text-gray-600">
                  <strong>Notes:</strong> {appointment.notes}
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

// Calendar View Component
function AppointmentsCalendar({ 
  appointments: _appointments, 
  currentDate, 
  onNavigateMonth, 
  generateCalendarDays,
  formatTime,
  onDoubleClick
}: { 
  appointments: Appointment[]
  currentDate: Date
  onNavigateMonth: (direction: 'prev' | 'next') => void
  generateCalendarDays: () => any[]
  formatTime: (dateString: string) => string
  onDoubleClick: (appointmentId: string) => void
}) {
  const calendarDays = generateCalendarDays()
  const monthYear = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">{monthYear}</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onNavigateMonth('prev')}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft className="h-5 w-5 text-gray-600" />
          </button>
          <button
            onClick={() => onNavigateMonth('next')}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
          >
            <ChevronRight className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-6">
        {/* Day Headers */}
        <div className="grid grid-cols-7 gap-1 mb-4">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => (
            <div
              key={index}
              className={`min-h-[100px] p-2 border border-gray-100 rounded-lg ${
                day.isCurrentMonth ? 'bg-white' : 'bg-gray-50'
              } ${day.isToday ? 'ring-2 ring-primary-500' : ''}`}
            >
              <div className={`text-sm font-medium mb-1 ${
                day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
              } ${day.isToday ? 'text-primary-600' : ''}`}>
                {day.date.getDate()}
              </div>
              
              {day.appointments.length > 0 && (
                <div className="space-y-1">
                  {day.appointments.slice(0, 2).map((appointment: Appointment) => (
                    <div
                      key={appointment.id}
                      className={`text-xs p-1 rounded truncate cursor-pointer hover:opacity-80 ${statusConfig[appointment.status as keyof typeof statusConfig].color}`}
                      title={`${formatTime(appointment.scheduledAt)} - ${typeConfig[appointment.appointmentType as keyof typeof typeConfig].label} (Double-click to view details)`}
                      onDoubleClick={() => onDoubleClick(appointment.id)}
                    >
                      {formatTime(appointment.scheduledAt)}
                    </div>
                  ))}
                  {day.appointments.length > 2 && (
                    <div className="text-xs text-gray-500">
                      +{day.appointments.length - 2} more
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Appointments
