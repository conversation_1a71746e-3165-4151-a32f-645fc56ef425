import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewContent } from '@tiptap/react';
import { 
  Stethoscope, 
  Pill, 
  Activity, 
  ClipboardList, 
  Target,
  Plus,
  X,
  Edit3
} from 'lucide-react';

interface MedicalCode {
  system: 'snomed' | 'rxnav';
  code: string;
  display: string;
}

interface MedicalBlockProps {
  node: {
    attrs: {
      blockType: 'diagnosis' | 'medication' | 'vitals' | 'assessment' | 'plan';
      data: Record<string, any>;
      medicalCodes: MedicalCode[];
    };
  };
  updateAttributes: (attributes: Record<string, any>) => void;
  deleteNode: () => void;
  selected: boolean;
}

const blockConfig = {
  diagnosis: {
    icon: Stethoscope,
    title: 'Diagnosis',
    color: 'blue',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    iconColor: 'text-blue-600',
  },
  medication: {
    icon: Pill,
    title: 'Medication',
    color: 'green',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    iconColor: 'text-green-600',
  },
  vitals: {
    icon: Activity,
    title: 'Vital Signs',
    color: 'red',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    iconColor: 'text-red-600',
  },
  assessment: {
    icon: ClipboardList,
    title: 'Assessment',
    color: 'purple',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    iconColor: 'text-purple-600',
  },
  plan: {
    icon: Target,
    title: 'Plan',
    color: 'orange',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    iconColor: 'text-orange-600',
  },
};

export const MedicalBlockComponent: React.FC<MedicalBlockProps> = ({
  node,
  updateAttributes,
  deleteNode,
  selected,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showCodes, setShowCodes] = useState(false);
  
  const { blockType, data, medicalCodes } = node.attrs;
  const config = blockConfig[blockType];
  const Icon = config.icon;

  const addMedicalCode = (code: MedicalCode) => {
    const updatedCodes = [...medicalCodes, code];
    updateAttributes({ medicalCodes: updatedCodes });
  };

  const removeMedicalCode = (index: number) => {
    const updatedCodes = medicalCodes.filter((_, i) => i !== index);
    updateAttributes({ medicalCodes: updatedCodes });
  };

  const updateBlockData = (key: string, value: any) => {
    updateAttributes({
      data: {
        ...data,
        [key]: value,
      },
    });
  };

  return (
    <NodeViewWrapper
      className={`medical-block-wrapper ${selected ? 'ring-2 ring-blue-500' : ''} mb-4`}
    >
      <div className={`medical-block border-2 rounded-lg ${config.bgColor} ${config.borderColor}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Icon className={`h-5 w-5 ${config.iconColor}`} />
            <h3 className="font-medium text-gray-900">{config.title}</h3>
            {medicalCodes.length > 0 && (
              <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
                {medicalCodes.length} code{medicalCodes.length > 1 ? 's' : ''}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setShowCodes(!showCodes)}
              className="p-1 text-gray-500 hover:text-gray-700 rounded"
              title="Toggle medical codes"
            >
              <Edit3 className="h-4 w-4" />
            </button>
            <button
              onClick={deleteNode}
              className="p-1 text-gray-500 hover:text-red-600 rounded"
              title="Delete block"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Medical Codes Section */}
        {showCodes && (
          <div className="p-3 border-b border-gray-200 bg-white">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Medical Codes</h4>
              <button
                onClick={() => {
                  // This would trigger a modal or inline form to add codes
                  // For now, we'll add a placeholder
                  const newCode: MedicalCode = {
                    system: 'snomed',
                    code: 'placeholder',
                    display: 'Placeholder code',
                  };
                  addMedicalCode(newCode);
                }}
                className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Code
              </button>
            </div>
            
            {medicalCodes.length === 0 ? (
              <p className="text-xs text-gray-500 italic">
                No medical codes assigned. Use @snomed: or @med: in the content to add codes.
              </p>
            ) : (
              <div className="space-y-2">
                {medicalCodes.map((code, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs"
                  >
                    <div>
                      <span className={`inline-block px-2 py-1 rounded text-white text-xs mr-2 ${
                        code.system === 'snomed' ? 'bg-blue-500' : 'bg-green-500'
                      }`}>
                        {code.system.toUpperCase()}
                      </span>
                      <span className="font-mono">{code.code}</span>
                      <span className="ml-2 text-gray-600">{code.display}</span>
                    </div>
                    <button
                      onClick={() => removeMedicalCode(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Block-specific content */}
        {blockType === 'vitals' && (
          <div className="p-3 border-b border-gray-200 bg-white">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  BP (mmHg)
                </label>
                <input
                  type="text"
                  placeholder="120/80"
                  value={data.bloodPressure || ''}
                  onChange={(e) => updateBlockData('bloodPressure', e.target.value)}
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  HR (bpm)
                </label>
                <input
                  type="number"
                  placeholder="72"
                  value={data.heartRate || ''}
                  onChange={(e) => updateBlockData('heartRate', e.target.value)}
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Temp (°F)
                </label>
                <input
                  type="number"
                  step="0.1"
                  placeholder="98.6"
                  value={data.temperature || ''}
                  onChange={(e) => updateBlockData('temperature', e.target.value)}
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  O2 Sat (%)
                </label>
                <input
                  type="number"
                  placeholder="98"
                  value={data.oxygenSaturation || ''}
                  onChange={(e) => updateBlockData('oxygenSaturation', e.target.value)}
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                />
              </div>
            </div>
          </div>
        )}

        {blockType === 'medication' && (
          <div className="p-3 border-b border-gray-200 bg-white">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Dosage
                </label>
                <input
                  type="text"
                  placeholder="10mg"
                  value={data.dosage || ''}
                  onChange={(e) => updateBlockData('dosage', e.target.value)}
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Frequency
                </label>
                <input
                  type="text"
                  placeholder="BID"
                  value={data.frequency || ''}
                  onChange={(e) => updateBlockData('frequency', e.target.value)}
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Route
                </label>
                <select
                  value={data.route || ''}
                  onChange={(e) => updateBlockData('route', e.target.value)}
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                >
                  <option value="">Select route</option>
                  <option value="PO">PO (Oral)</option>
                  <option value="IV">IV (Intravenous)</option>
                  <option value="IM">IM (Intramuscular)</option>
                  <option value="SC">SC (Subcutaneous)</option>
                  <option value="SL">SL (Sublingual)</option>
                  <option value="TOP">TOP (Topical)</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Content area */}
        <div className="p-3">
          <NodeViewContent className="medical-block-content prose prose-sm max-w-none" />
        </div>

        {/* Usage hint */}
        <div className="px-3 pb-3">
          <p className="text-xs text-gray-500 italic">
            💡 Use @snomed: to search clinical terms or @med: to search medications
          </p>
        </div>
      </div>
    </NodeViewWrapper>
  );
};
