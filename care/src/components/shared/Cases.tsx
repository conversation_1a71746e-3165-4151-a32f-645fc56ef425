import { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Search,
  User,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
  Plus,
  RefreshCw,
  Grid3X3,
  List,
  Kanban
} from 'lucide-react'
import { CasesCardView } from './CasesCardView'
import { CasesListView } from './CasesListView'
import { CasesKanbanView } from './CasesKanbanView'
import { statusColors, urgencyColors } from '@/shared/constants/theme'

// Define types
interface Case {
  id: string
  title: string
  description: string
  status: 'draft' | 'submitted' | 'assigned' | 'in_review' | 'completed'
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  patientId: string
  patientName?: string
  // Note: assignedDoctorId has been removed in favor of multi-doctor assignment system
  // Doctors are now assigned via the case_doctors table
  assignedDoctorName?: string
  specialtyRequired?: string
  createdAt: string
  updatedAt: string
  // Doctor-specific fields for per-doctor acceptance status
  doctorAcceptanceStatus?: 'pending' | 'accepted' | 'declined' | 'closed'
  doctorAcceptedAt?: string
}

interface Doctor {
  id: string
  name: string
  specialty: string
  email: string
}

// Status and urgency configurations for consistent UI
const statusConfig = {
  draft: { label: 'Draft', color: statusColors.draft, icon: FileText },
  submitted: { label: 'Submitted', color: statusColors.submitted, icon: Clock },
  assigned: { label: 'Assigned', color: statusColors.assigned, icon: User },
  in_review: { label: 'In Review', color: statusColors.in_review, icon: Eye },
  completed: { label: 'Completed', color: statusColors.completed, icon: CheckCircle }
}

const urgencyConfig = {
  low: { label: 'Low', color: urgencyColors.low },
  medium: { label: 'Medium', color: urgencyColors.medium },
  high: { label: 'High', color: urgencyColors.high },
  urgent: { label: 'Urgent', color: urgencyColors.urgent }
}

export function Cases() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const [cases, setCases] = useState<Case[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [urgencyFilter, setUrgencyFilter] = useState<string>('all')
  const [specialtyFilter, setSpecialtyFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [specialties, setSpecialties] = useState<string[]>([])
  
  // View mode state (kanban, card, list)
  const [viewMode, setViewMode] = useState<'kanban' | 'card' | 'list'>(
    user?.role === 'admin' ? 'kanban' : 'card'
  )
  


  // Load cases from API
  useEffect(() => {
    const loadCases = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        let response
        if (user?.role === 'doctor') {
          // For doctors, get only their assigned cases
          response = await apiClient.getDoctorCases()
          setCases(response || [])
        } else {
          // For other roles, use the general getCases method
          response = await apiClient.getCases({
            status: statusFilter !== 'all' ? statusFilter : undefined,
            priority: urgencyFilter !== 'all' ? urgencyFilter : undefined,
            search: searchTerm || undefined
          })
          setCases(response.data || [])
        }
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load cases:', error)
        setError('Failed to load cases. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    loadCases()
  }, [statusFilter, urgencyFilter, searchTerm, user?.role])

  // Get current section filter from URL hash for doctors
  const getCurrentSectionFilter = () => {
    if (user?.role !== 'doctor') return 'all'
    
    const hash = location.hash.substring(1) // Remove the '#'
    switch (hash) {
      case 'inbox':
        return 'pending'
      case 'workload':
        return 'accepted'
      case 'completed':
        return 'closed'
      default:
        return 'all' // Show all sections when no hash or invalid hash
    }
  }

  const sectionFilter = getCurrentSectionFilter()

  // Load specialties for admin/agent filtering
  useEffect(() => {
    if (user?.role === 'admin' || user?.role === 'agent') {
      const loadSpecialties = async () => {
        try {
          const response = await apiClient.getDoctors()
          
          // Extract unique specialties
          const uniqueSpecialties = Array.from(
            new Set((response as any).data?.map((doctor: Doctor) => doctor.specialty) || [])
          )
          setSpecialties(uniqueSpecialties as string[])
        } catch (error) {
          // TODO: Replace with proper error reporting
  console.error('Failed to load specialties:', error)
        }
      }
      
      loadSpecialties()
    }
  }, [user?.role])

  // Handle case detail view - navigate to unified case detail page
  const handleCaseClick = (caseId: string) => {
    navigate(`/cases/${caseId}`)
  }

  // Handle case acceptance for doctors
  const handleAcceptCase = async (caseId: string) => {
    try {
      // Use the specific acceptCase endpoint for doctors
      await apiClient.acceptCase(caseId)
      
      // Refresh cases after acceptance
      let response
      if (user?.role === 'doctor') {
        response = await apiClient.getDoctorCases()
        setCases(response || [])
      } else {
        response = await apiClient.getCases({
          status: statusFilter !== 'all' ? statusFilter : undefined,
          priority: urgencyFilter !== 'all' ? urgencyFilter : undefined,
          search: searchTerm || undefined
        })
        setCases(response.data || [])
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to accept case:', error)
      setError('Failed to accept case. Please try again.')
    }
  }

  // Filter cases based on search and filters
  const filteredCases = user?.role === 'doctor'
    ? cases // Show all cases for doctors, we'll filter by section below
    : cases.filter(case_ => {
        // Search term filter
        const matchesSearch = searchTerm
          ? case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            case_.description.toLowerCase().includes(searchTerm.toLowerCase())
          : true
        
        // Status filter
        const matchesStatus = statusFilter === 'all' ? true : case_.status === statusFilter
        
        // Urgency filter
        const matchesUrgency = urgencyFilter === 'all' ? true : case_.urgencyLevel === urgencyFilter
        
        // Specialty filter (admin/agent only)
        const matchesSpecialty = specialtyFilter === 'all'
          ? true
          : case_.specialtyRequired === specialtyFilter
        
        return matchesSearch && matchesStatus && matchesUrgency &&
               (user?.role === 'admin' || user?.role === 'agent' ? matchesSpecialty : true)
      })

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-8 sm:p-8">
              <div className="text-center">
                <h1 className="text-3xl font-bold text-gray-900 mb-3">
                {user?.role === 'patient' 
                    ? '                  🏥 My Requests'
                    : '                  🏥 My Cases'
                  }
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed mb-6">
                  {user?.role === 'patient' 
                    ? 'Track your medical consultations and get expert second opinions'
                    : 'Manage and review medical cases'
                  }
                </p>
                
                {user?.role === 'patient' && (
                  <button
                    onClick={() => navigate('/patient/cases/new')}
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-xl rounded-xl hover:from-blue-700 hover:to-purple-700 cursor-pointer transition-all duration-200 transform hover:scale-105 shadow-lg"
                  >
                    <Plus className="h-6 w-6 mr-3" />
                    🎆 Start New Request
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Filters and Search - Hidden for doctors since they only see assigned cases */}
          {user?.role !== 'doctor' && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="px-6 py-6">
                <div className="flex flex-col lg:flex-row gap-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <input
                        type="text"
                        placeholder="🔍 Search your cases..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-12 pr-4 py-3 w-full text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-8 py-3 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                    >
                      <option value="all">📋 All Statuses</option>
                      <option value="draft">📝 Draft</option>
                      <option value="submitted">📤 Submitted</option>
                      <option value="assigned">👨‍⚕️ Assigned</option>
                      <option value="in_review">🔍 In Review</option>
                      <option value="completed">✅ Completed</option>
                    </select>
                    
                    <select
                      value={urgencyFilter}
                      onChange={(e) => setUrgencyFilter(e.target.value)}
                      className="px-8 py-3 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                    >
                      <option value="all">⚡ All Urgencies</option>
                      <option value="low">🟢 Low</option>
                      <option value="medium">🟡 Medium</option>
                      <option value="high">🟠 High</option>
                      <option value="urgent">🔴 Urgent</option>
                    </select>
                    
                    {(user?.role === 'admin' || user?.role === 'agent') && (
                      <select
                        value={specialtyFilter}
                        onChange={(e) => setSpecialtyFilter(e.target.value)}
                        className="px-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                      >
                        <option value="all">🏥 All Specialties</option>
                        {specialties.map(specialty => (
                          <option key={specialty} value={specialty}>{specialty}</option>
                        ))}
                      </select>
                    )}
                  </div>
                  
                  {/* View Mode Toggle */}
                  <div className="flex items-center justify-center">
                    <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-2">
                      <button
                        onClick={() => setViewMode('kanban')}
                        className={`p-3 rounded-lg transition-all duration-200 ${
                          viewMode === 'kanban'
                            ? 'bg-white text-blue-600 shadow-md transform scale-105'
                            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                        }`}
                        title="Kanban View"
                      >
                        <Kanban className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => setViewMode('card')}
                        className={`p-3 rounded-lg transition-all duration-200 ${
                          viewMode === 'card'
                            ? 'bg-white text-blue-600 shadow-md transform scale-105'
                            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                        }`}
                        title="Card View"
                      >
                        <Grid3X3 className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`p-3 rounded-lg transition-all duration-200 ${
                          viewMode === 'list'
                            ? 'bg-white text-blue-600 shadow-md transform scale-105'
                            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                        }`}
                        title="List View"
                      >
                        <List className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Doctor case categories */}
          {user?.role === 'doctor' && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Your Cases</h3>
                    <p className="text-sm text-gray-600">Assigned cases and current workload</p>
                  </div>
                  
                  {/* View Mode Toggle for doctors */}
                  <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-2">
                    <button
                      onClick={() => setViewMode('card')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'card'
                          ? 'bg-white text-blue-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="Card View"
                    >
                      <Grid3X3 className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'list'
                          ? 'bg-white text-blue-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="List View"
                    >
                      <List className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Loading and Error States */}
          {isLoading && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="flex justify-center items-center py-16">
                <div className="text-center">
                  <RefreshCw className="animate-spin h-12 w-12 text-blue-500 mx-auto mb-4" />
                  <p className="text-xl text-gray-600 font-medium">Loading your cases...</p>
                </div>
              </div>
            </div>
          )}
          
          {error && (
            <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-red-800 mb-1">⚠️ Something went wrong</h3>
                  <p className="text-red-700 text-base leading-relaxed">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* View content based on selected view mode */}
          <div className="mt-6">
            {!isLoading && !error && filteredCases.length === 0 && (
              <div className="bg-white shadow-lg rounded-xl border border-gray-100">
                <div className="text-center py-16 px-8">
                  <div className="mb-6">
                    <FileText className="h-20 w-20 text-blue-300 mx-auto mb-4" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    📋 No Requests found
                  </h3>
                  <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                    {searchTerm || statusFilter !== 'all' || urgencyFilter !== 'all' || specialtyFilter !== 'all'
                      ? 'Try adjusting your filters or search term to find what you\'re looking for'
                      : user?.role === 'patient'
                      ? 'Ready to get expert medical opinions? Create your first request to connect with specialists'
                      : 'No cases are available for you at this time'}
                  </p>
                  <div className="mt-8 text-sm text-gray-500">
                    <p>👨‍⚕️ Get expert medical opinions from board-certified specialists</p>
                  </div>
                </div>
              </div>
            )}
        
        {!isLoading && !error && filteredCases.length > 0 && (
          <>
            {user?.role === 'doctor' ? (
              // Doctor-specific view with section-based filtering
              <div className="space-y-8">
                {/* Show section based on URL hash filter */}
                {(sectionFilter === 'all' || sectionFilter === 'pending') && (() => {
                  const inboxCases = filteredCases.filter(case_ => case_.doctorAcceptanceStatus === 'pending')
                  return inboxCases.length > 0 && (
                    <div>
                      <div className="mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          📥 Inbox ({inboxCases.length})
                        </h3>
                        <p className="text-gray-600">New cases assigned to you awaiting acceptance</p>
                      </div>
                      {viewMode === 'card' && (
                        <CasesCardView
                          cases={inboxCases}
                          statusConfig={statusConfig}
                          urgencyConfig={urgencyConfig}
                          onCaseClick={handleCaseClick}
                          userRole={user?.role || ''}
                          onAcceptCase={handleAcceptCase}
                        />
                      )}
                      {viewMode === 'list' && (
                        <CasesListView
                          cases={inboxCases}
                          statusConfig={statusConfig}
                          urgencyConfig={urgencyConfig}
                          onCaseClick={handleCaseClick}
                          userRole={user?.role || ''}
                        />
                      )}
                    </div>
                  )
                })()}

                {(sectionFilter === 'all' || sectionFilter === 'accepted') && (() => {
                  const workloadCases = filteredCases.filter(case_ => case_.doctorAcceptanceStatus === 'accepted')
                  return workloadCases.length > 0 && (
                    <div>
                      <div className="mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          🏥 Current Workload ({workloadCases.length})
                        </h3>
                        <p className="text-gray-600">Cases you've accepted and are actively working on</p>
                      </div>
                      {viewMode === 'card' && (
                        <CasesCardView
                          cases={workloadCases}
                          statusConfig={statusConfig}
                          urgencyConfig={urgencyConfig}
                          onCaseClick={handleCaseClick}
                          userRole={user?.role || ''}
                          onAcceptCase={undefined} // No accept button for already accepted cases
                        />
                      )}
                      {viewMode === 'list' && (
                        <CasesListView
                          cases={workloadCases}
                          statusConfig={statusConfig}
                          urgencyConfig={urgencyConfig}
                          onCaseClick={handleCaseClick}
                          userRole={user?.role || ''}
                        />
                      )}
                    </div>
                  )
                })()}

                {(sectionFilter === 'all' || sectionFilter === 'closed') && (() => {
                  const completedCases = filteredCases.filter(case_ => case_.doctorAcceptanceStatus === 'closed')
                  return completedCases.length > 0 && (
                    <div>
                      <div className="mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          ✅ Completed Opinions ({completedCases.length})
                        </h3>
                        <p className="text-gray-600">Cases where you've delivered your medical opinion</p>
                      </div>
                      {viewMode === 'card' && (
                        <CasesCardView
                          cases={completedCases}
                          statusConfig={statusConfig}
                          urgencyConfig={urgencyConfig}
                          onCaseClick={handleCaseClick}
                          userRole={user?.role || ''}
                          onAcceptCase={undefined} // No accept button for completed cases
                        />
                      )}
                      {viewMode === 'list' && (
                        <CasesListView
                          cases={completedCases}
                          statusConfig={statusConfig}
                          urgencyConfig={urgencyConfig}
                          onCaseClick={handleCaseClick}
                          userRole={user?.role || ''}
                        />
                      )}
                    </div>
                  )
                })()}
              </div>
            ) : (
              // Non-doctor view (existing logic)
              <>
                {viewMode === 'kanban' && (
                  <CasesKanbanView
                    cases={filteredCases}
                    statusConfig={statusConfig}
                    urgencyConfig={urgencyConfig}
                    onCaseClick={handleCaseClick}
                    userRole={user?.role || ''}
                    onStatusChange={async (caseId, newStatus) => {
                      try {
                        await apiClient.updateCase(caseId, { status: newStatus })
                        // Refresh cases after status change
                        let response
                        if (user?.role === 'doctor') {
                          response = await apiClient.getDoctorCases()
                          setCases(response || [])
                        } else {
                          response = await apiClient.getCases({
                            status: statusFilter !== 'all' ? statusFilter : undefined,
                            priority: urgencyFilter !== 'all' ? urgencyFilter : undefined,
                            search: searchTerm || undefined
                          })
                          setCases(response.data || [])
                        }
                      } catch (error) {
                        // TODO: Replace with proper error reporting
  console.error('Failed to update case status:', error)
                        setError('Failed to update case status. Please try again.')
                      }
                    }}
                  />
                )}
                
                {viewMode === 'card' && (
                  <CasesCardView
                    cases={filteredCases}
                    statusConfig={statusConfig}
                    urgencyConfig={urgencyConfig}
                    onCaseClick={handleCaseClick}
                    userRole={user?.role || ''}
                    onAcceptCase={(user as any)?.role === 'doctor' ? handleAcceptCase : undefined}
                  />
                )}
                
                {viewMode === 'list' && (
                  <CasesListView
                    cases={filteredCases}
                    statusConfig={statusConfig}
                    urgencyConfig={urgencyConfig}
                    onCaseClick={handleCaseClick}
                    userRole={user?.role || ''}
                  />
                )}
              </>
            )}
          </>
        )}
          </div>
        </div>
      </div>
    </div>
  )
}
