import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';
import { apiClient } from '../../../services/api';
import debounce from 'debounce';

export interface RxNavSuggestion {
  id: string;
  term: string;
  description?: string;
  code: string;
  system: 'rxnav';
}

interface RxNavState {
  suggestions: RxNavSuggestion[];
  query: string;
  range: { from: number; to: number } | null;
  isLoading: boolean;
  selectedIndex: number;
}

const RXNAV_PLUGIN_KEY = new PluginKey<RxNavState>('rxnavAutocompletion');

export const RxNavExtension = Extension.create({
  name: 'rxnavExtension',

  addOptions() {
    return {
      trigger: '@med:',
      minQueryLength: 2,
      maxSuggestions: 10,
      debounceMs: 300,
    };
  },

  addProseMirrorPlugins() {
    const { trigger, minQueryLength, maxSuggestions, debounceMs } = this.options;

    // Debounced search function
    const debouncedSearch = debounce(async (query: string, view: any, range: { from: number; to: number }) => {
      if (query.length < minQueryLength) {
        view.dispatch(
          view.state.tr.setMeta(RXNAV_PLUGIN_KEY, {
            type: 'clear'
          })
        );
        return;
      }

      // Set loading state
      view.dispatch(
        view.state.tr.setMeta(RXNAV_PLUGIN_KEY, {
          type: 'loading',
          query,
          range
        })
      );

      try {
        const response = await apiClient.get(`/api/medical/rxnav/search`, {
          params: {
            term: query,
            limit: maxSuggestions
          }
        });

        const suggestions: RxNavSuggestion[] = response.data.data.results;

        view.dispatch(
          view.state.tr.setMeta(RXNAV_PLUGIN_KEY, {
            type: 'suggestions',
            suggestions,
            query,
            range
          })
        );
      } catch (error) {
        console.error('RxNAV search error:', error);
        view.dispatch(
          view.state.tr.setMeta(RXNAV_PLUGIN_KEY, {
            type: 'error',
            query,
            range
          })
        );
      }
    }, debounceMs);

    return [
      new Plugin({
        key: RXNAV_PLUGIN_KEY,
        
        state: {
          init(): RxNavState {
            return {
              suggestions: [],
              query: '',
              range: null,
              isLoading: false,
              selectedIndex: 0,
            };
          },

          apply(tr, oldState): RxNavState {
            const meta = tr.getMeta(RXNAV_PLUGIN_KEY);
            
            if (meta) {
              switch (meta.type) {
                case 'clear':
                  return {
                    ...oldState,
                    suggestions: [],
                    query: '',
                    range: null,
                    isLoading: false,
                    selectedIndex: 0,
                  };
                
                case 'loading':
                  return {
                    ...oldState,
                    query: meta.query,
                    range: meta.range,
                    isLoading: true,
                    selectedIndex: 0,
                  };
                
                case 'suggestions':
                  return {
                    ...oldState,
                    suggestions: meta.suggestions,
                    query: meta.query,
                    range: meta.range,
                    isLoading: false,
                    selectedIndex: 0,
                  };
                
                case 'select':
                  return {
                    ...oldState,
                    selectedIndex: meta.index,
                  };
                
                case 'error':
                  return {
                    ...oldState,
                    suggestions: [],
                    isLoading: false,
                    selectedIndex: 0,
                  };
              }
            }

            // Check if cursor moved or content changed
            if (tr.docChanged || tr.selectionSet) {
              const { selection } = tr;
              const { $from } = selection;
              
              // Look for trigger pattern
              const textBefore = $from.nodeBefore?.textContent || '';
              const textAfter = $from.nodeAfter?.textContent || '';
              const fullText = textBefore + textAfter;
              
              const triggerIndex = fullText.lastIndexOf(trigger);
              
              if (triggerIndex !== -1 && triggerIndex <= textBefore.length) {
                const query = fullText.slice(triggerIndex + trigger.length);
                const spaceIndex = query.indexOf(' ');
                const actualQuery = spaceIndex !== -1 ? query.slice(0, spaceIndex) : query;
                
                if (actualQuery !== oldState.query) {
                  const from = $from.pos - (textBefore.length - triggerIndex);
                  const to = from + trigger.length + actualQuery.length;
                  
                  // Trigger search
                  setTimeout(() => {
                    debouncedSearch(actualQuery, this.editor?.view, { from, to });
                  }, 0);
                  
                  return {
                    ...oldState,
                    query: actualQuery,
                    range: { from, to },
                  };
                }
              } else if (oldState.range) {
                // Clear suggestions if trigger is no longer present
                return {
                  ...oldState,
                  suggestions: [],
                  query: '',
                  range: null,
                  isLoading: false,
                  selectedIndex: 0,
                };
              }
            }

            return oldState;
          },
        },

        props: {
          decorations(state) {
            const pluginState = RXNAV_PLUGIN_KEY.getState(state);
            
            if (!pluginState?.range) {
              return DecorationSet.empty;
            }

            // Highlight the trigger and query
            const decoration = Decoration.inline(
              pluginState.range.from,
              pluginState.range.to,
              {
                class: 'rxnav-trigger bg-green-100 text-green-800 px-1 rounded',
              }
            );

            return DecorationSet.create(state.doc, [decoration]);
          },

          handleKeyDown(view, event) {
            const pluginState = RXNAV_PLUGIN_KEY.getState(view.state);
            
            if (!pluginState?.suggestions.length) {
              return false;
            }

            switch (event.key) {
              case 'ArrowDown':
                event.preventDefault();
                const nextIndex = Math.min(
                  pluginState.selectedIndex + 1,
                  pluginState.suggestions.length - 1
                );
                view.dispatch(
                  view.state.tr.setMeta(RXNAV_PLUGIN_KEY, {
                    type: 'select',
                    index: nextIndex,
                  })
                );
                return true;

              case 'ArrowUp':
                event.preventDefault();
                const prevIndex = Math.max(pluginState.selectedIndex - 1, 0);
                view.dispatch(
                  view.state.tr.setMeta(RXNAV_PLUGIN_KEY, {
                    type: 'select',
                    index: prevIndex,
                  })
                );
                return true;

              case 'Enter':
              case 'Tab':
                event.preventDefault();
                const selectedSuggestion = pluginState.suggestions[pluginState.selectedIndex];
                if (selectedSuggestion && pluginState.range) {
                  // Insert the selected medication
                  const { from, to } = pluginState.range;
                  const insertText = `${selectedSuggestion.term} (RxCUI: ${selectedSuggestion.code})`;
                  
                  view.dispatch(
                    view.state.tr
                      .replaceWith(from, to, view.state.schema.text(insertText))
                      .setMeta(RXNAV_PLUGIN_KEY, { type: 'clear' })
                  );
                }
                return true;

              case 'Escape':
                view.dispatch(
                  view.state.tr.setMeta(RXNAV_PLUGIN_KEY, { type: 'clear' })
                );
                return true;
            }

            return false;
          },
        },

        view() {
          let suggestionElement: HTMLElement | null = null;

          return {
            update: (view, prevState) => {
              const pluginState = RXNAV_PLUGIN_KEY.getState(view.state);
              
              if (pluginState?.suggestions.length && pluginState.range) {
                if (!suggestionElement) {
                  suggestionElement = this.createSuggestionElement(pluginState, view);
                  document.body.appendChild(suggestionElement);
                } else {
                  this.updateSuggestionElement(suggestionElement, pluginState, view);
                }
              } else if (suggestionElement) {
                suggestionElement.remove();
                suggestionElement = null;
              }
            },

            destroy: () => {
              if (suggestionElement) {
                suggestionElement.remove();
              }
            },
          };
        },
      }),
    ];
  },

  // Helper methods for suggestion UI
  createSuggestionElement(pluginState: RxNavState, view: any): HTMLElement {
    const element = document.createElement('div');
    element.className = 'rxnav-suggestions absolute z-50 bg-white border border-gray-300 rounded-lg shadow-lg max-w-md';
    
    this.updateSuggestionElement(element, pluginState, view);
    this.positionSuggestionElement(element, pluginState, view);
    
    return element;
  },

  updateSuggestionElement(element: HTMLElement, pluginState: RxNavState, view: any) {
    if (pluginState.isLoading) {
      element.innerHTML = `
        <div class="p-3 text-sm text-gray-600">
          <div class="flex items-center">
            <div class="animate-spin h-4 w-4 border-2 border-green-500 border-t-transparent rounded-full mr-2"></div>
            Searching medications...
          </div>
        </div>
      `;
      return;
    }

    if (!pluginState.suggestions.length) {
      element.innerHTML = `
        <div class="p-3 text-sm text-gray-500">
          No medications found for "${pluginState.query}"
        </div>
      `;
      return;
    }

    const suggestionItems = pluginState.suggestions
      .map((suggestion, index) => {
        const isSelected = index === pluginState.selectedIndex;
        return `
          <div class="p-3 cursor-pointer hover:bg-gray-50 ${isSelected ? 'bg-green-50 border-l-4 border-green-500' : ''}" 
               data-index="${index}">
            <div class="font-medium text-sm text-gray-900">${suggestion.term}</div>
            <div class="text-xs text-gray-500 mt-1">
              RxCUI: ${suggestion.code}
              ${suggestion.description ? ` • ${suggestion.description}` : ''}
            </div>
          </div>
        `;
      })
      .join('');

    element.innerHTML = `
      <div class="max-h-64 overflow-y-auto">
        <div class="p-2 bg-gray-50 border-b text-xs font-medium text-gray-700">
          💊 Medications (RxNAV)
        </div>
        ${suggestionItems}
      </div>
    `;

    // Add click handlers
    element.querySelectorAll('[data-index]').forEach((item, index) => {
      item.addEventListener('click', () => {
        const suggestion = pluginState.suggestions[index];
        if (suggestion && pluginState.range) {
          const { from, to } = pluginState.range;
          const insertText = `${suggestion.term} (RxCUI: ${suggestion.code})`;
          
          view.dispatch(
            view.state.tr
              .replaceWith(from, to, view.state.schema.text(insertText))
              .setMeta(RXNAV_PLUGIN_KEY, { type: 'clear' })
          );
        }
      });
    });
  },

  positionSuggestionElement(element: HTMLElement, pluginState: RxNavState, view: any) {
    if (!pluginState.range) return;

    const coords = view.coordsAtPos(pluginState.range.from);
    element.style.left = `${coords.left}px`;
    element.style.top = `${coords.bottom + 5}px`;
  },
});
