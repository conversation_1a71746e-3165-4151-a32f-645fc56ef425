import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { MedicalBlockComponent } from '../MedicalBlockComponent';

export interface MedicalBlockAttributes {
  blockType: 'diagnosis' | 'medication' | 'vitals' | 'assessment' | 'plan';
  data: Record<string, any>;
  medicalCodes: Array<{
    system: 'snomed' | 'rxnav';
    code: string;
    display: string;
  }>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    medicalBlock: {
      insertMedicalBlock: (attributes: Partial<MedicalBlockAttributes>) => ReturnType;
    };
  }
}

export const MedicalBlockExtension = Node.create({
  name: 'medicalBlock',

  group: 'block',

  content: 'block*',

  defining: true,

  addAttributes() {
    return {
      blockType: {
        default: 'diagnosis',
        parseHTML: element => element.getAttribute('data-block-type'),
        renderHTML: attributes => ({
          'data-block-type': attributes.blockType,
        }),
      },
      data: {
        default: {},
        parseHTML: element => {
          const data = element.getAttribute('data-block-data');
          return data ? JSON.parse(data) : {};
        },
        renderHTML: attributes => ({
          'data-block-data': JSON.stringify(attributes.data),
        }),
      },
      medicalCodes: {
        default: [],
        parseHTML: element => {
          const codes = element.getAttribute('data-medical-codes');
          return codes ? JSON.parse(codes) : [];
        },
        renderHTML: attributes => ({
          'data-medical-codes': JSON.stringify(attributes.medicalCodes),
        }),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="medical-block"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, {
        'data-type': 'medical-block',
        class: 'medical-block',
      }),
      0,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(MedicalBlockComponent);
  },

  addCommands() {
    return {
      insertMedicalBlock:
        (attributes) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-d': () => this.editor.commands.insertMedicalBlock({ blockType: 'diagnosis' }),
      'Mod-Shift-m': () => this.editor.commands.insertMedicalBlock({ blockType: 'medication' }),
      'Mod-Shift-v': () => this.editor.commands.insertMedicalBlock({ blockType: 'vitals' }),
      'Mod-Shift-a': () => this.editor.commands.insertMedicalBlock({ blockType: 'assessment' }),
      'Mod-Shift-p': () => this.editor.commands.insertMedicalBlock({ blockType: 'plan' }),
    };
  },
});
