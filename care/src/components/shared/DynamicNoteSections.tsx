import { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { apiClient } from '../../services/api';
import { CollabClinicalEditor } from './CollabClinicalEditor';
import * as LucideIcons from 'lucide-react';

interface NoteType {
  id: string;
  key: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
  category: string;
  allowedRoles: string[];
  requiresDoctor: boolean;
  requiresPermission?: string;
  autoSave: boolean;
  autoSaveDelay: number;
  richText: boolean;
  showDoctorInfo: boolean;
  showInSidebar: boolean;
  placeholder?: string;
  template?: any;
  aiEnabled: boolean;
  aiModel?: string;
  aiPrompt?: string;
  sortOrder: number;
  isActive: boolean;
}

interface DynamicNoteSectionsProps {
  caseId: string;
  showInSidebar?: boolean; // Filter by sidebar vs main content
  className?: string;
  disabled?: boolean; // Whether the notes should be read-only
}

// Color mapping for Tailwind classes
const colorMap: Record<string, { 
  background: string;
  border: string;
  text: string;
  gradient: string;
  icon: string;
}> = {
  blue: {
    background: 'bg-blue-50',
    border: 'border-blue-200',
    text: 'text-blue-900',
    gradient: 'bg-gradient-to-r from-blue-50 to-blue-100',
    icon: 'text-blue-600'
  },
  green: {
    background: 'bg-green-50',
    border: 'border-green-200',
    text: 'text-green-900',
    gradient: 'bg-gradient-to-r from-green-50 to-green-100',
    icon: 'text-green-600'
  },
  purple: {
    background: 'bg-purple-50',
    border: 'border-purple-200',
    text: 'text-purple-900',
    gradient: 'bg-gradient-to-r from-purple-50 to-purple-100',
    icon: 'text-purple-600'
  },
  orange: {
    background: 'bg-orange-50',
    border: 'border-orange-200',
    text: 'text-orange-900',
    gradient: 'bg-gradient-to-r from-orange-50 to-orange-100',
    icon: 'text-orange-600'
  },
  indigo: {
    background: 'bg-indigo-50',
    border: 'border-indigo-200',
    text: 'text-indigo-900',
    gradient: 'bg-gradient-to-r from-indigo-50 to-indigo-100',
    icon: 'text-indigo-600'
  },
  gray: {
    background: 'bg-gray-50',
    border: 'border-gray-200',
    text: 'text-gray-900',
    gradient: 'bg-gradient-to-r from-gray-50 to-gray-100',
    icon: 'text-gray-600'
  },
  red: {
    background: 'bg-red-50',
    border: 'border-red-200',
    text: 'text-red-900',
    gradient: 'bg-gradient-to-r from-red-50 to-red-100',
    icon: 'text-red-600'
  },
  yellow: {
    background: 'bg-yellow-50',
    border: 'border-yellow-200',
    text: 'text-yellow-900',
    gradient: 'bg-gradient-to-r from-yellow-50 to-yellow-100',
    icon: 'text-yellow-600'
  },
};

export default function DynamicNoteSections({
  caseId,
  showInSidebar = false,
  className = '',
  disabled = false,
}: DynamicNoteSectionsProps) {
  const { user } = useAuth();
  const [noteTypes, setNoteTypes] = useState<NoteType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load available note types for current user
  useEffect(() => {
    const loadNoteTypes = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const response = await apiClient.getNoteTypes();
        
        if (response && response.noteTypes) {
          // Filter by showInSidebar preference and sort by sortOrder
          const filteredTypes = response.noteTypes
            .filter((type: NoteType) => type.showInSidebar === showInSidebar)
            .sort((a: NoteType, b: NoteType) => a.sortOrder - b.sortOrder);
          
          setNoteTypes(filteredTypes);
        }
      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error loading note types:', err);
        setError('Failed to load note types');
      } finally {
        setLoading(false);
      }
    };

    loadNoteTypes();
  }, [user, showInSidebar]);

  // Get Lucide icon component by name
  const getIconComponent = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName];
    return IconComponent || LucideIcons.FileText; // Fallback to FileText
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin h-6 w-6 border border-blue-500 border-t-transparent rounded-full"></div>
          <span className="ml-2 text-gray-600">Loading note types...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-8">
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  if (noteTypes.length === 0) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-8">
          <p className="text-gray-500">No note types available for your role.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {noteTypes.map((noteType) => {
        const IconComponent = getIconComponent(noteType.icon);
        const colorClasses = colorMap[noteType.color] || colorMap.blue;

        return (
          <section
            key={noteType.id}
            id={`note-section-${noteType.key}`}
            className="scroll-mt-6 w-full"
          >
            <div className={`${colorClasses.gradient} rounded-xl border ${colorClasses.border} p-6 w-full`}>
              {/* Section Header */}
              <div className="mb-4">
                <h3 className={`text-lg font-semibold ${colorClasses.text} flex items-center`}>
                  <IconComponent className={`h-5 w-5 mr-2 ${colorClasses.icon}`} />
                  {noteType.name}
                </h3>
                {noteType.description && (
                  <p className="text-sm text-gray-600 mt-1">{noteType.description}</p>
                )}
                
                {/* AI Badge */}
                {noteType.aiEnabled && (
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                      🤖 AI-Enabled
                      {noteType.aiModel && (
                        <span className="ml-1">({noteType.aiModel})</span>
                      )}
                    </span>
                  </div>
                )}
              </div>

              {/* Note Editor */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <CollabClinicalEditor
                  caseId={caseId}
                  noteType={noteType.key} // Use the key directly as noteType
                  placeholder={noteType.placeholder || `Enter ${noteType.name.toLowerCase()}...`}
                  disabled={disabled}
                  className="w-full"
                  height={400}
                />
              </div>

              {/* AI Generation Button (if AI enabled) */}
              {noteType.aiEnabled && user?.role !== 'patient' && (
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => {
                      // TODO: Implement AI generation
                      console.log('Generate AI content for:', noteType.key);
                    }}
                    className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-indigo-600 text-white hover:bg-indigo-700 transition-colors"
                  >
                    <LucideIcons.Sparkles className="h-4 w-4 mr-2" />
                    Generate AI Insights
                  </button>
                </div>
              )}
            </div>
          </section>
        );
      })}
    </div>
  );
}
