import React, { useCallback, useEffect, useState } from 'react';
import SimpleTextEditor from '../ui/simple-text-editor';
import { Lock, AlertCircle } from 'lucide-react';

interface UnifiedEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  onSave?: (content: string) => void;
  autoSave?: boolean;
  autoSaveDelay?: number;
  className?: string;
  disabled?: boolean;
  height?: number;
  title?: string;
  showSaveStatus?: boolean;
  'data-testid'?: string;
}

export default function UnifiedEditor({
  initialContent = '',
  placeholder = 'Start typing...',
  onChange,
  onSave,
  autoSave = false,
  autoSaveDelay = 2000,
  className = '',
  disabled = false,
  height = 300,
  title,
  showSaveStatus = true,
  'data-testid': dataTestId,
}: UnifiedEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize content when initialContent changes
  useEffect(() => {
    if (initialContent !== content) {
      setContent(initialContent);
    }
    // Mark as initialized after a brief delay
    const timer = setTimeout(() => setIsInitialized(true), 500);
    return () => clearTimeout(timer);
  }, [initialContent]);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !onSave || !isInitialized || disabled) return;

    const timer = setTimeout(async () => {
      if (content !== initialContent) {
        setIsSaving(true);
        setError(null);
        try {
          await onSave(content);
          setLastSaved(new Date());
        } catch (err) {
          // TODO: Replace with proper error reporting
  console.error('Auto-save failed:', err);
          setError('Failed to save');
        } finally {
          setIsSaving(false);
        }
      }
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [content, autoSave, onSave, autoSaveDelay, isInitialized, disabled, initialContent]);

  const handleChange = useCallback((value: string | undefined) => {
    const newContent = value || '';
    setContent(newContent);
    onChange?.(newContent);
  }, [onChange]);

  return (
    <div className={`unified-editor ${className}`} data-testid={dataTestId}>
      {title && (
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            {disabled && (
              <div className="flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-md text-xs text-gray-600">
                <Lock className="h-3 w-3" />
                <span>Read-only</span>
              </div>
            )}
          </div>
          {showSaveStatus && (
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              {isSaving && (
                <div className="flex items-center space-x-1">
                  <div className="animate-spin h-3 w-3 border border-blue-500 border-t-transparent rounded-full"></div>
                  <span>Saving...</span>
                </div>
              )}
              {!isSaving && lastSaved && (
                <span>Saved at {lastSaved.toLocaleTimeString()}</span>
              )}
              {error && (
                <span className="text-red-500 flex items-center space-x-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{error}</span>
                </span>
              )}
            </div>
          )}
        </div>
      )}

      <div className={disabled ? 'pointer-events-none opacity-60' : ''}>
        <SimpleTextEditor
          value={content}
          onChange={handleChange}
          height={height}
          placeholder={disabled ? "Content is read-only" : placeholder}
          className="w-full"
          disabled={disabled}
        />
      </div>
    </div>
  );
}