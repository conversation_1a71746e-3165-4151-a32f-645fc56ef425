import React from 'react'
import {
  FileText
} from 'lucide-react'

// Define types
interface Case {
  id: string
  title: string
  description: string
  status: 'draft' | 'submitted' | 'assigned' | 'in_review' | 'completed'
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  patientId: string
  patientName?: string
  // Note: assignedDoctorId has been removed in favor of multi-doctor assignment system
  // Doctors are now assigned via the case_doctors table
  assignedDoctorName?: string
  specialtyRequired?: string
  createdAt: string
  updatedAt: string
  // Doctor-specific fields for per-doctor acceptance status
  doctorAcceptanceStatus?: 'pending' | 'accepted' | 'declined' | 'closed'
  doctorAcceptedAt?: string
}

interface StatusConfig {
  [key: string]: {
    label: string
    color: string
    icon: React.ElementType
  }
}

interface UrgencyConfig {
  [key: string]: {
    label: string
    color: string
  }
}

interface CasesListViewProps {
  cases: Case[]
  statusConfig: StatusConfig
  urgencyConfig: UrgencyConfig
  onCaseClick: (caseId: string) => void
  userRole: string
}

export function CasesListView({
  cases,
  statusConfig,
  urgencyConfig,
  onCaseClick,
  userRole
}: CasesListViewProps) {
  // Format date to readable format with error handling
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Unknown date'
    
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return 'Invalid date'
      }
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    } catch (error) {
      console.warn('Error formatting date:', dateString, error)
      return 'Invalid date'
    }
  }

  // Handle empty or invalid cases array
  if (!Array.isArray(cases) || cases.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FileText className="w-6 h-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No cases found</h3>
        <p className="text-gray-500">There are no cases to display at this time.</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Case
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {userRole === 'doctor' ? 'Case Status / My Status' : 'Status'}
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Urgency
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            {(userRole === 'admin' || userRole === 'agent' || userRole === 'doctor') && (
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Specialty
              </th>
            )}
            {(userRole === 'admin' || userRole === 'agent' || userRole === 'patient') && (
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {userRole === 'patient' ? 'Doctor' : 'Patient'}
              </th>
            )}
            <th scope="col" className="relative px-6 py-3">
              <span className="sr-only">View</span>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {cases.map((case_) => {
            // Validate case data with fallbacks
            const caseId = case_?.id || 'unknown'
            const caseTitle = case_?.title || 'Untitled Case'
            const caseDescription = case_?.description || 'No description available'
            const caseStatus = case_?.status || 'draft'
            const caseUrgency = case_?.urgencyLevel || 'medium'
            const caseCreatedAt = case_?.createdAt
            const caseSpecialty = case_?.specialtyRequired
            const caseDoctorName = case_?.assignedDoctorName
            const casePatientName = case_?.patientName
            
            // Get status configuration with fallback
            const statusInfo = statusConfig?.[caseStatus] || {
              label: 'Unknown',
              color: 'bg-gray-100 text-gray-800',
              icon: FileText
            }
            
            // Get urgency configuration with fallback
            const urgencyInfo = urgencyConfig?.[caseUrgency] || {
              label: 'Medium',
              color: 'bg-yellow-100 text-yellow-800'
            }
            
            const StatusIcon = statusInfo.icon || FileText
            
            return (
              <tr 
                key={caseId}
                onDoubleClick={() => {
                  try {
                    if (onCaseClick && caseId !== 'unknown') {
                      onCaseClick(caseId)
                    }
                  } catch (error) {
                    // TODO: Replace with proper error reporting
  console.error('Error handling case click:', error)
                  }
                }}
                className="hover:bg-gray-50 cursor-pointer transition-colors"
                title="Double-click to view case details"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{caseTitle}</div>
                      <div className="text-sm text-gray-500 line-clamp-1">{caseDescription}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    <StatusIcon className="h-4 w-4 mr-2 text-gray-500" />
                    <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${statusInfo.color}`}>
                      {statusInfo.label}
                    </span>
                    {/* Show doctor's individual acceptance status for doctors */}
                    {userRole === 'doctor' && case_.doctorAcceptanceStatus && (
                      <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${
                        case_.doctorAcceptanceStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        case_.doctorAcceptanceStatus === 'accepted' ? 'bg-green-100 text-green-800' :
                        case_.doctorAcceptanceStatus === 'declined' ? 'bg-red-100 text-red-800' :
                        case_.doctorAcceptanceStatus === 'closed' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {case_.doctorAcceptanceStatus === 'pending' ? 'Pending' :
                         case_.doctorAcceptanceStatus === 'accepted' ? 'Accepted' :
                         case_.doctorAcceptanceStatus === 'declined' ? 'Declined' :
                         case_.doctorAcceptanceStatus === 'closed' ? 'Opinion Delivered' :
                         case_.doctorAcceptanceStatus}
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${urgencyInfo.color}`}>
                    {urgencyInfo.label}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(caseCreatedAt)}
                </td>
                {(userRole === 'admin' || userRole === 'agent' || userRole === 'doctor') && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {caseSpecialty || 'Not specified'}
                  </td>
                )}
                {(userRole === 'admin' || userRole === 'agent' || userRole === 'patient') && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {userRole === 'patient' 
                      ? (caseDoctorName || 'Not assigned')
                      : (casePatientName || 'Unknown')}
                  </td>
                )}
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button className="text-primary-600 hover:text-primary-900">
                    View
                  </button>
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  )
}
