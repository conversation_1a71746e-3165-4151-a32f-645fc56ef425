import { useCallback, useEffect, useRef, useState } from 'react';
import { $getRoot, $createParagraphNode, $createTextNode } from 'lexical';
import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { LinkNode, AutoLinkNode } from '@lexical/link';
import { MarkNode } from '@lexical/mark';

interface LexicalEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (content: string, editorState: any) => void;
  onSave?: (content: string, editorState: any) => void;
  autoSave?: boolean;
  autoSaveDelay?: number;
  richText?: boolean;
  className?: string;
  disabled?: boolean;
  'data-testid'?: string;
}

// Auto-save plugin component
function AutoSavePlugin({ 
  onSave, 
  delay = 2000 
}: { 
  onSave?: (content: string, editorState: any) => void;
  delay?: number;
}) {
  const [editor] = useLexicalComposerContext();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleSave = useCallback(async (editorState: any) => {
    if (!onSave || !isInitialized) return; // Don't save during initialization

    setIsSaving(true);
    try {
      const htmlContent = await new Promise<string>((resolve) => {
        editorState.read(() => {
          const htmlString = $generateHtmlFromNodes(editor, null);
          resolve(htmlString);
        });
      });

      await onSave(htmlContent, editorState);
      setLastSaved(new Date());
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Auto-save failed:', error);
    } finally {
      setIsSaving(false);
    }
  }, [editor, onSave, isInitialized]);

  // Allow auto-save after a brief delay to ensure initialization is complete
  useEffect(() => {
    initializationTimeoutRef.current = setTimeout(() => {
      setIsInitialized(true);
    }, 1000); // 1 second delay to allow content initialization

    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Only set timeout if initialized
      if (isInitialized) {
        timeoutRef.current = setTimeout(() => {
          handleSave(editorState);
        }, delay);
      }
    });
  }, [editor, handleSave, delay, isInitialized]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Render save status
  return (
    <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
      <div className="flex items-center space-x-2">
        {isSaving && (
          <div className="flex items-center space-x-1">
            <div className="animate-spin h-3 w-3 border border-blue-500 border-t-transparent rounded-full"></div>
            <span>Saving...</span>
          </div>
        )}
        {!isSaving && lastSaved && (
          <span>Saved at {lastSaved.toLocaleTimeString()}</span>
        )}
      </div>
    </div>
  );
}

// Initialize content plugin
function InitializeContentPlugin({ content }: { content?: string }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (!content) return;

    editor.update(() => {
      try {
        const root = $getRoot();
        root.clear();
        
        // Check if content looks like HTML
        if (content.includes('<') && content.includes('>')) {
          // Parse HTML content more carefully
          const parser = new DOMParser();
          const dom = parser.parseFromString(content, 'text/html');
          
          try {
            const nodes = $generateNodesFromDOM(editor, dom);
            if (nodes.length > 0) {
              root.append(...nodes);
            } else {
              // Fallback to plain text if HTML parsing fails
              const paragraph = $createParagraphNode();
              const textNode = $createTextNode(content.replace(/<[^>]*>/g, ''));
              paragraph.append(textNode);
              root.append(paragraph);
            }
          } catch (parseError) {
            console.warn('HTML parsing failed, falling back to plain text:', parseError);
            // Fallback to plain text
            const paragraph = $createParagraphNode();
            const textNode = $createTextNode(content.replace(/<[^>]*>/g, ''));
            paragraph.append(textNode);
            root.append(paragraph);
          }
        } else {
          // Handle as plain text - ensure it's in a single paragraph
          const paragraph = $createParagraphNode();
          const textNode = $createTextNode(content);
          paragraph.append(textNode);
          root.append(paragraph);
        }
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Lexical editor error:', error);
        // Fallback: create simple text content
        try {
          const root = $getRoot();
          root.clear();
          const paragraph = $createParagraphNode();
          const textNode = $createTextNode(content || '');
          paragraph.append(textNode);
          root.append(paragraph);
        } catch (fallbackError) {
          // TODO: Replace with proper error reporting
  console.error('Fallback content creation failed:', fallbackError);
        }
      }
    });
  }, [editor, content]);

  return null;
}

const theme = {
  ltr: 'ltr',
  rtl: 'rtl',
  placeholder: 'text-gray-400',
  paragraph: 'mb-2',
  quote: 'border-l-4 border-gray-300 pl-4 italic text-gray-700',
  heading: {
    h1: 'text-2xl font-bold mb-4',
    h2: 'text-xl font-semibold mb-3',
    h3: 'text-lg font-medium mb-2',
  },
  list: {
    nested: {
      listitem: 'list-none',
    },
    ol: 'list-decimal list-inside mb-2',
    ul: 'list-disc list-inside mb-2',
    listitem: 'mb-1',
  },
  text: {
    bold: 'font-bold',
    italic: 'italic',
    underline: 'underline',
    strikethrough: 'line-through',
    code: 'bg-gray-100 px-1 py-0.5 rounded text-sm font-mono',
  },
  link: 'text-blue-600 hover:text-blue-800 underline',
  mark: 'bg-yellow-200',
};

export default function LexicalEditor({
  initialContent,
  placeholder = 'Start typing...',
  onChange,
  onSave,
  autoSave = false,
  autoSaveDelay = 2000,
  richText = true,
  className = '',
  disabled = false,
  'data-testid': dataTestId,
}: LexicalEditorProps) {
  const initialConfig = {
    namespace: 'ClinicalNotesEditor',
    theme,
    onError: (error: Error) => {
      // TODO: Replace with proper error reporting
  console.error('Lexical editor error:', error);
    },
    nodes: richText ? [
      HeadingNode,
      ListNode,
      ListItemNode,
      QuoteNode,
      LinkNode,
      AutoLinkNode,
      MarkNode,
    ] : [],
    editable: !disabled,
  };

  const handleChange = useCallback((editorState: any, editor: any) => {
    if (!onChange) return;

    editorState.read(() => {
      try {
        const htmlContent = $generateHtmlFromNodes(editor, null);
        onChange(htmlContent, editorState);
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Error generating content from nodes:', error);
        // Fallback: try to get text content
        try {
          const root = $getRoot();
          const textContent = root.getTextContent();
          onChange(textContent, editorState);
        } catch (fallbackError) {
          // TODO: Replace with proper error reporting
  console.error('Fallback text content generation failed:', fallbackError);
          onChange('', editorState);
        }
      }
    });
  }, [onChange]);

  const editorClassName = `
    min-h-[120px] 
    p-3 
    border 
    border-gray-300 
    rounded-md 
    focus-within:ring-2 
    focus-within:ring-blue-500 
    focus-within:border-blue-500
    ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}
    ${className}
  `.trim();

  return (
    <div className="lexical-editor-container" data-testid={dataTestId}>
      <LexicalComposer initialConfig={initialConfig}>
        <div className={editorClassName}>
          {richText ? (
            <RichTextPlugin
              contentEditable={<ContentEditable className="outline-none" />}
              placeholder={<div className="text-gray-400 pointer-events-none">{placeholder}</div>}
              ErrorBoundary={LexicalErrorBoundary as any}
            />
          ) : (
            <PlainTextPlugin
              contentEditable={<ContentEditable className="outline-none" />}
              placeholder={<div className="text-gray-400 pointer-events-none">{placeholder}</div>}
              ErrorBoundary={LexicalErrorBoundary as any}
            />
          )}
          <HistoryPlugin />
          <OnChangePlugin onChange={handleChange} />
          <InitializeContentPlugin content={initialContent} />
        </div>
        {autoSave && onSave && <AutoSavePlugin onSave={onSave} delay={autoSaveDelay} />}
      </LexicalComposer>
    </div>
  );
}
