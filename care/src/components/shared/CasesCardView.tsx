import React from 'react'
import {
  Calendar,
  User,
  FileText,
  Stethoscope,
  ChevronRight,
  CheckCircle
} from 'lucide-react'

// Define types
interface Case {
  id: string
  title: string
  description: string
  status: 'draft' | 'submitted' | 'assigned' | 'in_review' | 'completed'
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  patientId: string
  patientName?: string
  // Note: assignedDoctorId has been removed in favor of multi-doctor assignment system
  // Doctors are now assigned via the case_doctors table
  assignedDoctorName?: string
  specialtyRequired?: string
  createdAt: string
  updatedAt: string
  // Doctor-specific fields for per-doctor acceptance status
  doctorAcceptanceStatus?: 'pending' | 'accepted' | 'declined' | 'closed'
  doctorAcceptedAt?: string
}

interface StatusConfig {
  [key: string]: {
    label: string
    color: string
    icon: React.ElementType
  }
}

interface UrgencyConfig {
  [key: string]: {
    label: string
    color: string
  }
}

interface CasesCardViewProps {
  cases: Case[]
  statusConfig: StatusConfig
  urgencyConfig: UrgencyConfig
  onCaseClick: (caseId: string) => void
  userRole: string
  onAcceptCase?: (caseId: string) => void
}

export function CasesCardView({
  cases,
  statusConfig,
  urgencyConfig,
  onCaseClick,
  userRole,
  onAcceptCase
}: CasesCardViewProps) {
  // Format date to readable format with error handling
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Unknown date'
    
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return 'Invalid date'
      }
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    } catch (error) {
      console.warn('Error formatting date:', dateString, error)
      return 'Invalid date'
    }
  }

  // Handle empty or invalid cases array
  if (!Array.isArray(cases) || cases.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FileText className="w-6 h-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No cases found</h3>
        <p className="text-gray-500">There are no cases to display at this time.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cases.map((case_) => {
        // Validate case data with fallbacks
        const caseId = case_?.id || 'unknown'
        const caseTitle = case_?.title || 'Untitled Case'
        const caseDescription = case_?.description || 'No description available'
        const caseStatus = case_?.status || 'draft'
        const caseUrgency = case_?.urgencyLevel || 'medium'
        const caseCreatedAt = case_?.createdAt
        const caseSpecialty = case_?.specialtyRequired
        const caseDoctorName = case_?.assignedDoctorName
        
        // Get status configuration with fallback
        const statusInfo = statusConfig?.[caseStatus] || {
          label: 'Unknown',
          color: 'bg-gray-100 text-gray-800',
          icon: FileText
        }
        
        // Get urgency configuration with fallback
        const urgencyInfo = urgencyConfig?.[caseUrgency] || {
          label: 'Medium',
          color: 'bg-yellow-100 text-yellow-800'
        }
        
        const StatusIcon = statusInfo.icon || FileText
        
        return (
          <div
            key={caseId}
            onDoubleClick={() => {
              try {
                if (onCaseClick && caseId !== 'unknown') {
                  onCaseClick(caseId)
                }
              } catch (error) {
                // TODO: Replace with proper error reporting
  console.error('Error handling case click:', error)
              }
            }}
            className="bg-white rounded-2xl shadow-lg border border-primary-100 overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer"
            title="Double-click to view case details"
          >
            <div className="p-5">
              <div className="flex justify-between items-start mb-3">
                <div className="flex items-center space-x-2">
                  <StatusIcon className="h-5 w-5 mr-2 text-gray-500" />
                  <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${statusInfo.color}`}>
                    {statusInfo.label}
                  </span>
                  {/* Show doctor's individual acceptance status for doctors */}
                  {userRole === 'doctor' && case_.doctorAcceptanceStatus && (
                    <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${
                      case_.doctorAcceptanceStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      case_.doctorAcceptanceStatus === 'accepted' ? 'bg-green-100 text-green-800' :
                      case_.doctorAcceptanceStatus === 'declined' ? 'bg-red-100 text-red-800' :
                      case_.doctorAcceptanceStatus === 'closed' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {case_.doctorAcceptanceStatus === 'pending' ? 'Pending' :
                       case_.doctorAcceptanceStatus === 'accepted' ? 'Accepted' :
                       case_.doctorAcceptanceStatus === 'declined' ? 'Declined' :
                       case_.doctorAcceptanceStatus === 'closed' ? 'Opinion Delivered' :
                       case_.doctorAcceptanceStatus}
                    </span>
                  )}
                </div>
                <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${urgencyInfo.color}`}>
                  {urgencyInfo.label}
                </span>
              </div>
              
              <h3 className="text-lg font-medium text-gray-900 mb-2 line-clamp-2">
                {caseTitle}
              </h3>
              <p className="text-sm text-gray-500 mb-4 line-clamp-2">
                {caseDescription}
              </p>
              
              <div className="flex flex-col space-y-2 text-sm">
                <div className="flex items-center text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>Created: {formatDate(caseCreatedAt)}</span>
                </div>
                
                {caseSpecialty && (
                  <div className="flex items-center text-gray-600">
                    <Stethoscope className="h-4 w-4 mr-2" />
                    <span>{caseSpecialty}</span>
                  </div>
                )}
                
                {caseDoctorName && (
                  <div className="flex items-center text-gray-600">
                    <User className="h-4 w-4 mr-2" />
                    <span>{caseDoctorName}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-primary-50 px-5 py-3 border-t border-primary-100">
              {userRole === 'doctor' && case_.doctorAcceptanceStatus === 'pending' && onAcceptCase ? (
                <div className="flex justify-between items-center gap-3">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      onAcceptCase(caseId)
                    }}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-primary-500 text-white text-sm font-medium rounded-full hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Accept Case
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      onCaseClick(caseId)
                    }}
                    className="px-3 py-2 text-primary-500 hover:text-primary-700 transition-colors"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">
                    {userRole === 'patient' ? 'View details' :
                     userRole === 'doctor' && case_.doctorAcceptanceStatus === 'accepted' ? 'Working on case' :
                     userRole === 'doctor' && case_.doctorAcceptanceStatus === 'closed' ? 'Opinion delivered' :
                     'Manage case'}
                  </span>
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
