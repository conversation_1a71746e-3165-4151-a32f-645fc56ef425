import { UnifiedDocumentViewer } from '@/components/ui/UnifiedDocumentViewer'

interface UniversalDocumentViewerProps {
  document: {
    id: string
    title: string
    originalFileName: string
    mimeType: string
    fileSize: number
  }
  streamUrl: string
  onClose?: () => void
  showControls?: boolean
}

export function UniversalDocumentViewer({
  document,
  onClose,
  showControls = true
}: UniversalDocumentViewerProps) {
  return (
    <UnifiedDocumentViewer
      document={{
        id: document.id,
        title: document.title,
        originalFileName: document.originalFileName,
        mimeType: document.mimeType,
        fileSize: document.fileSize,
        documentType: 'other'
      }}
      onClose={onClose}
      showControls={showControls}
      showPatientInfo={false}
      auditLog={false}
      className="w-full h-full"
    />
  )
}