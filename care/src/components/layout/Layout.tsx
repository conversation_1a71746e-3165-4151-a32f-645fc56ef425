import { ReactNode, useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
// import { useNavigate } from 'react-router-dom'
import { Sidebar } from './Sidebar'
import { Menu, X } from 'lucide-react'
import { ImpersonationBanner } from '@/components/admin/ImpersonationBanner'
import logoHorizontal from '../../assets/images/logo-horizontal.png'

interface LayoutProps {
  children: ReactNode
}

export function Layout({ children }: LayoutProps) {
  const { user: _user } = useAuth()
  // const _navigate = useNavigate()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100 flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-gray-600 bg-opacity-75 z-20 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64 h-screen">
          <Sidebar />
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-30 w-64 transform transition-transform duration-300 ease-in-out lg:hidden h-screen
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <Sidebar />
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Impersonation Banner */}
        <ImpersonationBanner />
        {/* Top header for mobile */}
        <header className="lg:hidden bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
              >
                {sidebarOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
              <div className="flex items-center">
                <img 
                  src={logoHorizontal} 
                  alt="Continuia Logo" 
                  className="h-8 w-auto"
                />
              </div>
            </div>
            

          </div>
        </header>



        {/* Main content */}
        <main className="flex-1 overflow-y-auto bg-transparent">
          <div className="px-4 py-6 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

// Helper functions for dynamic page titles
// function _getPageTitle(pathname: string, role?: string): string {
//   if (pathname === '/') return 'Dashboard'
//   if (pathname.startsWith('/patient')) return 'Patient Portal'
//   if (pathname.startsWith('/doctor')) return 'Doctor Portal'
//   if (pathname.startsWith('/agent')) return 'Agent Portal'
//   if (pathname.startsWith('/admin')) return 'Admin Portal'
//   return 'Continuia'
// }

// function _getPageDescription(pathname: string, role?: string): string {
//   if (pathname === '/') {
//     switch (role) {
//       case 'patient': return 'Manage your health cases and get expert second opinions'
//       case 'doctor': return 'Review cases and provide medical opinions'
//       case 'agent': return 'Coordinate patient care and manage workflows'
//       case 'admin': return 'System administration and analytics'
//       default: return 'Welcome to Continuia Healthcare Platform'
//     }
//   }
//   if (pathname.startsWith('/patient')) return 'Manage your medical cases and appointments'
//   if (pathname.startsWith('/doctor')) return 'Review assigned cases and provide expert opinions'
//   if (pathname.startsWith('/agent')) return 'Coordinate patient interactions and case workflows'
//   if (pathname.startsWith('/admin')) return 'System management and analytics'
//   return 'Healthcare platform for second medical opinions'
// }
