import React, { useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import logoHorizontal from '../../assets/images/logo-horizontal.png'
import {
  ChevronDown,
  ChevronRight,
  Home,
  User,
  Calendar,
  Heart,
  Folder,
  Copy,
  Shield,
  LogOut
} from 'lucide-react'

interface MenuItem {
  id: string
  label: string
  icon: React.ComponentType<any>
  path?: string
  children?: MenuItem[]
  roles?: Array<'patient'>
  badge?: string | number
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    path: '/',
    roles: ['patient']
  },
  
  // Patient Menu Items
  {
    id: 'my-health',
    label: 'My Health',
    icon: Heart,
    roles: ['patient'],
    children: [
      {
        id: 'my-cases',
        label: 'My Requests',
        icon: Folder,
        path: '/cases',
        roles: ['patient']
      },
      {
        id: 'my-appointments',
        label: 'My Appointments',
        icon: Calendar,
        path: '/appointments',
        roles: ['patient']
      },
      {
        id: 'my-activity',
        label: 'My Activity Log',
        icon: Shield,
        path: '/patient/activity',
        roles: ['patient']
      }
    ]
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: Copy,
    roles: ['patient'],
    children: [
      {
        id: 'my-documents',
        label: 'My Documents',
        icon: Folder,
        path: '/documents',
        roles: ['patient']
      }
    ]
  },

  // Shared Items
  {
    id: 'profile',
    label: 'Profile',
    icon: User,
    path: '/profile',
    roles: ['patient']
  }
]

interface SidebarItemProps {
  item: MenuItem
  isExpanded: boolean
  onToggle: (id: string) => void
  userRole: string
  currentPath: string
}

function SidebarItem({ item, isExpanded, onToggle, userRole, currentPath }: SidebarItemProps) {
  const hasChildren = item.children && item.children.length > 0
  const isActive = currentPath === item.path
  const hasActiveChild = item.children?.some(child => currentPath === child.path)
  const shouldShow = !item.roles || item.roles.includes(userRole as any)
  
  if (!shouldShow) return null

  const navigate = useNavigate();
  
  const handleClick = () => {
    if (hasChildren) {
      onToggle(item.id)
    } else if (item.path) {
      navigate(item.path)
    }
  }

  const IconComponent = item.icon

  return (
    <div className="mb-1">
      {/* Main Item */}
      <div
        className={`
          flex items-center justify-between px-3 py-2.5 rounded-xl cursor-pointer transition-all duration-200
          ${isActive || hasActiveChild
            ? 'bg-primary-500 text-white shadow-lg transform scale-105'
            : 'text-gray-600 hover:bg-primary-100 hover:text-primary-600 hover:shadow-md'
          }
        `}
        onClick={handleClick}
      >
        <div className="flex items-center space-x-3 flex-1">
          <IconComponent className={`h-5 w-5 ${isActive || hasActiveChild ? 'text-white' : 'text-gray-500'}`} />
          {item.path ? (
            <Link to={item.path} className="flex-1 text-sm font-medium">
              {item.label}
            </Link>
          ) : (
            <span className="flex-1 text-sm font-medium">{item.label}</span>
          )}
          {item.badge && (
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              {item.badge}
            </span>
          )}
        </div>
        {hasChildren && (
          <div className="ml-2">
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
          </div>
        )}
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-6 mt-1 space-y-1 border-l-2 border-primary-200 pl-4">
          {item.children?.map((child) => (
            <SidebarItem
              key={child.id}
              item={child}
              isExpanded={false}
              onToggle={onToggle}
              userRole={userRole}
              currentPath={currentPath}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function Sidebar() {
  const { user, logout } = useAuth()
  const location = useLocation()
  const navigate = useNavigate()
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set(['my-health']))

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  if (!user) return null

  const userRole = user.role
  const currentPath = location.pathname

  return (
    <div className="w-64 bg-white shadow-xl border-r border-primary-200 h-full flex flex-col">
      {/* Logo Section */}
      <div className="p-4 border-b border-primary-200 flex-shrink-0 bg-gradient-to-r from-primary-50 to-white">
        <Link to="/" className="flex items-center justify-center">
          <img 
            src={logoHorizontal} 
            alt="Continuia Care" 
            className="h-10 w-auto"
          />
        </Link>
      </div>

      {/* Navigation Menu - Scrollable */}
      <nav className="p-4 space-y-2 flex-1 overflow-y-auto">
        {menuItems.map((item) => (
          <SidebarItem
            key={item.id}
            item={item}
            isExpanded={expandedItems.has(item.id)}
            onToggle={toggleExpanded}
            userRole={userRole}
            currentPath={currentPath}
          />
        ))}
      </nav>

      {/* Bottom Section - Fixed Logout */}
      <div className="p-4 border-t border-primary-100 flex-shrink-0 bg-gradient-to-r from-primary-50 to-white">
        <button
          onClick={handleLogout}
          className="w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-600 hover:bg-primary-100 hover:text-primary-700 rounded-xl transition-all duration-200 hover:shadow-md"
        >
          <LogOut className="h-5 w-5 text-gray-400" />
          <span>Logout</span>
        </button>
      </div>
    </div>
  )
}
