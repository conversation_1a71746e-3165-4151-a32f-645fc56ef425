import * as React from "react"
import { cn } from "@/lib/utils"
import { AlertCircle, X } from "lucide-react"
import { backgroundColors, textColors, shadows } from "@/constants/theme"

export interface ErrorAlertProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  message: string
  dismissible?: boolean
  onDismiss?: () => void
  variant?: "error" | "warning" | "info" | "success"
}

const variantStyles = {
  error: {
    background: backgroundColors.errorBackground,
    border: backgroundColors.errorBorder,
    text: textColors.error,
    icon: "text-red-500"
  },
  warning: {
    background: "bg-yellow-50",
    border: "border-yellow-200",
    text: "text-yellow-700",
    icon: "text-yellow-500"
  },
  info: {
    background: backgroundColors.infoBackground,
    border: backgroundColors.infoBorder,
    text: textColors.info,
    icon: "text-blue-500"
  },
  success: {
    background: backgroundColors.successBackground,
    border: backgroundColors.successBorder,
    text: textColors.success,
    icon: "text-green-500"
  }
}

const ErrorAlert = React.forwardRef<HTMLDivElement, ErrorAlertProps>(
  ({ className, title, message, dismissible = false, onDismiss, variant = "error", ...props }, ref) => {
    const styles = variantStyles[variant]
    
    return (
      <div
        ref={ref}
        className={cn(
          styles.background,
          styles.border,
          "border rounded-xl p-4",
          shadows.sm,
          className
        )}
        {...props}
      >
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <AlertCircle className={cn("h-5 w-5 mt-0.5", styles.icon)} />
          </div>
          <div className="flex-1 min-w-0">
            {title && (
              <h3 className={cn("text-sm font-medium mb-1", styles.text)}>
                {title}
              </h3>
            )}
            <p className={cn("text-sm leading-relaxed", styles.text)}>
              {message}
            </p>
          </div>
          {dismissible && onDismiss && (
            <div className="flex-shrink-0">
              <button
                onClick={onDismiss}
                className={cn(
                  "rounded-md p-1.5 inline-flex items-center justify-center transition-colors",
                  styles.text,
                  "hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-current"
                )}
              >
                <span className="sr-only">Dismiss</span>
                <X className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    )
  }
)
ErrorAlert.displayName = "ErrorAlert"

export { ErrorAlert }