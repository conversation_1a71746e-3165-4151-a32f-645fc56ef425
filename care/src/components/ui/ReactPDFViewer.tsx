import { useState, useCallback, useEffect } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, Download, ExternalLink } from 'lucide-react'
import { sizes, typography, textColors, commonClasses } from '@/constants/theme'

// Set up PDF.js worker with better configuration
pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`

// Import CSS for react-pdf
import 'react-pdf/dist/esm/Page/AnnotationLayer.css'
import 'react-pdf/dist/esm/Page/TextLayer.css'

interface ReactPDFViewerProps {
  pdfBlobUrl: string
  title: string
  fileName: string
  fileSize: number
  onDownload?: () => void
  onOpenExternal?: () => void
  zoom?: number
  onZoomChange?: (zoom: number) => void
  className?: string
}

export function ReactPDFViewer({
  pdfBlobUrl,
  title,
  fileName,
  fileSize,
  onDownload,
  onOpenExternal,
  zoom = 100,
  onZoomChange,
  className = "w-full h-full"
}: ReactPDFViewerProps) {
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    console.log('PDF loaded successfully with', numPages, 'pages')
    setNumPages(numPages)
    setLoading(false)
    setError(null)
  }, [])

  const onDocumentLoadError = useCallback((error: Error) => {
    // TODO: Replace with proper error reporting
  console.error('PDF load error:', error)
    setError(`Failed to load PDF document: ${error.message}`)
    setLoading(false)
  }, [])

  const onDocumentLoadProgress = useCallback(({ loaded, total }: { loaded: number; total: number }) => {
    * 100) + '%')
  }, [])

  const goToPrevPage = () => setPageNumber(prev => Math.max(prev - 1, 1))
  const goToNextPage = () => setPageNumber(prev => Math.min(prev + 1, numPages))
  const handleZoomIn = () => onZoomChange?.(Math.min(zoom + 25, 300))
  const handleZoomOut = () => onZoomChange?.(Math.max(zoom - 25, 50))

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-50`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-500">Loading PDF...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-50`}>
        <div className="text-center p-8 max-w-md">
          <div className="mb-6">
            <svg className="h-16 w-16 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className={`${typography.h4} mb-3`}>📄 PDF Viewer Error</h3>
          <p className={`${typography.body} ${textColors.secondary} mb-6`}>
            {error}. You can still download or open the PDF in a new tab.
          </p>
          <div className="flex items-center justify-center space-x-4">
            {onDownload && (
              <button onClick={onDownload} className={commonClasses.primaryButton}>
                <Download className={`${sizes.iconSmall} mr-2`} />
                Download PDF
              </button>
            )}
            {onOpenExternal && (
              <button onClick={onOpenExternal} className={commonClasses.secondaryButton}>
                <ExternalLink className={`${sizes.iconSmall} mr-2`} />
                Open in New Tab
              </button>
            )}
          </div>
          <div className={`mt-4 ${typography.caption} ${textColors.muted}`}>
            <p>File: {fileName}</p>
            <p>Size: {(fileSize / 1024 / 1024).toFixed(2)} MB</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className} flex flex-col bg-gray-50`}>
      {/* PDF Controls */}
      <div className="flex items-center justify-between p-3 border-b bg-white shadow-sm">
        <div className="flex items-center space-x-2">
          <button
            onClick={goToPrevPage}
            disabled={pageNumber <= 1}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Previous page"
          >
            <ChevronLeft className={sizes.iconSmall} />
          </button>
          
          <span className="text-sm text-gray-600 min-w-[4rem] text-center">
            {pageNumber} / {numPages}
          </span>
          
          <button
            onClick={goToNextPage}
            disabled={pageNumber >= numPages}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Next page"
          >
            <ChevronRight className={sizes.iconSmall} />
          </button>
        </div>

        <div className="flex items-center space-x-2">
          {onZoomChange && (
            <>
              <button
                onClick={handleZoomOut}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
                title="Zoom out"
              >
                <ZoomOut className={sizes.iconSmall} />
              </button>
              <span className="text-sm text-gray-600 min-w-[4rem] text-center">{zoom}%</span>
              <button
                onClick={handleZoomIn}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
                title="Zoom in"
              >
                <ZoomIn className={sizes.iconSmall} />
              </button>
            </>
          )}
          
          {onDownload && (
            <button
              onClick={onDownload}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
              title="Download PDF"
            >
              <Download className={sizes.iconSmall} />
            </button>
          )}
          
          {onOpenExternal && (
            <button
              onClick={onOpenExternal}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
              title="Open in new tab"
            >
              <ExternalLink className={sizes.iconSmall} />
            </button>
          )}
        </div>
      </div>

      {/* PDF Document */}
      <div className="flex-1 overflow-auto flex items-center justify-center p-4">
        <Document
          file={pdfBlobUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          onLoadProgress={onDocumentLoadProgress}
          options={{
            cMapUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,
            cMapPacked: true,
            standardFontDataUrl: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/standard_fonts/`,
            workerSrc: `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`
          }}
          loading={
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">Loading PDF...</p>
            </div>
          }
        >
          <Page
            pageNumber={pageNumber}
            scale={zoom / 100}
            loading={
              <div className="text-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto mb-2"></div>
                <p className="text-xs text-gray-500">Loading page...</p>
              </div>
            }
            className="shadow-lg"
          />
        </Document>
      </div>

      {/* PDF Info Footer */}
      <div className="border-t bg-gray-100 px-4 py-2">
        <div className="flex items-center justify-between text-xs text-gray-600">
          <div className="flex items-center space-x-4">
            <span>📄 {title}</span>
            <span>{fileName}</span>
          </div>
          <div className="flex items-center space-x-4">
            <span>{(fileSize / 1024 / 1024).toFixed(2)} MB</span>
            <span>🔒 Secure PDF Viewer</span>
          </div>
        </div>
      </div>
    </div>
  )
}