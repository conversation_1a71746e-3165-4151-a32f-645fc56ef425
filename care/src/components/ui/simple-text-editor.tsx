import React, { useCallback } from 'react';

interface SimpleTextEditorProps {
  value: string;
  onChange: (value: string | undefined) => void;
  height?: number;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

/**
 * A simple text editor component using a textarea
 * Fallback for when MarkdownEditor fails to load
 */
const SimpleTextEditor: React.FC<SimpleTextEditorProps> = ({
  value,
  onChange,
  height = 300,
  placeholder = 'Write your content here...',
  className = '',
  disabled = false,
}) => {
  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  return (
    <div className={`simple-text-editor ${className}`}>
      <textarea
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className={`
          w-full p-4 border border-gray-300 rounded-md resize-none
          focus:ring-2 focus:ring-primary-500 focus:border-primary-500
          disabled:bg-gray-100 disabled:cursor-not-allowed
          font-mono text-sm leading-relaxed
          ${disabled ? 'opacity-60' : ''}
        `}
        style={{ 
          height: `${height}px`,
          minHeight: `${height}px`,
        }}
        rows={Math.floor(height / 24)} // Approximate rows based on height
      />
    </div>
  );
};

export default SimpleTextEditor;