import React from 'react';

interface TextViewerProps {
  content: string;
  className?: string;
}

/**
 * A simple text viewer component that renders plain text or markdown content
 * This is a lightweight replacement for markdown-viewer
 */
const TextViewer: React.FC<TextViewerProps> = ({ content, className = '' }) => {
  if (!content) {
    return <div className={`text-gray-500 italic ${className}`}>No content available</div>;
  }

  return (
    <div className={`prose max-w-none ${className}`}>
      {/* Split content by newlines and render each paragraph */}
      {content.split('\n\n').map((paragraph, i) => {
        // Skip empty paragraphs
        if (!paragraph.trim()) return null;
        
        // Handle lists (both ordered and unordered)
        if (paragraph.trim().startsWith('- ') || paragraph.trim().startsWith('* ')) {
          const items = paragraph.split('\n').filter(item => item.trim());
          return (
            <ul key={i} className="list-disc pl-5 my-4">
              {items.map((item, j) => (
                <li key={j}>{item.replace(/^[-*]\s+/, '')}</li>
              ))}
            </ul>
          );
        }
        
        // Handle numbered lists
        if (/^\d+\.\s/.test(paragraph.trim())) {
          const items = paragraph.split('\n').filter(item => item.trim());
          return (
            <ol key={i} className="list-decimal pl-5 my-4">
              {items.map((item, j) => (
                <li key={j}>{item.replace(/^\d+\.\s+/, '')}</li>
              ))}
            </ol>
          );
        }
        
        // Handle headings (# Heading)
        if (paragraph.trim().startsWith('# ')) {
          return <h1 key={i} className="text-2xl font-bold my-4">{paragraph.replace(/^#\s+/, '')}</h1>;
        }
        
        if (paragraph.trim().startsWith('## ')) {
          return <h2 key={i} className="text-xl font-bold my-3">{paragraph.replace(/^##\s+/, '')}</h2>;
        }
        
        if (paragraph.trim().startsWith('### ')) {
          return <h3 key={i} className="text-lg font-bold my-2">{paragraph.replace(/^###\s+/, '')}</h3>;
        }
        
        // Handle bold text (**bold**)
        let formattedText = paragraph;
        formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Handle italic text (*italic*)
        formattedText = formattedText.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Handle links [text](url)
        formattedText = formattedText.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" class="text-blue-600 hover:underline">$1</a>');
        
        // Render as paragraph with dangerouslySetInnerHTML for the formatted text
        return (
          <p key={i} className="my-4" dangerouslySetInnerHTML={{ __html: formattedText }} />
        );
      })}
    </div>
  );
};

export default TextViewer;
