import React, { useEffect, useState, useRef, useCallback } from 'react';

interface RobustMarkdownEditorProps {
  value: string;
  onChange: (value: string | undefined) => void;
  height?: number;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

/**
 * A robust markdown editor that handles EasyMDE initialization issues
 * Falls back gracefully if EasyMDE fails to load
 */
const RobustMarkdownEditor: React.FC<RobustMarkdownEditorProps> = ({
  value,
  onChange,
  height = 300,
  placeholder = 'Write your content here...',
  className = '',
  disabled = false,
}) => {
  const [isEasyMDELoaded, setIsEasyMDELoaded] = useState(false);
  const [easyMDEError, setEasyMDEError] = useState<string | null>(null);
  const editorRef = useRef<HTMLTextAreaElement | null>(null);
  const easyMDERef = useRef<any>(null);
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle textarea change for fallback mode
  const handleTextareaChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  // Initialize EasyMDE with robust error handling
  const initializeEasyMDE = useCallback(async () => {
    if (!editorRef.current || easyMDERef.current || disabled) return;

    try {
      // Dynamic import to handle potential loading issues
      const EasyMDE = (await import('easymde')).default;
      
      // CSS should be imported at the top level, not dynamically
      // The CSS import issue suggests the build system might not be handling it properly

      // Small delay to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100));

      if (!editorRef.current) {
        throw new Error('Editor element not available');
      }

      const easyMDE = new EasyMDE({
        element: editorRef.current,
        autofocus: false,
        spellChecker: false, // Disable to reduce conflicts
        placeholder,
        status: false, // Disable status bar to reduce complexity
        initialValue: value || '',
        minHeight: `${height}px`,
        maxHeight: `${height * 1.5}px`,
        toolbar: [
          'bold',
          'italic',
          'heading',
          '|',
          'quote',
          'unordered-list',
          'ordered-list',
          '|',
          'link',
          '|',
          'preview',
          'fullscreen',
        ],
        renderingConfig: {
          singleLineBreaks: false,
          codeSyntaxHighlighting: false, // Disable to reduce complexity
        },
        forceSync: true, // Force synchronization
        tabSize: 2,
      });

      // Add custom CSS to replace Font Awesome icons with text labels
      const style = document.createElement('style');
      style.textContent = `
        .editor-toolbar .fa-bold::before { content: 'B'; font-weight: bold; font-family: serif; }
        .editor-toolbar .fa-italic::before { content: 'I'; font-style: italic; font-family: serif; }
        .editor-toolbar .fa-header::before { content: 'H'; font-weight: bold; font-family: serif; }
        .editor-toolbar .fa-quote-left::before { content: '"'; font-size: 16px; }
        .editor-toolbar .fa-list-ul::before { content: '•'; font-size: 16px; }
        .editor-toolbar .fa-list-ol::before { content: '1.'; font-size: 12px; }
        .editor-toolbar .fa-link::before { content: '🔗'; font-size: 12px; }
        .editor-toolbar .fa-eye::before { content: '👁'; font-size: 12px; }
        .editor-toolbar .fa-arrows-alt::before { content: '⛶'; font-size: 12px; }
        .editor-toolbar .fa::before { font-family: inherit !important; }
      `;
      document.head.appendChild(style);

      // Set up change handler without debouncing to prevent control loss
      easyMDE.codemirror.on('change', () => {
        const currentValue = easyMDE.value();
        onChange(currentValue);
      });

      easyMDERef.current = easyMDE;
      setIsEasyMDELoaded(true);
      setEasyMDEError(null);

      // Set initial value if provided
      if (value && value !== easyMDE.value()) {
        easyMDE.value(value);
      }

    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to initialize EasyMDE:', error);
      setEasyMDEError(error instanceof Error ? error.message : 'Unknown error');
      setIsEasyMDELoaded(false);
    }
  }, [value, onChange, placeholder, height, disabled]);

  // Initialize EasyMDE when component mounts
  useEffect(() => {
    if (disabled) return;

    // Clear any existing timeout
    if (initTimeoutRef.current) {
      clearTimeout(initTimeoutRef.current);
    }

    // Delay initialization to ensure DOM is ready
    initTimeoutRef.current = setTimeout(() => {
      initializeEasyMDE();
    }, 200);

    return () => {
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
      }
      
      // Cleanup EasyMDE instance
      if (easyMDERef.current) {
        try {
          easyMDERef.current.toTextArea();
          easyMDERef.current = null;
        } catch (error) {
          // TODO: Replace with proper error reporting
  console.error('Error cleaning up EasyMDE:', error);
        }
      }
    };
  }, [initializeEasyMDE, disabled]);

  // Update editor value when prop changes - but avoid infinite loops
  useEffect(() => {
    if (easyMDERef.current && isEasyMDELoaded) {
      const currentEditorValue = easyMDERef.current.value();
      if (value !== currentEditorValue) {
        // Only update if the values are actually different
        // This prevents the cursor from jumping when user is typing
        const cursorPos = easyMDERef.current.codemirror.getCursor();
        easyMDERef.current.value(value);
        // Restore cursor position
        easyMDERef.current.codemirror.setCursor(cursorPos);
      }
    }
  }, [value, isEasyMDELoaded]);

  // Render fallback textarea if EasyMDE failed to load
  if (easyMDEError || (!isEasyMDELoaded && editorRef.current)) {
    return (
      <div className={className}>
        {easyMDEError && (
          <div className="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
            <span className="font-medium">Markdown editor unavailable:</span> {easyMDEError}. Using simple text editor.
          </div>
        )}
        <textarea
          value={value}
          onChange={handleTextareaChange}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            w-full p-4 border border-gray-300 rounded-md resize-none
            focus:ring-2 focus:ring-primary-500 focus:border-primary-500
            disabled:bg-gray-100 disabled:cursor-not-allowed
            font-mono text-sm leading-relaxed
            ${disabled ? 'opacity-60' : ''}
          `}
          style={{ 
            height: `${height}px`,
            minHeight: `${height}px`,
          }}
          rows={Math.floor(height / 24)}
        />
      </div>
    );
  }

  return (
    <div className={className}>
      <textarea
        ref={editorRef}
        defaultValue={value}
        className={`
          w-full p-4 border border-gray-300 rounded-md resize-none
          focus:ring-2 focus:ring-primary-500 focus:border-primary-500
          font-mono text-sm leading-relaxed
        `}
        style={{
          height: `${height}px`,
          minHeight: `${height}px`,
          display: isEasyMDELoaded ? 'none' : 'block'
        }}
        placeholder={placeholder}
        onChange={handleTextareaChange}
      />
      {!isEasyMDELoaded && (
        <div className="mt-2 text-sm text-gray-500">
          Loading markdown editor...
        </div>
      )}
    </div>
  );
};

export default RobustMarkdownEditor;