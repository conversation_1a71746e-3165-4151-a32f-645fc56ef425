import { useState, useEffect } from 'react'
import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from 'react-doc-viewer'
import {
  ExternalLink,
  AlertCircle,
  Maximize2,
  Shield,
  Clock,
  User,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import {
  textColors,
  typography,
  sizes,
  commonClasses
} from '@/constants/theme'

// Helper function to get file type from filename
const getFileTypeFromName = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const mimeTypes: Record<string, string> = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'txt': 'text/plain',
    'html': 'text/html'
  };
  return mimeTypes[extension || ''] || 'application/octet-stream';
};

// Configure PDF.js worker to use locally hosted files
// This eliminates external CDN dependencies and authentication issues
if (typeof window !== 'undefined') {
  const win = window as any;
  
  // Use locally hosted PDF.js worker file (version 4.3.136 - ES module format)
  const localWorkerSrc = '/js/pdfjs/pdf.worker.min.js';
  
  // Method 1: Set global PDF.js worker (most common solution)
  const pdfjsLib = win.pdfjsLib;
  if (pdfjsLib && pdfjsLib.GlobalWorkerOptions) {
    pdfjsLib.GlobalWorkerOptions.workerSrc = localWorkerSrc;
  }
  
  // Method 2: Set via window.pdfjsLib for react-doc-viewer compatibility
  if (!win.pdfjsLib) {
    win.pdfjsLib = {
      GlobalWorkerOptions: {
        workerSrc: localWorkerSrc
      }
    };
  }
  
  // Method 3: Alternative configuration for different PDF.js versions
  if (win.pdfjs) {
    win.pdfjs.GlobalWorkerOptions = {
      workerSrc: localWorkerSrc
    };
  }
  
  // Method 4: Set PDFJS global (legacy compatibility)
  if (win.PDFJS) {
    win.PDFJS.workerSrc = localWorkerSrc;
  }
  
  // Method 5: Try to set it on the global scope for react-doc-viewer
  win.pdfjsWorkerSrc = localWorkerSrc;
  
  console.log('PDF.js worker configured to use local file:', localWorkerSrc);
}

interface DocumentData {
  id: string
  title: string
  originalFileName: string
  mimeType: string
  fileSize: number
  documentType?: 'medical_record' | 'lab_result' | 'prescription' | 'imaging' | 'insurance' | 'consent_form' | 'discharge_summary' | 'other'
  patientId?: string
  caseId?: string
  uploadedBy?: string
  createdAt?: string
  isConfidential?: boolean
  hipaaCompliant?: boolean
  documentSource?: 'patient' | 'medical'
}

interface UnifiedDocumentViewerProps {
  document: DocumentData
  onClose?: () => void
  showControls?: boolean
  showPatientInfo?: boolean
  auditLog?: boolean
  className?: string
  fullscreen?: boolean
  onFullscreenToggle?: () => void
  // Navigation props for multi-document scenarios
  documents?: DocumentData[]
  currentIndex?: number
  onNavigate?: (index: number) => void
}

export function UnifiedDocumentViewer({
  document,
  onClose,
  showControls = true,
  showPatientInfo = true,
  auditLog = true,
  className = "w-full h-full",
  fullscreen = false,
  onFullscreenToggle,
  documents = [],
  currentIndex = -1,
  onNavigate
}: UnifiedDocumentViewerProps) {
  const [error, setError] = useState<string | null>(null)
  const [accessLogged, setAccessLogged] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Inject CSS for full-screen document viewer with object-fit contain behavior
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    const styleId = 'unified-document-viewer-styles'
    
    // Remove existing styles if any
    const existingStyle = window.document.getElementById(styleId)
    if (existingStyle) {
      existingStyle.remove()
    }
    
    // Create and inject new styles
    const style = window.document.createElement('style')
    style.id = styleId
    style.textContent = `
      .react-doc-viewer {
        height: 100% !important;
        width: 100% !important;
        display: flex !important;
        flex-direction: column !important;
      }
      
      .react-doc-viewer > div {
        flex: 1 !important;
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
      }
      
      .react-doc-viewer iframe {
        width: 100% !important;
        height: 100% !important;
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
        border: none !important;
      }
      
      .react-doc-viewer img {
        width: 100% !important;
        height: 100% !important;
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
        object-position: center !important;
      }
      
      .react-doc-viewer canvas {
        width: 100% !important;
        height: 100% !important;
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
      }
      
      .react-doc-viewer .pdf-viewer {
        height: 100% !important;
        width: 100% !important;
        display: flex !important;
        flex-direction: column !important;
      }
      
      .react-doc-viewer .pdf-viewer > div {
        flex: 1 !important;
        height: 100% !important;
        overflow: auto !important;
      }
      
      .react-doc-viewer .pdf-viewer .react-pdf__Page {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;
      }
      
      .react-doc-viewer .pdf-viewer .react-pdf__Page__canvas {
        max-width: 100% !important;
        max-height: 100% !important;
        width: auto !important;
        height: auto !important;
        object-fit: contain !important;
      }
      
      .react-doc-viewer .office-viewer iframe {
        width: 100% !important;
        height: 100% !important;
        border: none !important;
      }
      
      .react-doc-viewer .txt-viewer {
        width: 100% !important;
        height: 100% !important;
        overflow: auto !important;
        padding: 1rem !important;
        font-family: monospace !important;
        white-space: pre-wrap !important;
      }
    `
    
    window.document.head.appendChild(style)
    
    // Cleanup function to remove styles when component unmounts
    return () => {
      const styleElement = window.document.getElementById(styleId)
      if (styleElement) {
        styleElement.remove()
      }
    }
  }, [])

  // Handle AbortError and other async cleanup issues
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      // Suppress AbortError in development mode (React double-rendering)
      if (event.error?.name === 'AbortError' && process.env.NODE_ENV === 'development') {
        console.warn('AbortError suppressed in development mode:', event.error.message)
        event.preventDefault()
        return false
      }
    }

    window.addEventListener('error', handleError)
    return () => {
      window.removeEventListener('error', handleError)
    }
  }, [])

  // Get document type info with patient-specific types
  const getDocumentTypeInfo = (type?: string) => {
    const types = {
      medical_record: { label: '🏥 Medical Record', color: 'bg-blue-100 text-blue-800', priority: 'high' },
      lab_result: { label: '🧪 Lab Result', color: 'bg-green-100 text-green-800', priority: 'high' },
      prescription: { label: '💊 Prescription', color: 'bg-purple-100 text-purple-800', priority: 'high' },
      imaging: { label: '📸 Medical Imaging', color: 'bg-indigo-100 text-indigo-800', priority: 'high' },
      insurance: { label: '🛡️ Insurance Document', color: 'bg-cyan-100 text-cyan-800', priority: 'medium' },
      consent_form: { label: '📝 Consent Form', color: 'bg-orange-100 text-orange-800', priority: 'high' },
      discharge_summary: { label: '📋 Discharge Summary', color: 'bg-teal-100 text-teal-800', priority: 'high' },
      other: { label: '📄 Other Document', color: 'bg-gray-100 text-gray-800', priority: 'low' }
    }
    return types[type as keyof typeof types] || types.other
  }

  const documentTypeInfo = getDocumentTypeInfo(document.documentType)

  // Create the proxy URL for the document with authentication token for react-doc-viewer
  // Since react-doc-viewer can't send custom headers, we'll include the token as a query parameter
  const getAuthenticatedDocumentUrl = () => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      return `/api/storage/documents/${document.id}/stream?token=${encodeURIComponent(token)}`
    }
    return `/api/storage/documents/${document.id}/stream`
  }
  
  const documentProxyUrl = getAuthenticatedDocumentUrl()

  // Log document access for HIPAA compliance
  useEffect(() => {
    if (auditLog && !accessLogged) {
      setAccessLogged(true)
    }
  }, [document.id, auditLog, accessLogged])

  // Navigation handlers
  const handlePrevious = () => {
    if (onNavigate && currentIndex > 0) {
      onNavigate(currentIndex - 1)
    }
  }

  const handleNext = () => {
    if (onNavigate && currentIndex < documents.length - 1) {
      onNavigate(currentIndex + 1)
    }
  }

  // Open in new tab with audit logging and authentication (no download)
  const handleOpenExternal = () => {
    try {
      if (auditLog) {
        }
      
      window.open(documentProxyUrl, '_blank')
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to open document:', error)
      setError('Failed to open document. Please try again.')
    }
  }

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-50`}>
        <div className="text-center p-8 max-w-md">
          <AlertCircle className={`${sizes.iconXLarge} text-red-400 mx-auto mb-4`} />
          <h3 className={`${typography.h4} mb-3`}>📄 Document Viewer Error</h3>
          <p className={`${typography.body} ${textColors.secondary} mb-6`}>
            {error}. You can still open the document in a new tab.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <button onClick={handleOpenExternal} className={commonClasses.primaryButton}>
              <ExternalLink className={`${sizes.iconSmall} mr-2`} />
              Open in New Tab
            </button>
          </div>
          <div className={`mt-4 ${typography.caption} ${textColors.muted}`}>
            <p>File: {document.originalFileName}</p>
            <p>Size: {formatFileSize(document.fileSize)}</p>
          </div>
        </div>
      </div>
    )
  }

  const containerClasses = fullscreen
    ? "fixed inset-0 z-50 bg-white flex flex-col"
    : `${className} flex flex-col h-full`

  return (
    <div className={containerClasses}>
      {/* Enhanced Controls Bar */}
      {showControls && (
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <h3 className={`${typography.h5} truncate max-w-md`}>{document.title}</h3>
              {document.documentType && (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${documentTypeInfo.color}`}>
                  {documentTypeInfo.label}
                </span>
              )}
              {document.isConfidential && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <Shield className="h-3 w-3 mr-1" />
                  Confidential
                </span>
              )}
            </div>
            
            {showPatientInfo && document.patientId && (
              <div className={`flex items-center space-x-4 ${typography.caption} ${textColors.muted}`}>
                <span className="flex items-center">
                  <User className={`${sizes.iconSmall} mr-1`} />
                  Patient ID: {document.patientId}
                </span>
                {document.createdAt && (
                  <span className="flex items-center">
                    <Clock className={`${sizes.iconSmall} mr-1`} />
                    {new Date(document.createdAt).toLocaleDateString()}
                  </span>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Navigation controls for multi-document scenarios */}
            {documents.length > 1 && currentIndex >= 0 && (
              <>
                <button
                  onClick={handlePrevious}
                  disabled={currentIndex <= 0}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Previous document"
                >
                  <ChevronLeft className={sizes.iconSmall} />
                </button>
                <span className="text-sm text-gray-600 px-2">
                  {currentIndex + 1} of {documents.length}
                </span>
                <button
                  onClick={handleNext}
                  disabled={currentIndex >= documents.length - 1}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Next document"
                >
                  <ChevronRight className={sizes.iconSmall} />
                </button>
              </>
            )}
            
            {onFullscreenToggle && (
              <button
                onClick={onFullscreenToggle}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
                title={fullscreen ? "Exit fullscreen" : "Fullscreen"}
              >
                <Maximize2 className={sizes.iconSmall} />
              </button>
            )}
            
            <button
              onClick={handleOpenExternal}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
              title="Open in new tab (Audit Logged)"
            >
              <ExternalLink className={sizes.iconSmall} />
            </button>

            {onClose && (
              <button
                onClick={onClose}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
                title="Close document"
              >
                <X className={sizes.iconSmall} />
              </button>
            )}
          </div>
        </div>
      )}

      {/* Document Content using react-doc-viewer with proxy URL */}
      <div className="flex-1 overflow-hidden bg-gray-100 relative">
        <DocViewer
          documents={[{
            uri: documentProxyUrl,
            fileType: document.mimeType || getFileTypeFromName(document.originalFileName)
          }]}
          pluginRenderers={DocViewerRenderers}
          config={{
            header: {
              disableHeader: true, // We have our own header
              disableFileName: true,
              retainURLParams: false
            }
          }}
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}
          theme={{
            primary: "#3b82f6",
            secondary: "#64748b",
            tertiary: "#f1f5f9",
            text_primary: "#1e293b",
            text_secondary: "#64748b",
            text_tertiary: "#94a3b8",
            disableThemeScrollbar: false
          }}
        />
        
      </div>

      {/* HIPAA Compliance Footer */}
      {auditLog && (
        <div className="border-t bg-blue-50 px-4 py-2">
          <div className="flex items-center justify-between text-xs text-blue-700">
            <div className="flex items-center space-x-2">
              <Shield className="h-3 w-3" />
              <span>🔒 HIPAA Compliant • All access is audited and logged • Download disabled for compliance</span>
            </div>
            <div className="flex items-center space-x-4">
              <span>Document ID: {document.id}</span>
              <span>Accessed: {new Date().toLocaleTimeString()}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}