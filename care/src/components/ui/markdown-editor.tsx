import React, { useEffect, useState, useRef } from 'react';
import EasyMD<PERSON> from 'easymde';
import 'easymde/dist/easymde.min.css';

// Define a type for EasyMDE instance with all needed methods
type EasyMDEInstance = EasyMDE & {
  togglePreview: () => void;
  toggleSideBySide: () => void;
  codemirror: any;
  value: (val?: string) => string;
  toTextArea: () => void;
}

interface MarkdownEditorProps {
  value: string;
  onChange: (value: string | undefined) => void;
  height?: number;
  preview?: 'edit' | 'preview' | 'split';
  placeholder?: string;
  className?: string;
}

/**
 * A markdown editor component using EasyMDE
 * Provides a rich editing experience with toolbar, preview, and other features
 */
const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  onChange,
  height = 300,
  preview = 'edit',
  placeholder = 'Write your content here...',
  className = '',
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const editorRef = useRef<HTMLTextAreaElement | null>(null);
  const easyMDERef = useRef<EasyMDEInstance | null>(null);

  // Initialize EasyMDE when component mounts
  useEffect(() => {
    setIsMounted(true);
    
    if (editorRef.current && !easyMDERef.current) {
      try {
        // Create EasyMDE instance
        const easyMDE = new EasyMDE({
          element: editorRef.current,
          autofocus: false,
          spellChecker: true,
          placeholder,
          status: ['lines', 'words', 'cursor'],
          initialValue: value || '',
          minHeight: `${height}px`,
          maxHeight: `${height * 1.5}px`,
          toolbar: [
            'bold',
            'italic',
            'heading',
            '|',
            'quote',
            'unordered-list',
            'ordered-list',
            '|',
            'link',
            '|',
            'preview',
            'fullscreen',
            '|',
            'guide',
          ],
          renderingConfig: {
            singleLineBreaks: false,
            codeSyntaxHighlighting: true,
          },
        });

        // Set up change handler
        easyMDE.codemirror.on('change', () => {
          const currentValue = easyMDE.value();
          onChange(currentValue);
        });

        // Store the instance for cleanup
        easyMDERef.current = easyMDE as EasyMDEInstance;

        // Set initial value if provided
        if (value) {
          easyMDE.value(value);
        }

        // Set preview mode if specified
        try {
          if (preview === 'preview') {
            (easyMDE as any).togglePreview();
          } else if (preview === 'split') {
            (easyMDE as any).toggleSideBySide();
          }
        } catch (error) {
          // TODO: Replace with proper error reporting
  console.error('Error setting preview mode:', error);
        }

      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Error initializing EasyMDE:', error);
      }
    }

    // Clean up on unmount
    return () => {
      if (easyMDERef.current) {
        try {
          easyMDERef.current.toTextArea();
          easyMDERef.current = null;
        } catch (error) {
          // TODO: Replace with proper error reporting
  console.error('Error cleaning up EasyMDE:', error);
        }
      }
    };
  }, []);

  // Update editor value when prop changes
  useEffect(() => {
    if (easyMDERef.current && value !== easyMDERef.current.value()) {
      easyMDERef.current.value(value);
    }
  }, [value]);

  if (!isMounted) {
    return (
      <div 
        className={`border rounded-md p-4 bg-gray-50 ${className}`}
        style={{ height: `${height}px` }}
      >
        <div className="animate-pulse flex space-x-4">
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <textarea ref={editorRef} style={{ display: 'none' }} />
    </div>
  );
};

export default MarkdownEditor;
