import * as React from "react"
import { cn } from "@/lib/utils"

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", ...props }, ref) => {
    const variants = {
      default: "bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",
      destructive: "bg-red-500 text-white hover:bg-red-600 focus:ring-red-500 shadow-lg hover:shadow-xl",
      outline: "border-2 border-primary-500 bg-transparent text-primary-600 hover:bg-primary-500 hover:text-white focus:ring-primary-500",
      secondary: "bg-white text-primary-600 hover:bg-primary-50 focus:ring-primary-500 border-2 border-primary-200 hover:border-primary-300 shadow-md hover:shadow-lg",
      ghost: "text-primary-600 hover:bg-primary-50 focus:ring-primary-500",
      link: "text-primary-600 hover:text-primary-500 underline-offset-4 hover:underline",
    }

    const sizes = {
      default: "px-6 py-3 text-sm",
      sm: "px-4 py-2 text-xs",
      lg: "px-8 py-4 text-base",
      icon: "p-3",
    }

    return (
      <button
        className={cn(
          "inline-flex items-center justify-center font-medium rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button }
