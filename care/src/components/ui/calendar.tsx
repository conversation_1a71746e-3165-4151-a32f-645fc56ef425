import * as React from "react"
import { cn } from "@/lib/utils"

export interface CalendarProps {
  selected?: Date
  onSelect?: (date: Date | undefined) => void
  disabled?: (date: Date) => boolean
  className?: string
}

const Calendar = React.forwardRef<HTMLDivElement, CalendarProps>(
  ({ className, selected, onSelect, disabled, ...props }, ref) => {
    const [currentMonth, setCurrentMonth] = React.useState(
      selected ? new Date(selected.getFullYear(), selected.getMonth(), 1) : new Date()
    )

    const daysInMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0).getDate()
    const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1).getDay()
    
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ]
    
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

    const goToPreviousMonth = () => {
      setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1))
    }

    const goToNextMonth = () => {
      setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1))
    }

    const handleDateClick = (day: number) => {
      const clickedDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
      if (disabled && disabled(clickedDate)) return
      onSelect?.(clickedDate)
    }

    const isDateSelected = (day: number) => {
      if (!selected) return false
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
      return date.toDateString() === selected.toDateString()
    }

    const isDateDisabled = (day: number) => {
      if (!disabled) return false
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
      return disabled(date)
    }

    // Generate calendar days including empty cells for proper alignment
    const calendarDays = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      calendarDays.push(null)
    }
    
    // Add actual days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      calendarDays.push(day)
    }

    return (
      <div
        ref={ref}
        className={cn("p-3 bg-white border border-gray-200 rounded-md shadow-sm", className)}
        {...props}
      >
        {/* Header with month/year and navigation */}
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={goToPreviousMonth}
            className="p-1 hover:bg-gray-100 rounded"
            type="button"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <h2 className="text-sm font-medium text-gray-900">
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </h2>
          
          <button
            onClick={goToNextMonth}
            className="p-1 hover:bg-gray-100 rounded"
            type="button"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Day names header */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {dayNames.map((day) => (
            <div key={day} className="p-2 text-xs font-medium text-gray-500 text-center">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => (
            <div key={index} className="aspect-square">
              {day && (
                <button
                  onClick={() => handleDateClick(day)}
                  disabled={isDateDisabled(day)}
                  className={cn(
                    "w-full h-full text-sm rounded hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500",
                    isDateSelected(day)
                      ? "bg-primary-600 text-white hover:bg-primary-700"
                      : "text-gray-900",
                    isDateDisabled(day)
                      ? "text-gray-400 cursor-not-allowed hover:bg-transparent"
                      : "cursor-pointer"
                  )}
                  type="button"
                >
                  {day}
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    )
  }
)
Calendar.displayName = "Calendar"

export { Calendar }
