# Use Node.js 22 LTS Alpine for smaller image size
FROM node:22-alpine

WORKDIR /app

# Copy care app package files first for better caching
COPY care/package*.json ./care/
WORKDIR /app/care

# Install dependencies
RUN npm install --legacy-peer-deps

# Go back to app root and copy shared folder
WORKDIR /app
COPY shared/ ./shared/

# Copy care app source code
COPY care/ ./care/

# Set working directory back to care
WORKDIR /app/care

# Expose port
EXPOSE 3000

# Start development server with hot reload
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
