{"name": "continuia-care", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.2", "@lexical/link": "^0.33.1", "@lexical/list": "^0.33.1", "@lexical/mark": "^0.33.1", "@lexical/overflow": "^0.33.1", "@lexical/plain-text": "^0.33.1", "@lexical/react": "^0.33.1", "@lexical/rich-text": "^0.33.1", "@lexical/selection": "^0.33.1", "@lexical/utils": "^0.33.1", "@lexical/yjs": "^0.33.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tiptap/core": "^2.26.1", "@tiptap/extension-blockquote": "^2.26.1", "@tiptap/extension-bullet-list": "^2.26.1", "@tiptap/extension-character-count": "^2.26.1", "@tiptap/extension-collaboration": "^2.26.1", "@tiptap/extension-collaboration-cursor": "^2.26.1", "@tiptap/extension-dropcursor": "^2.26.1", "@tiptap/extension-gapcursor": "^2.26.1", "@tiptap/extension-list-item": "^2.26.1", "@tiptap/extension-ordered-list": "^2.26.1", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@uiw/react-md-editor": "^4.0.8", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "debounce": "^2.2.0", "easymde": "^2.20.0", "fuse.js": "^7.1.0", "lexical": "^0.33.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-doc-viewer": "^0.1.14", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-simplemde-editor": "^5.2.0", "rehype-sanitize": "^6.0.0", "tailwind-merge": "^2.0.0", "y-indexeddb": "^9.0.12", "y-prosemirror": "^1.2.12", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.21", "cypress": "^14.5.3", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "prettier": "^3.1.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}