import { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'

interface ProtectedRouteProps {
  children: ReactNode
  requiredRole?: 'patient' | 'doctor' | 'agent' | 'admin' | Array<'patient' | 'doctor' | 'agent' | 'admin'>
}

export function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { user, isLoading } = useAuth()
  const location = useLocation()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  if (requiredRole) {
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
    
    if (!allowedRoles.includes(user.role)) {
      // Redirect to appropriate dashboard based on user role
      const roleRoutes = {
        patient: '/cases',
        doctor: '/cases',
        agent: '/agent/assignments',
        admin: '/cases'
      }
      return <Navigate to={roleRoutes[user.role] || '/'} replace />
    }
  }

  return <>{children}</>
}
