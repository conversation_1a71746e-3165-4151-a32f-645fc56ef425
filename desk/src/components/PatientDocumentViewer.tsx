import { UnifiedDocumentViewer } from '@/components/ui/UnifiedDocumentViewer'

interface PatientDocument {
  id: string
  title: string
  originalFileName: string
  mimeType: string
  fileSize: number
  documentType: 'medical_record' | 'lab_result' | 'prescription' | 'imaging' | 'insurance' | 'consent_form' | 'discharge_summary' | 'other'
  patientId: string
  uploadedBy: string
  createdAt: string
  isConfidential?: boolean
  hipaaCompliant?: boolean
}

interface PatientDocumentViewerProps {
  document: PatientDocument
  streamUrl: string
  onClose?: () => void
  showControls?: boolean
  showPatientInfo?: boolean
  auditLog?: boolean
}

export function PatientDocumentViewer({
  document,
  onClose,
  showControls = true,
  showPatientInfo = true,
  auditLog = true
}: PatientDocumentViewerProps) {
  return (
    <UnifiedDocumentViewer
      document={{
        id: document.id,
        title: document.title,
        originalFileName: document.originalFileName,
        mimeType: document.mimeType,
        fileSize: document.fileSize,
        documentType: document.documentType,
        patientId: document.patientId,
        uploadedBy: document.uploadedBy,
        createdAt: document.createdAt,
        isConfidential: document.isConfidential,
        hipaaCompliant: document.hipaaCompliant,
        documentSource: 'patient'
      }}
      onClose={onClose}
      showControls={showControls}
      showPatientInfo={showPatientInfo}
      auditLog={auditLog}
      className="w-full h-full"
    />
  )
}