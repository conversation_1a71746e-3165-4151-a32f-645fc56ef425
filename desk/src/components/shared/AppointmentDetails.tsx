import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { apiClient } from '@/services/api'
import { 
  Calendar,
  Clock,
  User,
  Video,
  MapPin,
  ArrowLeft,
  Mail,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  Edit,
  Trash2
} from 'lucide-react'

interface Appointment {
  id: string
  patientId: string
  doctorId: string
  caseId?: string
  appointmentType: 'consultation' | 'follow_up' | 'review' | 'emergency'
  scheduledAt: string
  duration: number
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
  notes?: string
  meetingLink?: string
  createdAt: string
  updatedAt: string
  // Joined data from API
  doctor?: {
    id: string
    firstName: string
    lastName: string
    email: string
    specialization?: string
    profileImage?: string
  }
  patient?: {
    id: string
    firstName: string
    lastName: string
    email: string
    profileImage?: string
  }
  case?: {
    id: string
    title: string
    description: string
    status: string
    urgencyLevel: string
    specialtyRequired: string
  }
}

interface AppointmentDetailsProps {
  appointmentId?: string // If not provided, will use URL param
  userRole?: 'patient' | 'doctor' | 'admin' | 'agent'
  onBack?: () => void // Custom back handler
  showActions?: boolean // Show edit/delete actions
  embedded?: boolean // If true, don't show full page layout
}

const statusConfig = {
  scheduled: { label: 'Scheduled', color: 'bg-blue-100 text-blue-800', icon: Clock },
  confirmed: { label: 'Confirmed', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  in_progress: { label: 'In Progress', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  completed: { label: 'Completed', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: XCircle },
  no_show: { label: 'No Show', color: 'bg-gray-100 text-gray-800', icon: AlertCircle }
}

const typeConfig = {
  consultation: { label: 'Consultation', color: 'bg-blue-50 text-blue-700' },
  follow_up: { label: 'Follow-up', color: 'bg-green-50 text-green-700' },
  review: { label: 'Review', color: 'bg-purple-50 text-purple-700' },
  emergency: { label: 'Emergency', color: 'bg-red-50 text-red-700' }
}

export function AppointmentDetails({
  appointmentId: propAppointmentId,
  userRole,
  onBack,
  showActions = false,
  embedded = false
}: AppointmentDetailsProps) {
  const { appointmentId: urlAppointmentId } = useParams<{ appointmentId: string }>()
  const navigate = useNavigate()
  const [appointment, setAppointment] = useState<Appointment | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Use prop appointmentId if provided, otherwise use URL param
  const effectiveAppointmentId = propAppointmentId || urlAppointmentId

  useEffect(() => {
    const loadAppointment = async () => {
      if (!effectiveAppointmentId) {
        setError('No appointment ID provided')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)
        
        // Get appointment details
        // @ts-ignore - Using private request method for appointment details
        const response = await (apiClient as any).request(`/appointments/${effectiveAppointmentId}`)
        
        // Handle response format
        let appointmentData: Appointment | null = null
        if (response && (response as any).appointment) {
          appointmentData = (response as any).appointment
        } else if (response && (response as any).data) {
          appointmentData = (response as any).data
        } else if (response) {
          appointmentData = response as Appointment
        }
        
        setAppointment(appointmentData)
        
      } catch (error: any) {
        // TODO: Replace with proper error reporting
  console.error('Error loading appointment:', error)
        setError('Failed to load appointment details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    loadAppointment()
  }, [effectiveAppointmentId])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      // Default navigation based on user role
      if (userRole === 'patient') {
        navigate('/appointments')
      } else if (userRole === 'doctor') {
        navigate('/doctor/appointments')
      } else {
        navigate('/admin/appointments')
      }
    }
  }

  const handleEdit = () => {
    // Navigate to edit page based on user role
    if (userRole === 'doctor') {
      navigate(`/doctor/appointments/${effectiveAppointmentId}/edit`)
    } else {
      navigate(`/admin/appointments/${effectiveAppointmentId}/edit`)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this appointment?')) {
      return
    }
    
    try {
      // @ts-ignore - Using private request method for appointment deletion
      await (apiClient as any).request(`/appointments/${effectiveAppointmentId}`, {
        method: 'DELETE'
      })
      
      // Navigate back after successful deletion
      handleBack()
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error deleting appointment:', error)
      alert('Failed to delete appointment. Please try again.')
    }
  }

  if (loading) {
    return (
      <div className={embedded ? "flex items-center justify-center p-8" : "min-h-screen bg-gray-50 flex items-center justify-center"}>
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error || !appointment) {
    return (
      <div className={embedded ? "text-center p-8" : "min-h-screen bg-gray-50 flex items-center justify-center"}>
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Appointment</h2>
          <p className="text-gray-600 mb-4">{error || 'Appointment not found'}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  const StatusIcon = statusConfig[appointment.status as keyof typeof statusConfig].icon
  const isUpcoming = new Date(appointment.scheduledAt) > new Date()

  const content = (
    <>
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          {!embedded && (
            <button
              onClick={handleBack}
              className="mr-4 p-2 rounded-md hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Appointment Details</h1>
            <p className="text-gray-600 mt-2">
              {formatDate(appointment.scheduledAt)} at {formatTime(appointment.scheduledAt)}
            </p>
          </div>
        </div>
        
        {showActions && (userRole === 'doctor' || userRole === 'admin') && (
          <div className="flex items-center space-x-2">
            <button
              onClick={handleEdit}
              className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </button>
            <button
              onClick={handleDelete}
              className="flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Appointment Overview Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Appointment Overview</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[appointment.status as keyof typeof statusConfig].color}`}>
                    <StatusIcon className="h-3 w-3 mr-1" />
                    {statusConfig[appointment.status as keyof typeof statusConfig].label}
                  </span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig[appointment.appointmentType as keyof typeof typeConfig].color}`}>
                    {typeConfig[appointment.appointmentType as keyof typeof typeConfig].label}
                  </span>
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-3" />
                  <span>{formatDate(appointment.scheduledAt)}</span>
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-3" />
                  <span>{formatTime(appointment.scheduledAt)} ({appointment.duration} minutes)</span>
                </div>
              </div>
              
              <div className="space-y-4">
                {appointment.meetingLink && isUpcoming && appointment.status !== 'cancelled' && (
                  <div>
                    <a
                      href={appointment.meetingLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                    >
                      <Video className="h-4 w-4 mr-2" />
                      Join Call
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Notes Card */}
          {appointment.notes && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Notes</h2>
              <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 whitespace-pre-wrap">{appointment.notes}</p>
              </div>
            </div>
          )}
        </div>

        {/* Right Column - Related Info */}
        <div className="space-y-6">
          {/* Patient Info Card (for doctors/admins) */}
          {userRole !== 'patient' && appointment.patient && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Patient Information</h2>
              
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-900">
                    {appointment.patient.firstName} {appointment.patient.lastName}
                  </p>
                  <p className="text-sm text-gray-600">{appointment.patient.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Doctor Info Card (for patients) */}
          {userRole === 'patient' && appointment.doctor && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Doctor Information</h2>
              
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-900">
                    Dr. {appointment.doctor.firstName} {appointment.doctor.lastName}
                  </p>
                  {appointment.doctor.specialization && (
                    <p className="text-sm text-gray-600">{appointment.doctor.specialization}</p>
                  )}
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center">
                  <Mail className="h-4 w-4 text-gray-400 mr-3" />
                  <p className="text-sm text-gray-600">{appointment.doctor.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Case Info Card */}
          {appointment.case && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Related Case</h2>
                <MapPin className="h-5 w-5 text-blue-600" />
              </div>
              
              <div className="space-y-4">
                <div>
                  <p className="text-base font-medium text-gray-900 mb-2">{appointment.case.title}</p>
                  {appointment.case.description && (
                    <p className="text-sm text-gray-600 leading-relaxed mb-3">{appointment.case.description}</p>
                  )}
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {appointment.case.status && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {appointment.case.status}
                    </span>
                  )}
                  {appointment.case.urgencyLevel && (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      appointment.case.urgencyLevel === 'urgent' ? 'bg-red-100 text-red-800' :
                      appointment.case.urgencyLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                      appointment.case.urgencyLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {appointment.case.urgencyLevel} priority
                    </span>
                  )}
                  {appointment.case.specialtyRequired && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      {appointment.case.specialtyRequired}
                    </span>
                  )}
                </div>
                
                <div className="pt-3 border-t border-gray-200">
                  <button 
                    onClick={() => {
                      // Navigate to case details using unified route
                      navigate(`/cases/${appointment.case?.id}`)
                    }}
                    className="w-full flex items-center justify-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    View Full Case Details
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Appointment History Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Appointment History</h2>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Created:</span>
                <span className="text-gray-900">{formatDateTime(appointment.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Last Updated:</span>
                <span className="text-gray-900">{formatDateTime(appointment.updatedAt)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )

  if (embedded) {
    return <div className="space-y-6">{content}</div>
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {content}
      </div>
    </div>
  )
}

export default AppointmentDetails
