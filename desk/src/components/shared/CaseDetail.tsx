import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { usePermissions } from '@/hooks/usePermissions'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import { Documents } from '@/components/Documents'
import { CollabClinicalEditor } from './CollabClinicalEditor'
import DynamicNoteSections from './DynamicNoteSections'
import DoctorAssignmentModal from './DoctorAssignmentModal'
import CaseDiscussions from './CaseDiscussions'
import MedicalOpinion from './MedicalOpinion'
import { CaseData } from '@/types/case'
import {
  Activity,
  AlertCircle,
  ArrowLeft,
  BookOpen,
  CheckCircle,
  ChevronDown,
  Clock,
  FileText,
  FolderOpen,
  Globe,
  MessageSquare,
  Stethoscope,
  User,
  Users,
  XCircle
} from 'lucide-react'
// Using Globe from lucide-react, not react-feather

// Using CaseData from @/types/case

const statusConfig = {
  draft: { label: 'Draft', color: 'bg-gray-100 text-gray-800', icon: FileText },
  submitted: { label: 'Submitted', color: 'bg-primary-100 text-primary-800', icon: Clock },
  in_review: { label: 'In Review', color: 'bg-primary-100 text-primary-800', icon: Clock },
  assigned: { label: 'Assigned', color: 'bg-primary-500 text-white', icon: User },
  in_progress: { label: 'In Progress', color: 'bg-primary-600 text-white', icon: Activity },
  completed: { label: 'Completed', color: 'bg-green-500 text-white', icon: CheckCircle },
  archived: { label: 'Archived', color: 'bg-gray-100 text-gray-800', icon: XCircle },
  cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: XCircle }
}

const urgencyConfig = {
  low: { label: 'Low', color: 'bg-green-100 text-green-800' },
  medium: { label: 'Medium', color: 'bg-primary-100 text-primary-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-500 text-white' }
}

// Helper function to calculate age from date of birth
const calculateAge = (dateOfBirth: string): number => {
  const dob = new Date(dateOfBirth);
  const today = new Date();
  let age = today.getFullYear() - dob.getFullYear();
  const monthDiff = today.getMonth() - dob.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
    age--;
  }

  return age;
};

interface CaseDetailProps {
  caseId?: string
  embedded?: boolean
}

export function CaseDetail({ caseId }: CaseDetailProps) {
  console.log('🚨 CASE DETAIL COMPONENT LOADED - NEW VERSION WITH BULK NOTES');
  const params = useParams<{ id: string }>()
  const id = caseId || params.id
  const navigate = useNavigate()
  const permissions = usePermissions()
  const { user } = useAuth()

  const [caseData, setCaseData] = useState<CaseData | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'documents' | 'timeline' | 'messages' | 'opinions'>('overview')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [doctorAcceptanceStatus, setDoctorAcceptanceStatus] = useState<{
    isDoctor: boolean;
    isAssigned: boolean;
    hasAccepted: boolean;
    message: string;
  } | null>(null)

  // Clinical notes state - optimized to load all data once
  const [clinicalNotes, setClinicalNotes] = useState<{
    symptoms?: string;
    pastMedicalHistory?: string;
    currentMedications?: string;
    caseDescription?: string;
    [key: string]: any;
  } | null>(null)

  // All unified notes state - fetch once and distribute to editors
  const [allNotes, setAllNotes] = useState<{[noteType: string]: any}>({})
  const [notesLoading, setNotesLoading] = useState(false)

  const [caseSectionsExpanded, setCaseSectionsExpanded] = useState(true)
  const [rightSidebarWidth, setRightSidebarWidth] = useState(480)
  const [isResizing, setIsResizing] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // Medical opinion submission state
  // Doctor assignment modal state
  const [isDoctorModalOpen, setIsDoctorModalOpen] = useState(false)

  // Permission check helper
  const canEdit = (resourceType: string, action: string, resource?: any) => {
    return permissions.checkPermission(resourceType, action, resource)
  }

  const canUpdateCase = caseData ? canEdit('case', 'update', caseData) : false
  const isDoctor = user?.role === 'doctor'

  // Determine if notes should be read-only based on case status and user role
  const isNotesReadOnly = () => {
    if (!caseData || !user) return true;
  
    // If the case is completed, all fields should be read-only for all roles.
    if (caseData.status === 'completed') {
      return true;
    }
  
    // Admins and agents can always edit unless the case is completed.
    if (user.role === 'admin' || user.role === 'agent') return false;
  
    // Doctors can edit only if they have accepted the case assignment and the case is not completed.
    if (user.role === 'doctor') {
      if (!doctorAcceptanceStatus) return true; // Loading state - be safe and make read-only.
  
      // Doctor must be assigned and have accepted the case.
      return !(doctorAcceptanceStatus.isAssigned && doctorAcceptanceStatus.hasAccepted);
    }
  
    // Patients can only edit their own cases when in draft status.
    if (user.role === 'patient') {
      return caseData.status !== 'draft';
    }
  
    // Default to read-only.
    return true;
  };

  // Drag resize handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return
    const newWidth = window.innerWidth - e.clientX
    if (newWidth >= 320 && newWidth <= 1600) {
      setRightSidebarWidth(newWidth)
    }
  }

  const handleMouseUp = () => {
    setIsResizing(false)
  }

  // Add/remove mouse event listeners
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    } else {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isResizing])

  // Debug logging for permissions (commented out to prevent excessive logging)
  // console.log('🔍 Debug - Case Data:', caseData)
  // console.log('🔍 Debug - Can Update Case:', canUpdateCase)
  // console.log('🔍 Debug - Permissions Object:', permissions)
  // console.log('🔍 Debug - Is Doctor:', isDoctor)

  // Load clinical notes using optimized endpoint (DEPRECATED - now using bulk notes API)
  const loadClinicalNotes = async (caseId: string) => {
    try {
      setLoading(true)
      const response = await apiClient.getCaseNotes(caseId)

      // Handle both response formats
      if (response && response.clinicalData) {
        // New simplified format from /cases/:id/notes
        setClinicalNotes(response.clinicalData)
      } else if (response && response.notes && Array.isArray(response.notes) && response.notes.length > 0) {
        // Complex format from /case-notes/:caseId/notes - extract from latest note
        const latestNote = response.notes[0];
        const structured = latestNote.structuredContent || {};

        // Extract clinical data from the complex nested structure
        const extractedData = {
          symptoms: '',
          medicalHistory: '',
          currentMedications: '',
          caseDescription: ''
        };

        // Extract symptoms
        if (structured.symptoms && typeof structured.symptoms === 'object' && structured.symptoms.content) {
          extractedData.symptoms = structured.symptoms.content;
        } else if (typeof structured.symptoms === 'string') {
          extractedData.symptoms = structured.symptoms;
        }

        // Extract medical history
        if (structured.medicalHistory && typeof structured.medicalHistory === 'object' && structured.medicalHistory.content) {
          extractedData.medicalHistory = structured.medicalHistory.content;
        } else if (typeof structured.medicalHistory === 'string') {
          extractedData.medicalHistory = structured.medicalHistory;
        } else if (typeof structured.pastMedicalHistory === 'string') {
          extractedData.medicalHistory = structured.pastMedicalHistory;
        }

        // Extract current medications
        if (structured.currentMedications && typeof structured.currentMedications === 'object' && structured.currentMedications.content) {
          extractedData.currentMedications = structured.currentMedications.content;
        } else if (typeof structured.currentMedications === 'string') {
          extractedData.currentMedications = structured.currentMedications;
        }

        // Extract case description
        if (structured.caseDescription && typeof structured.caseDescription === 'object' && structured.caseDescription.content) {
          extractedData.caseDescription = structured.caseDescription.content;
        } else if (typeof structured.caseDescription === 'string') {
          extractedData.caseDescription = structured.caseDescription;
        }

        console.log('Extracted clinical data:', extractedData);
        setClinicalNotes(extractedData);
      } else {
        setClinicalNotes(null)
      }
    } catch (err: any) {
      // TODO: Replace with proper error reporting
  console.error('Error loading clinical notes:', err)
      setClinicalNotes(null)
    } finally {
      setLoading(false)
    }
  }

  // Fetch case data and doctor acceptance status
  useEffect(() => {
    const fetchCaseData = async () => {
      if (!id) {
        setError('No case ID provided')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)

        // Fetch case data and doctor acceptance status in parallel
        const [caseResponse, acceptanceResponse] = await Promise.all([
          apiClient.getCase(id),
          user?.role === 'doctor' ? apiClient.getDoctorAcceptanceStatus(id) : Promise.resolve(null)
        ])

        if (!caseResponse) {
          throw new Error('No response received from API')
        }

        const caseData = (caseResponse as any).case
        if (!caseData) {
          throw new Error('Case data not found in response')
        }

        // Set doctor acceptance status
        if (acceptanceResponse) {
          setDoctorAcceptanceStatus(acceptanceResponse)
          console.log('Doctor acceptance status:', acceptanceResponse)
        }

        // Validate and sanitize case data with fallbacks
        const sanitizedCaseData: CaseData = {
          id: caseData.id || id,
          patientId: caseData.patientId || '',
          title: caseData.title || 'Untitled Case',
          description: caseData.description || 'No description available',
          status: caseData.status || 'draft',
          urgencyLevel: caseData.urgencyLevel || 'medium',
          symptoms: caseData.symptoms || '',
          medicalHistory: caseData.medicalHistory || '',
          currentMedications: caseData.currentMedications || '',
          createdAt: caseData.createdAt || new Date().toISOString(),
          updatedAt: caseData.updatedAt || new Date().toISOString(),
          patient: caseData.patient ? {
            id: caseData.patient.id || '',
            name: caseData.patient.name || 'Unknown Patient'
          } : undefined,
          assignedDoctors: Array.isArray(caseData.assignedDoctors) ? caseData.assignedDoctors.map((doc: any) => ({
            id: doc.id || '',
            name: doc.name || 'Unknown Doctor',
            specialization: doc.specialization || 'General Practice',
            assignedAt: doc.assignedAt || new Date().toISOString()
          })) : [],
          documents: Array.isArray(caseData.documents) ? caseData.documents.map((doc: any) => ({
            id: doc.id || '',
            title: doc.title || 'Untitled Document',
            fileName: doc.fileName || 'unknown.pdf',
            createdAt: doc.createdAt || new Date().toISOString()
          })) : []
        }

        setCaseData(sanitizedCaseData)

        // Clinical notes will be loaded via bulk notes API in separate useEffect

      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error fetching case:', err)
        const errorMessage = err instanceof Error ? err.message : 'Failed to load case details'
        setError(errorMessage)
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchCaseData()
    }
  }, [id, user?.role])

  // Load all notes in bulk after case data is loaded
  useEffect(() => {
    const loadNotesInBulk = async () => {
      if (!caseData?.id) {
        return;
      }

      try {
        setNotesLoading(true);
        const notesResponse = await apiClient.getAllNotesBulk(caseData.id);
        setAllNotes(notesResponse.notesByType || {});

        // Extract clinical notes from bulk response
        const notesByType = notesResponse.notesByType || {};
        const extractedClinicalNotes = {
          symptoms: notesByType.symptoms?.content || '',
          pastMedicalHistory: notesByType.medical_history?.content || '',
          currentMedications: notesByType.current_medications?.content || '',
          caseDescription: notesByType.case_description?.content || ''
        };
        setClinicalNotes(extractedClinicalNotes);

        console.log('✅ Loaded notes in bulk:', Object.keys(notesResponse.notesByType || {}).length, 'note types');
        console.log('✅ Extracted clinical notes:', extractedClinicalNotes);
      } catch (notesError) {
        // TODO: Replace with proper error reporting
  console.error('❌ Error loading notes in bulk:', notesError);
        setAllNotes({}); // Set empty object on error
        setClinicalNotes(null); // Reset clinical notes on error
      } finally {
        setNotesLoading(false);
      }
    };

    console.log('🔄 Bulk notes useEffect triggered, caseData.id:', caseData?.id);
    console.log('🔄 NEW CODE IS RUNNING - BULK NOTES IMPLEMENTATION');
    loadNotesInBulk();
  }, [caseData?.id]); // Run when caseData.id changes

  // Loading state - Enhanced
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Loading Case Details</h3>
              <p className="text-gray-600">Please wait while we fetch the case information...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state - Enhanced
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-20">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertCircle className="h-10 w-10 text-red-500" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Case</h3>
            <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-full hover:bg-primary-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-semibold"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Case not found state - Enhanced
  if (!caseData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-20">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertCircle className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Case Not Found</h3>
            <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">The requested case could not be found or you may not have permission to view it.</p>
            <button
              onClick={() => navigate('/cases')}
              className="inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-full hover:bg-primary-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-semibold"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Cases
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Format doctor assignments for the modal
  const formattedAssignments = caseData?.assignedDoctors?.map(doctor => ({
    id: doctor.id || '',
    doctorId: doctor.id,
    name: doctor.name,
    specialization: doctor.specialization
  })) || [];

  // Handle doctor assignment changes
  const handleDoctorAssignmentChange = () => {
    // Refresh case data to get updated doctor assignments
    if (id) {
      // Re-fetch case data to get updated doctor assignments
      const fetchUpdatedCaseData = async () => {
        try {
          console.log('Refreshing case data after doctor assignment change');
          setLoading(true); // Show loading state while refreshing

          const response = await apiClient.getCase(id);
          if (response && response.case) {
            // Process the case data to ensure assigned doctors are properly formatted
            const updatedCase = response.case;
            // Update the case data state
            setCaseData(updatedCase);

            // Show success message
            alert('Doctor assignment updated successfully!');
          }
        } catch (err) {
          // TODO: Replace with proper error reporting
  console.error('Error refreshing case data:', err);
          alert('Error refreshing case data. Please reload the page.');
        } finally {
          setLoading(false); // Hide loading state
        }
      };

      fetchUpdatedCaseData();
    }
  };

  const handleSubmitCase = async () => {
    if (!caseData) return;

    try {
      setSubmitting(true);

      // Update the case status to 'submitted'
      await apiClient.updateCase(caseData.id, { status: 'submitted' });

      // Refresh case data
      if (id) {
        const response = await apiClient.getCase(id);
        if (response && (response as any).case) {
          setCaseData((response as any).case);
        }
      }

      // Show success message
      toast.success('Case submitted successfully!');
      setSubmitting(false);
    } catch (error) {
      console.error('Error submitting case:', error);
      setSubmitting(false);
      toast.error('Failed to submit case. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100" data-testid="case-detail">
      {/* Left Sidebar - Modern Navigation */}
      <div className="w-80 bg-white/90 backdrop-blur-sm border-r border-primary-200 flex flex-col fixed left-0 top-0 h-screen z-10 shadow-xl">
        {/* Case Header */}
        <div className="p-6 border-b border-primary-200 bg-gradient-to-r from-primary-50 to-white">
          <div className="flex items-center mb-4">
            <button
              onClick={() => navigate('/cases')}
              className="flex items-center text-primary-600 hover:text-primary-700 transition-colors font-medium"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Cases
            </button>
          </div>
          <h1 className="text-xl font-bold text-gray-900 mb-2">{caseData.title}</h1>
          <p className="text-sm text-primary-600 font-medium">Case #{caseData.id.slice(-7, -4)}-{caseData.id.slice(-4)}</p>
        </div>

        {/* Status and Priority - Enhanced */}
        <div className="px-6 py-4 border-b border-primary-200">
          <div className="space-y-3">
            {/* Status Label */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Status</span>
              <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm ${statusConfig[caseData.status]?.color}`}>
                {React.createElement(statusConfig[caseData.status]?.icon, { className: "h-3 w-3 mr-1.5" })}
                {statusConfig[caseData.status]?.label}
              </span>
            </div>

            {/* Priority Label */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Priority</span>
              <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm ${urgencyConfig[caseData.urgencyLevel]?.color}`}>
                <AlertCircle className="h-3 w-3 mr-1.5" />
                {urgencyConfig[caseData.urgencyLevel]?.label}
              </span>
            </div>
          </div>
        </div>

        {/* Modern Navigation */}
        <div className="flex-1 p-6">
          <h3 className="text-sm font-semibold text-primary-900 uppercase tracking-wide mb-4">Navigation</h3>
          <nav className="space-y-2">
            {/* Main Tab Navigation */}
            <div className="">
              <div className="text-xs font-medium text-primary-600 uppercase tracking-wide px-3 py-1 mb-3">Main Sections</div>
              {[
                { id: 'overview', label: 'Case Overview', icon: BookOpen, color: 'text-primary-600' },
                { id: 'documents', label: 'Documents', icon: FolderOpen, color: 'text-blue-600' },
                { id: 'timeline', label: 'Timeline', icon: Clock, color: 'text-green-600' },
                { id: 'messages', label: 'Messages', icon: MessageSquare, color: 'text-orange-600' },
                { id: 'opinions', label: 'Medical Opinions', icon: Users, color: 'text-red-600' }
              ].map((tab) => (
                <div key={tab.id}>
                  <button
                    onClick={() => {
                      if (tab.id === 'overview' && activeTab === 'overview') {
                        setCaseSectionsExpanded(!caseSectionsExpanded);
                      } else {
                        setActiveTab(tab.id as any);
                        if (tab.id === 'overview') {
                          setCaseSectionsExpanded(true);
                        }
                      }
                    }}
                    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-primary-500 text-white shadow-lg transform scale-105'
                        : 'text-gray-600 hover:text-primary-600 hover:bg-primary-50 hover:shadow-md'
                    }`}
                  >
                    <tab.icon className={`h-5 w-5 mr-3 ${activeTab === tab.id ? 'text-white' : tab.color}`} />
                    {tab.label}
                    {tab.id === 'overview' && (
                      <ChevronDown className={`h-4 w-4 ml-auto transition-transform ${
                        caseSectionsExpanded ? 'rotate-180' : ''
                      }`} />
                    )}
                  </button>

                  {/* Collapsible Case Sections Submenu */}
                  {tab.id === 'overview' && activeTab === 'overview' && caseSectionsExpanded && (
                    <div className="ml-6 mt-2 space-y-1 border-l-2 border-primary-200 pl-4">
                      {[
                        { id: 'complaint', label: 'Chief Complaint', icon: AlertCircle, color: 'text-primary-600' },
                        { id: 'history', label: 'Medical History', icon: FileText, color: 'text-primary-600' },
                        { id: 'medications', label: 'Current Medications', icon: Activity, color: 'text-primary-600' },
                        { id: 'description', label: 'Case Description', icon: Stethoscope, color: 'text-primary-600' }
                      ].map((section) => (
                        <button
                          key={section.id}
                          onClick={() => {
                            const element = document.getElementById(section.id);
                            element?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                          }}
                          className="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-gray-600 hover:text-primary-700 hover:bg-primary-50"
                        >
                          <section.icon className={`h-4 w-4 mr-3 ${section.color}`} />
                          {section.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </nav>
        </div>
      </div>

      {/* Center Content Area - Modern Design */}
      <div
        className="bg-transparent min-h-screen overflow-y-auto"
        style={{
          marginLeft: '20px',
          marginRight: isDoctor ? `${rightSidebarWidth + 0}px` : '20px'
        }}
      >
        {/* Modern Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-primary-200 px-8 py-8 flex-shrink-0 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <h1 className="text-4xl font-bold text-gray-900 truncate mb-4" data-testid="case-title">
                {caseData.title}
              </h1>
              <div className="flex flex-wrap items-center gap-3 mt-4">
                {/* Enhanced Patient Info Tags */}
                {caseData.patientGender && (
                  <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-primary-100 text-primary-800 shadow-sm">
                    <User className="h-4 w-4 mr-2" />
                    {caseData.patientGender}
                  </span>
                )}
                {caseData.patientDateOfBirth && (
                  <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-primary-100 text-primary-800 shadow-sm">
                    <Clock className="h-4 w-4 mr-2" />
                    {calculateAge(caseData.patientDateOfBirth)} years old
                  </span>
                )}
                {caseData.patientCountry && (
                  <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-primary-100 text-primary-800 shadow-sm">
                    <Globe className="h-4 w-4 mr-2" />
                    {caseData.patientCountry}
                  </span>
                )}
              </div>

              {/* Date Info - Enhanced */}
              <div className="mt-4 text-sm text-gray-600 bg-primary-50 px-4 py-2 rounded-lg inline-block">
                <span className="font-medium">Created:</span> {caseData.createdAt ? new Date(caseData.createdAt).toLocaleDateString() : 'Unknown'} •
                <span className="font-medium ml-2">Updated:</span> {caseData.updatedAt ? new Date(caseData.updatedAt).toLocaleDateString() : 'Unknown'}
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex items-center space-x-4">
              {/* Submit button for patient draft cases */}
              {user?.role === 'patient' && caseData.status === 'draft' && (
                <button
                  onClick={handleSubmitCase}
                  disabled={submitting}
                  className={`inline-flex items-center px-6 py-3 border border-transparent rounded-full shadow-lg text-sm font-semibold text-white transition-all duration-200 ${submitting ? 'bg-primary-400 cursor-not-allowed' : 'bg-primary-500 hover:bg-primary-600 transform hover:-translate-y-0.5 hover:shadow-xl'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                >
                  {submitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Submit Case
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Doctor Acceptance Banner */}
        {user?.role === 'doctor' && doctorAcceptanceStatus && doctorAcceptanceStatus.isAssigned && !doctorAcceptanceStatus.hasAccepted && (
          <div className="bg-orange-50 border-l-4 border-orange-400 p-6 mx-8 mt-4 rounded-r-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="h-6 w-6 text-orange-400 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-orange-800">Case Assignment Pending</h3>
                  <p className="text-orange-700 mt-1">
                    You have been assigned to this case but haven't accepted it yet.
                    Please accept the assignment to start making changes to case notes.
                  </p>
                </div>
              </div>
              <button
                onClick={async () => {
                  try {
                    await apiClient.acceptCase(caseData!.id)
                    // Refresh acceptance status
                    const newStatus = await apiClient.getDoctorAcceptanceStatus(caseData!.id)
                    setDoctorAcceptanceStatus(newStatus)
                    toast.success('Case accepted successfully!')
                  } catch (error) {
                    console.error('Error accepting case:', error)
                    toast.error('Failed to accept case. Please try again.')
                  }
                }}
                className="inline-flex items-center px-6 py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-semibold"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Accept Case
              </button>
            </div>
          </div>
        )}


        {/* Main Content Area - Enhanced */}
        <div
          className={`bg-transparent overflow-y-auto ${activeTab === 'opinions' ? 'h-screen' : 'min-h-screen'}`}
          style={{
            marginLeft: '20px',
            marginRight: '20px'
          }}
        >
          <div className="w-full">
            {activeTab === 'overview' && (
              <div className="w-full">
                {/* Case Overview Header - Compact Design */}
                <div className="p-8 pb-4">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-3xl font-bold text-gray-900 flex items-center">
                      <BookOpen className="h-8 w-8 mr-3 text-primary-500" />
                      Case Overview
                    </h2>

                    {/* Compact Assigned Doctors - Inline Display */}
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5 text-primary-500" />
                        <span className="text-sm font-medium text-gray-700">Assigned Doctors:</span>
                      </div>

                      {caseData.assignedDoctors && caseData.assignedDoctors.length > 0 ? (
                        <div className="flex items-center space-x-2">
                          {caseData.assignedDoctors.slice(0, 3).map((doctor, index) => (
                            <div key={doctor.id} className="flex items-center bg-primary-100 rounded-full px-3 py-1.5 text-sm">
                              <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center mr-2">
                                <User className="h-3 w-3 text-white" />
                              </div>
                              <span className="font-medium text-primary-800">{doctor.name}</span>
                            </div>
                          ))}
                          {(user?.role === 'admin' || user?.role === 'agent' || user?.role === 'doctor') && (
                            <button
                              className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-primary-600 bg-primary-50 rounded-full hover:bg-primary-100 transition-all duration-200"
                              onClick={() => setIsDoctorModalOpen(true)}
                            >
                              Manage
                            </button>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500 italic">None assigned</span>
                          {(user?.role === 'admin' || user?.role === 'agent' || user?.role === 'doctor') && (
                            <button
                              className="inline-flex items-center px-3 py-1.5 text-xs font-semibold text-white bg-primary-500 rounded-full hover:bg-primary-600 transition-all duration-200"
                              onClick={() => setIsDoctorModalOpen(true)}
                            >
                              <Users className="h-3 w-3 mr-1" />
                              Assign
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'overview' && (
              <div className="w-full p-8 space-y-8">
                {/* Chief Complaint Section - Enhanced */}
                <section id="complaint" className="scroll-mt-2 w-full">
                  <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl border border-primary-200 p-3 w-full shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-sm border border-primary-100 p-8">
                      <div className="flex items-center mb-4">
                        <AlertCircle className="h-6 w-6 mr-3 text-primary-500" />
                        <h3 className="text-xl font-bold text-gray-900">Chief Complaint & Symptoms</h3>
                      </div>
                      <CollabClinicalEditor
                        caseId={caseData.id}
                        noteType="symptoms"
                        placeholder="Enter symptoms and chief complaint..."
                        disabled={isNotesReadOnly()}
                        className="w-full"
                      />
                    </div>
                  </div>
                </section>

                {/* Medical History Section - Enhanced */}
                <section id="history" className="scroll-mt-6 w-full">
                  <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl border border-primary-200 p-3 w-full shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-sm border border-primary-100 p-8">
                      <div className="flex items-center mb-4">
                        <FileText className="h-6 w-6 mr-3 text-primary-500" />
                        <h3 className="text-xl font-bold text-gray-900">Medical History</h3>
                      </div>
                      <CollabClinicalEditor
                        caseId={caseData.id}
                        noteType="medical_history"
                        placeholder="Enter medical history..."
                        disabled={isNotesReadOnly()}
                        className="w-full"
                      />
                    </div>
                  </div>
                </section>

                {/* Current Medications Section - Enhanced */}
                <section id="medications" className="scroll-mt-6 w-full">
                  <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl border border-primary-200 p-3 w-full shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-sm border border-primary-100 p-8">
                      <div className="flex items-center mb-4">
                        <Activity className="h-6 w-6 mr-3 text-primary-500" />
                        <h3 className="text-xl font-bold text-gray-900">Current Medications</h3>
                      </div>
                      <CollabClinicalEditor
                        caseId={caseData.id}
                        noteType="current_medications"
                        placeholder="Enter current medications with dosages..."
                        disabled={isNotesReadOnly()}
                        className="w-full"
                      />
                    </div>
                  </div>
                </section>

                {/* Case Description Section - Enhanced */}
                <section id="description" className="scroll-mt-6 w-full">
                  <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl border border-primary-200 p-3 w-full shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-sm border border-primary-100 p-8">
                      <div className="flex items-center mb-4">
                        <Stethoscope className="h-6 w-6 mr-3 text-primary-500" />
                        <h3 className="text-xl font-bold text-gray-900">Case Description</h3>
                      </div>
                      <CollabClinicalEditor
                        caseId={caseData.id}
                        noteType="case_description"
                        placeholder="Enter detailed case description..."
                        disabled={isNotesReadOnly()}
                        className="w-full"
                      />
                    </div>
                  </div>
                </section>
              </div>
            )}

            {activeTab === 'documents' && (
              <div className="h-full">
                <Documents
                  caseId={caseData.id}
                  compactMode={true}
                  hideHeader={true}
                  title="Case Documents"
                  subtitle="Medical records and supporting documents for this case"
                  doctorAcceptanceStatus={doctorAcceptanceStatus?.hasAccepted ? 'accepted' : 'pending'}
                  caseStatus={caseData.status}
                />
              </div>
            )}

            {activeTab === 'timeline' && (
              <div className="space-y-8 p-8">
                <div className="prose max-w-none">
                  <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                    <Clock className="h-8 w-8 mr-3 text-primary-500" />
                    Timeline
                  </h2>
                  <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-primary-200 p-8 hover:shadow-2xl transition-all duration-300">
                    <div className="text-center py-16">
                      <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <Clock className="h-10 w-10 text-primary-500" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">Timeline Coming Soon</h3>
                      <p className="text-lg text-gray-600 max-w-md mx-auto">Case timeline and activity history will be available here to track the progress of your medical consultation.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'messages' && (
              <div className="space-y-8 p-8">
                <div className="prose max-w-none">
                  <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center">
                    <MessageSquare className="h-8 w-8 mr-3 text-primary-500" />
                    Case Discussions
                  </h2>
                  <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-primary-200 overflow-hidden hover:shadow-2xl transition-all duration-300">
                    {id && <CaseDiscussions
                      caseId={id}
                      doctorAcceptanceStatus={doctorAcceptanceStatus?.hasAccepted ? 'accepted' : 'pending'}
                    />}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'opinions' && (
              <MedicalOpinion
                caseData={caseData}
                isNotesReadOnly={isNotesReadOnly}
                allNotes={allNotes}
                notesLoading={notesLoading}
                doctorAcceptanceStatus={doctorAcceptanceStatus}
              />
            )}
          </div>
        </div>
      </div>

      {/* Right Sidebar - Dynamic Note Sections (Only visible to doctors) */}
      {isDoctor && caseData && (
        <>
          {/* Draggable Resize Handle */}
          <div
            className="fixed top-0 h-screen w-1 bg-gray-300 hover:bg-blue-500 cursor-col-resize z-20 transition-colors"
            style={{ right: `${rightSidebarWidth}px` }}
            onMouseDown={handleMouseDown}
            title="Drag to resize"
          />

          <div
            className="bg-white border-l border-gray-200 fixed right-0 top-0 h-screen z-10 flex flex-col"
            style={{ width: `${rightSidebarWidth}px` }}
          >

            {/* Dynamic Note Sections for Sidebar */}
            <div className="flex-1 p-2 pt-0 overflow-y-auto">
              <DynamicNoteSections
                caseId={caseData.id}
                showInSidebar={true}
                className="space-y-4"
                disabled={isNotesReadOnly()}
                preloadedNotes={allNotes}
                notesLoading={notesLoading}
              />
            </div>
          </div>
        </>
      )}

      {/* Doctor Assignment Modal */}
      {isDoctorModalOpen && caseData && (
        <DoctorAssignmentModal
          caseId={caseData.id}
          isOpen={isDoctorModalOpen}
          onClose={() => setIsDoctorModalOpen(false)}
          onAssignmentChange={handleDoctorAssignmentChange}
          currentAssignments={formattedAssignments}
        />
      )}
    </div>
  )
}

export default CaseDetail
