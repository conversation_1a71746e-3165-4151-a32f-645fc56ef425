import { useState, useCallback, useEffect } from 'react';
import UnifiedEditor from './UnifiedEditor';
import { useAuth } from '../../hooks/useAuth';
import { apiClient } from '../../services/api';
import { CaseData } from '@/types/case';

interface StructuredClinicalEditorProps {
  caseId: string;
  sectionType: 'symptoms' | 'medicalHistory' | 'currentMedications' | 'caseDescription';
  title: string;
  placeholder?: string;
  initialContent?: string | { content: string; lastUpdated?: string; updatedBy?: string; [key: string]: any };
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
  caseData?: CaseData; // Add case data for permission checks
  preloadedData?: boolean; // Flag to indicate data is preloaded, skip API call
}

export default function StructuredClinicalEditor({
  caseId,
  sectionType,
  title,
  placeholder,
  initialContent = '',
  disabled = false,
  className = '',
  'data-testid': dataTestId,
  caseData,
  preloadedData = false,
}: StructuredClinicalEditorProps) {
  const { user } = useAuth();
  // Handle both string and object formats for initialContent
  const [initialLoadedContent, setInitialLoadedContent] = useState<string>(
    typeof initialContent === 'string' 
      ? initialContent 
      : initialContent && typeof initialContent === 'object' && 'content' in initialContent 
        ? initialContent.content 
        : ''
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Permission logic: Patients can only edit when case is in draft, then only doctors/admins can edit
  const canEditStructuredContent = useCallback(() => {
    if (!user || !caseData) return false;
    
    // Admins can edit any case
    if (user.role === 'admin') return true;
    
    // Doctors can edit cases assigned to them
    if (user.role === 'doctor' && caseData.assignedDoctors?.some(doctor => doctor.id === user.id)) return true;
    
    // Patients can only edit their own cases when in draft status
    if (user.role === 'patient' && caseData.patientId === user.id && caseData.status === 'draft') return true;
    
    return false;
  }, [user, caseData]);

  // Determine if editing should be disabled
  const isEditingDisabled = disabled || !canEditStructuredContent();

  // Permission logic is now handled through the read-only badge only

  // Load initial content from structured storage
  useEffect(() => {
    let isMounted = true; // Prevent duplicate calls from React StrictMode
    
    const loadContent = async () => {
      if (!caseId || !isMounted) return;

      // If data is preloaded, use initialContent and skip API call
      if (preloadedData) {
        // Handle initialContent which could be a string or an object with content property
        if (typeof initialContent === 'string') {
          setInitialLoadedContent(initialContent);
        } else if (initialContent && typeof initialContent === 'object' && 'content' in initialContent) {
          setInitialLoadedContent(initialContent.content);
        } else {
          // Default to empty string if we can't extract content
          setInitialLoadedContent('');
        }
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await apiClient.getCaseStructuredContent(caseId, sectionType);
        
        if (response && isMounted) {
          // Handle response content which could be a string or an object with content property
          if (typeof response === 'string') {
            setInitialLoadedContent(response);
          } else if (response && typeof response === 'object') {
            if ('content' in response && typeof response.content === 'string') {
              setInitialLoadedContent(response.content);
            } else {
              // If we can't extract content, set empty string to avoid [object Object]
              setInitialLoadedContent('');
              console.warn('Unexpected content format in response:', response);
            }
          }
        }
      } catch (err) {
        if (isMounted) {
          // TODO: Replace with proper error reporting
  console.error(`Error loading ${sectionType} content:`, err);
          setError(`Failed to load ${sectionType}`);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadContent();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [caseId, sectionType]); // Removed 'title' dependency

  // Simplified auto-save function - just send content directly
  const handleAutoSave = useCallback(async (markdownContent: string) => {
    if (!caseId || !user?.id) return;

    try {
      // Simple format: send content directly, not as nested object
      await apiClient.updateCaseStructuredContent(caseId, sectionType, { content: markdownContent });
      setLastSaved(new Date());
      setError(null);
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error(`Error saving ${sectionType}:`, err);
      setError(`Failed to save ${title.toLowerCase()}`);
    }
  }, [caseId, sectionType, title, user]);

  // Handle content changes - updated to handle markdown
  const handleChange = useCallback((_markdownContent: string) => {
    // Content changes are handled internally by UnifiedEditor
    // We don't need to update state here to avoid re-renders and focus loss
  }, []);

  if (isLoading) {
    return (
      <div className={`${className}`} data-testid={dataTestId}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <div className="animate-spin h-4 w-4 border border-blue-500 border-t-transparent rounded-full"></div>
            <span>Loading...</span>
          </div>
        </div>
        <div className="border border-gray-300 rounded-md p-3 bg-gray-50 min-h-[120px] flex items-center justify-center">
          <span className="text-gray-500">Loading content...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`} data-testid={dataTestId}>
      <UnifiedEditor
        initialContent={initialLoadedContent}
        placeholder={isEditingDisabled ? "Content is read-only" : (placeholder || `Enter ${title.toLowerCase()}...`)}
        onChange={handleChange}
        onSave={handleAutoSave}
        autoSave={!isEditingDisabled}
        autoSaveDelay={2000}
        disabled={isEditingDisabled}
        height={300}
        title={title}
        showSaveStatus={true}
        className="w-full"
        data-testid={`${dataTestId}-editor`}
      />

      {/* Metadata footer - only shown when not in read-only mode */}
      {!isEditingDisabled && (
        <div className="mt-2 text-xs text-gray-500 flex justify-end">
          <div>
            Auto-save enabled
          </div>
        </div>
      )}
    </div>
  );
}
