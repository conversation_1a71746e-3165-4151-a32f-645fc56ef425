import { useState, useEffect, useCallback, useRef } from 'react'
import { apiClient } from '@/services/api'
import { useAuth } from '@/hooks/useAuth'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { 
  Select as UISelect
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { CalendarIcon, SearchIcon, Loader2 } from 'lucide-react'
import { format } from 'date-fns'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { statusColors } from '@/constants/theme'

interface AuditLog {
  id: string
  userId: string
  username: string
  action: string
  resource: string
  details: string
  timestamp: string
}

interface AuditLogsProps {
  title?: string
  description?: string
  showUserIdFilter?: boolean
  patientMode?: boolean
}

const ITEMS_PER_PAGE = 20

export function AuditLogs({ 
  title = "Audit Logs", 
  description = "View and filter system audit logs for compliance and security monitoring.",
  showUserIdFilter = true,
  patientMode = false
}: AuditLogsProps) {
  const { user } = useAuth()
  const [logs, setLogs] = useState<AuditLog[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [loadingMore, setLoadingMore] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState<boolean>(true)
  const [currentPage, setCurrentPage] = useState<number>(1)
  
  // Filter states
  const [userId, setUserId] = useState<string>('')
  const [action, setAction] = useState<string>('')
  const [resource, setResource] = useState<string>('')
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  
  // Infinite scroll observer
  const observerRef = useRef<IntersectionObserver | null>(null)
  const lastLogElementRef = useCallback((node: HTMLTableRowElement) => {
    if (loading || loadingMore) return
    if (observerRef.current) observerRef.current.disconnect()
    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMoreLogs()
      }
    })
    if (node) observerRef.current.observe(node)
  }, [loading, loadingMore, hasMore])

  const fetchAuditLogs = async (page: number = 1, reset: boolean = true) => {
    if (page === 1) {
      setLoading(true)
    } else {
      setLoadingMore(true)
    }
    setError(null)
    
    try {
      const params: any = {
        page,
        limit: ITEMS_PER_PAGE
      }
      
      // Only include userId filter for admin users
      if (showUserIdFilter && userId && user?.role === 'admin') {
        params.userId = userId
      }
      
      if (action) params.action = action
      if (resource) params.resource = resource
      if (startDate) params.startDate = format(startDate, 'yyyy-MM-dd')
      if (endDate) params.endDate = format(endDate, 'yyyy-MM-dd')
      
      console.log('Filter values:', { userId, action, resource, startDate, endDate })
      const response = await apiClient.getAuditLogs(params)
      
      // Handle the backend response format: { success: true, data: [...], pagination: {...} }
      if (response && (response as any).success && Array.isArray((response as any).data)) {
        const newLogs = (response as any).data
        
        if (reset) {
          setLogs(newLogs)
        } else {
          setLogs(prev => [...prev, ...newLogs])
        }
        
        // Check if there are more pages
        if ((response as any).pagination) {
          const { page: currentPageNum, totalPages } = (response as any).pagination
          setHasMore(currentPageNum < totalPages)
        } else {
          setHasMore(newLogs.length === ITEMS_PER_PAGE)
        }
        
      } else if (Array.isArray(response)) {
        // Fallback for direct array response
        const newLogs = response
        if (reset) {
          setLogs(newLogs)
        } else {
          setLogs(prev => [...prev, ...newLogs])
        }
        setHasMore(newLogs.length === ITEMS_PER_PAGE)
      } else {
        // TODO: Replace with proper error reporting
  console.error('Unexpected API response format:', response)
        if (reset) {
          setLogs([])
        }
        setHasMore(false)
      }
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Failed to fetch audit logs:', err)
      setError(patientMode ? 'Failed to load your activity log. Please try again later.' : 'Failed to load audit logs. Please try again later.')
      if (reset) {
        setLogs([])
      }
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  const loadMoreLogs = () => {
    if (!loadingMore && hasMore) {
      const nextPage = currentPage + 1
      setCurrentPage(nextPage)
      fetchAuditLogs(nextPage, false)
    }
  }

  // Initial load only - filters are applied manually via Search button
  useEffect(() => {
    fetchAuditLogs(1, true)
  }, [])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchAuditLogs(1, true)
  }

  const handleClearFilters = () => {
    setUserId('')
    setAction('')
    setResource('')
    setStartDate(undefined)
    setEndDate(undefined)
    setCurrentPage(1)
    // Trigger data reload after clearing filters
    fetchAuditLogs(1, true)
  }

  const getActionBadgeColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create':
      case 'register':
      case 'login':
        return statusColors.create
      case 'read':
      case 'view':
        return statusColors.read
      case 'update':
      case 'edit':
        return statusColors.update
      case 'delete':
      case 'remove':
        return statusColors.delete
      case 'admin':
      case 'assign':
        return statusColors.admin
      default:
        return statusColors.auth
    }
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'MMM dd, yyyy HH:mm:ss')
    } catch (e) {
      return timestamp
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{title}</h2>
          <p className="text-muted-foreground">{description}</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          {/* Single row filter layout for better space efficiency */}
          <div className="flex flex-wrap items-end gap-3">
            {showUserIdFilter && (
              <div className="flex-1 min-w-[140px]">
                <label htmlFor="userId" className="text-xs font-medium text-gray-600 mb-1 block">User ID</label>
                <Input
                  id="userId"
                  placeholder="User ID"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  className="h-9"
                />
              </div>
            )}
            
            <div className="flex-1 min-w-[120px]">
              <label htmlFor="action" className="text-xs font-medium text-gray-600 mb-1 block">Action</label>
              <UISelect value={action} onChange={(e) => setAction(e.target.value)}>
                <option value="">All Actions</option>
                <option value="create">Create</option>
                <option value="read">Read</option>
                <option value="update">Update</option>
                <option value="delete">Delete</option>
                <option value="login">Login</option>
                <option value="logout">Logout</option>
              </UISelect>
            </div>
            
            <div className="flex-1 min-w-[120px]">
              <label htmlFor="resource" className="text-xs font-medium text-gray-600 mb-1 block">Resource</label>
              <UISelect value={resource} onChange={(e) => {
                console.log('Resource dropdown changed to:', e.target.value)
                setResource(e.target.value)
              }}>
                <option value="">All Resources</option>
                <option value="user">Profile</option>
                <option value="cases">Cases</option>
                <option value="appointments">Appointments</option>
                <option value="documents">Documents</option>
                <option value="consent">Consent Forms</option>
              </UISelect>
            </div>
            
            <div className="flex-1 min-w-[130px]">
              <label htmlFor="startDate" className="text-xs font-medium text-gray-600 mb-1 block">Start Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal h-9 text-sm"
                  >
                    <CalendarIcon className="mr-2 h-3 w-3" />
                    {startDate ? format(startDate, 'MMM dd') : <span className="text-gray-500">Start</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    selected={startDate}
                    onSelect={setStartDate}
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="flex-1 min-w-[130px]">
              <label htmlFor="endDate" className="text-xs font-medium text-gray-600 mb-1 block">End Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal h-9 text-sm"
                  >
                    <CalendarIcon className="mr-2 h-3 w-3" />
                    {endDate ? format(endDate, 'MMM dd') : <span className="text-gray-500">End</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    selected={endDate}
                    onSelect={setEndDate}
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            {/* Action buttons */}
            <div className="flex gap-2">
              <Button onClick={handleSearch} size="sm" className="h-9">
                <SearchIcon className="h-3 w-3 mr-1" />
                Search
              </Button>
              <Button variant="outline" onClick={handleClearFilters} size="sm" className="h-9">
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Log Records */}
      <Card>
        <CardHeader>
          <CardTitle>{patientMode ? 'Your Activity Records' : 'Audit Log Records'}</CardTitle>
          <CardDescription>
            {patientMode 
              ? `Showing ${logs.length} activity records`
              : `Showing ${logs.length} audit log records`
            }
            {hasMore && ' (scroll down to load more)'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </div>
          ) : error ? (
            <div className="text-center py-10 text-red-500">
              <p>{error}</p>
              <Button variant="outline" onClick={() => fetchAuditLogs(1, true)} className="mt-4">
                Retry
              </Button>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-10 text-muted-foreground">
              <p>{patientMode ? 'No activity records found matching your criteria.' : 'No audit logs found matching your criteria.'}</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{patientMode ? 'Date & Time' : 'Timestamp'}</TableHead>
                    {!patientMode && <TableHead>User</TableHead>}
                    <TableHead>Action</TableHead>
                    <TableHead>Resource</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log, index) => (
                    <TableRow 
                      key={log.id}
                      ref={index === logs.length - 1 ? lastLogElementRef : null}
                    >
                      <TableCell className="font-mono text-xs">
                        {formatTimestamp(log.timestamp)}
                      </TableCell>
                      {!patientMode && (
                        <TableCell>
                          {log.username || log.userId}
                        </TableCell>
                      )}
                      <TableCell>
                        <Badge className={cn(getActionBadgeColor(log.action))}>
                          {log.action}
                        </Badge>
                      </TableCell>
                      <TableCell className="capitalize">{log.resource}</TableCell>
                      <TableCell className="max-w-xs truncate">
                        <span title={log.details}>{log.details}</span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {/* Loading more indicator */}
              {loadingMore && (
                <div className="flex justify-center items-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  <span className="text-sm text-muted-foreground">Loading more records...</span>
                </div>
              )}
              
              {/* End of records indicator */}
              {!hasMore && logs.length > 0 && (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  No more records to load
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default AuditLogs
