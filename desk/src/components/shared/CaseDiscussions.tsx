import React, { useState, useEffect, useRef } from 'react';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth';
import { apiClient } from '@/services/api';
import {
  Send,
  Paperclip,
  Download,
  File,
  Image,
  FileText,
  Trash2,
  Edit,
  Eye,
  EyeOff,
  User,
  Check,
  CheckCheck,
  UserCheck,
  Stethoscope,
  Shield,
  Bot,
  Filter
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';

// Types for case discussions
interface Attachment {
  id: string;
  filename: string;
  fileSize: number;
  fileType: string;
  url: string;
}

interface Author {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
}

interface CaseDiscussion {
  id: string;
  caseId: string;
  authorId: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  isVisibleToPatient: boolean;
  attachments: Attachment[];
  author: Author;
}

interface CaseDiscussionsProps {
  caseId: string;
  doctorAcceptanceStatus?: 'pending' | 'accepted' | 'declined' | 'closed' | null;
}

const CaseDiscussions: React.FC<CaseDiscussionsProps> = ({ caseId, doctorAcceptanceStatus }) => {
  const { user } = useAuth();
  const [discussions, setDiscussions] = useState<CaseDiscussion[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string>('');
  const [sending, setSending] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [editingDiscussionId, setEditingDiscussionId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState<string>('');
  
  // Message filtering state
  const [showPrivateMessages, setShowPrivateMessages] = useState<boolean>(true);
  const [showPublicMessages, setShowPublicMessages] = useState<boolean>(true);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if current user is patient
  const isPatient = user?.role === 'patient';
  
  // Check if doctor has accepted the case (for messaging restrictions)
  const isDoctorAccepted = user?.role === 'doctor' ? doctorAcceptanceStatus === 'accepted' : true;
  
  // For unaccepted doctors, force private messages only
  const forcePrivateMessages = user?.role === 'doctor' && !isDoctorAccepted;
  
  // Set default visibility based on user role - doctors/admins default to private, patients to public
  // For unaccepted doctors, always default to private
  const [isVisibleToPatient, setIsVisibleToPatient] = useState<boolean>(
    isPatient ? true : (forcePrivateMessages ? false : false)
  );
  
  // Check if current user can control patient visibility
  const canControlPatientVisibility = user?.role === 'doctor' || user?.role === 'admin' || user?.role === 'agent';

  // Get WhatsApp-style message colors with purple highlight for private messages
  const getMessageColor = (authorRole: string, isCurrentUser: boolean, isVisibleToPatient: boolean) => {
    if (isCurrentUser) {
      if (!isVisibleToPatient) {
        return 'bg-gradient-to-r from-[#dcf8c6] to-[#e8d5ff] text-gray-800 shadow-sm border-l-4 border-purple-300'; // Green with subtle purple highlight for private sent messages
      }
      return 'bg-[#dcf8c6] text-gray-800 shadow-sm'; // WhatsApp green for sent messages
    }
    if (!isVisibleToPatient) {
      return 'bg-gradient-to-r from-white to-[#f3f0ff] text-gray-800 shadow-sm border-l-4 border-purple-300'; // White with subtle purple highlight for private received messages
    }
    return 'bg-white text-gray-800 shadow-sm'; // White for received messages
  };

  // Get role icon based on user role
  const getRoleIcon = (role: string | undefined) => {
    if (!role) return <User className="h-3 w-3" />;
    
    switch (role.toLowerCase()) {
      case 'patient':
        return <UserCheck className="h-3 w-3" />;
      case 'doctor':
        return <Stethoscope className="h-3 w-3" />;
      case 'admin':
        return <Shield className="h-3 w-3" />;
      case 'agent':
        return <User className="h-3 w-3" />;
      case 'ai_agent':
        return <Bot className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  // Get role color based on user role
  const getRoleColor = (role: string | undefined) => {
    if (!role) return 'text-gray-600';
    
    switch (role.toLowerCase()) {
      case 'patient':
        return 'text-blue-600';
      case 'doctor':
        return 'text-green-600';
      case 'admin':
        return 'text-red-600';
      case 'agent':
        return 'text-purple-600';
      case 'ai_agent':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  // Get author initials for profile image
  const getAuthorInitials = (author: Author) => {
    return `${author.firstName.charAt(0)}${author.lastName.charAt(0)}`.toUpperCase();
  };

  // Generate consistent profile image color based on user ID
  const getProfileImageColor = (authorId: string, role: string) => {
    // Create a hash from the authorId for consistent colors
    let hash = 0;
    for (let i = 0; i < authorId.length; i++) {
      hash = authorId.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    // WhatsApp-style color palette
    const colors = [
      'bg-[#00a884]', // WhatsApp green
      'bg-[#0084ff]', // Blue
      'bg-[#7b68ee]', // Purple
      'bg-[#ff6b6b]', // Red
      'bg-[#4ecdc4]', // Teal
      'bg-[#45b7d1]', // Light blue
      'bg-[#f39c12]', // Orange
      'bg-[#e74c3c]', // Dark red
      'bg-[#9b59b6]', // Dark purple
      'bg-[#1abc9c]', // Turquoise
    ];
    
    return colors[Math.abs(hash) % colors.length];
  };

  // Format time in WhatsApp style
  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return format(date, 'HH:mm'); // Show time for today
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return format(date, 'dd/MM/yyyy'); // Show date for older messages
    }
  };

  // Filter discussions based on visibility settings
  const getFilteredDiscussions = (discussions: CaseDiscussion[]) => {
    return discussions.filter(discussion => {
      if (discussion.isDeleted) return false;
      
      // If both filters are off, show nothing
      if (!showPrivateMessages && !showPublicMessages) return false;
      
      // If both filters are on, show everything
      if (showPrivateMessages && showPublicMessages) return true;
      
      // Show only private messages
      if (showPrivateMessages && !showPublicMessages) {
        return !discussion.isVisibleToPatient;
      }
      
      // Show only public messages
      if (!showPrivateMessages && showPublicMessages) {
        return discussion.isVisibleToPatient;
      }
      
      return true;
    });
  };

  // Group messages by date
  const groupMessagesByDate = (discussions: CaseDiscussion[]) => {
    const groups: { [key: string]: CaseDiscussion[] } = {};
    
    getFilteredDiscussions(discussions)
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      .forEach(discussion => {
        const date = format(new Date(discussion.createdAt), 'yyyy-MM-dd');
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(discussion);
      });
    
    return groups;
  };

  // Format date header
  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  // Fetch case discussions
  const fetchDiscussions = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getCaseDiscussions(caseId);
      setDiscussions(response || []);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load case discussions');
      // TODO: Replace with proper error reporting
  console.error('Error fetching case discussions:', err);
    } finally {
      setLoading(false);
    }
  };

  // Send a new message
  const sendMessage = async () => {
    if (!message.trim() && !selectedFile) return;

    try {
      setSending(true);

      // Debug: Log authentication state
      console.log('🔍 Sending message - Auth debug:', {
        hasUser: !!user,
        userId: user?.id,
        userRole: user?.role,
        hasToken: !!localStorage.getItem('auth_token'),
        tokenPreview: localStorage.getItem('auth_token')?.substring(0, 20) + '...',
        apiBaseURL: (apiClient as any).baseURL,
        caseId,
        messageLength: message.length
      });

      // For patient messages, always set isVisibleToPatient to true
      // For unaccepted doctors, force private messages
      const finalIsVisibleToPatient = isPatient ? true : (forcePrivateMessages ? false : isVisibleToPatient);

      if (selectedFile) {
        // Send message with attachment
        await apiClient.uploadCaseDiscussionAttachment(caseId, message, selectedFile, finalIsVisibleToPatient);
      } else {
        // Send text-only message
        await apiClient.sendCaseDiscussion(caseId, message, finalIsVisibleToPatient);
      }
      
      // Refresh discussions to get the latest data including the new message
      await fetchDiscussions();
      
      setMessage('');
      setSelectedFile(null);
      // Reset visibility based on user role for next message
      setIsVisibleToPatient(isPatient ? true : (forcePrivateMessages ? false : false));
      scrollToBottom();
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to send message';
      setError(errorMessage);
      toast.error(errorMessage);
      // Log detailed error information for debugging
      console.error('Error sending message:', {
        error: err,
        message: err.message,
        status: err.status,
        details: err.details,
        caseId,
        messageContent: message,
        userRole: user?.role,
        userId: user?.id,
        apiUrl: (window as any).VITE_API_URL || 'not set'
      });
    } finally {
      setSending(false);
    }
  };

  // Update a message
  const updateMessage = async () => {
    if (!editingDiscussionId || !editContent.trim()) return;
    
    try {
      setSending(true);
      
      await apiClient.updateCaseDiscussion(caseId, editingDiscussionId, editContent);
      
      // Update the discussion in the state
      setDiscussions(discussions.map(discussion => 
        discussion.id === editingDiscussionId 
          ? { ...discussion, content: editContent, updatedAt: new Date().toISOString() }
          : discussion
      ));
      
      // Refresh discussions to get the latest data
      await fetchDiscussions();
      
      setEditingDiscussionId(null);
      setEditContent('');
    } catch (err: any) {
      setError(err.message || 'Failed to update message');
      // TODO: Replace with proper error reporting
  console.error('Error updating message:', err);
    } finally {
      setSending(false);
    }
  };

  // Delete a message
  const deleteMessage = async (discussionId: string) => {
    if (forcePrivateMessages) {
      alert('You must accept the case assignment before deleting messages. You can only send private text messages until you accept the case.');
      return;
    }
    
    if (!window.confirm('Are you sure you want to delete this message?')) return;
    
    try {
      await apiClient.deleteCaseDiscussion(caseId, discussionId);
      
      // Update the state to mark the discussion as deleted
      setDiscussions(discussions.map(discussion => 
        discussion.id === discussionId 
          ? { ...discussion, isDeleted: true }
          : discussion
      ));
      
      // Refresh discussions to sync with server
      await fetchDiscussions();
    } catch (err: any) {
      setError(err.message || 'Failed to delete message');
      // TODO: Replace with proper error reporting
  console.error('Error deleting message:', err);
    }
  };

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFile(e.target.files?.[0] || null);
  };

  // Trigger file input click
  const triggerFileInput = () => {
    if (forcePrivateMessages) {
      alert('You must accept the case assignment before uploading files. You can only send private text messages until you accept the case.');
      return;
    }
    fileInputRef.current?.click();
  };

  // Handle edit button click
  const handleEditClick = (discussion: CaseDiscussion) => {
    if (forcePrivateMessages) {
      alert('You must accept the case assignment before editing messages. You can only send private text messages until you accept the case.');
      return;
    }
    
    setEditingDiscussionId(discussion.id);
    setEditContent(discussion.content);
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // Get file icon based on file type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="h-5 w-5" />;
    return <FileText className="h-5 w-5" />;
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle key press for message input
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // If Enter is pressed without Shift, send the message
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (editingDiscussionId) {
        updateMessage();
      } else {
        sendMessage();
      }
    }
  };

  // Fetch discussions on component mount
  useEffect(() => {
    fetchDiscussions();
  }, [caseId]);

  // Scroll to bottom when discussions change
  useEffect(() => {
    scrollToBottom();
  }, [discussions]);

  return (
    <div className="flex flex-col h-full">
      {/* Messages container with WhatsApp-style background */}
      <div className={`flex-1 overflow-y-auto p-4 bg-[#efeae2] bg-opacity-30 ${isPatient ? 'pb-20' : 'pb-32'}`} style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}>
        {loading && discussions.length === 0 ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00a884]"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {error}
          </div>
        ) : discussions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="bg-white rounded-lg p-6 shadow-sm max-w-sm mx-auto">
              <div className="text-gray-400 mb-2">💬</div>
              <div className="text-sm">No messages yet. Start the conversation!</div>
            </div>
          </div>
        ) : (
          Object.entries(groupMessagesByDate(discussions)).map(([date, dayDiscussions]) => (
            <div key={date} className="mb-6">
              {/* Date header */}
              <div className="flex justify-center mb-4">
                <div className="bg-white bg-opacity-80 text-gray-600 text-xs px-3 py-1 rounded-full shadow-sm">
                  {formatDateHeader(date)}
                </div>
              </div>
              
              {/* Messages for this date */}
              <div className="space-y-1">
                {dayDiscussions.map((discussion, index) => {
                  const isCurrentUser = discussion.authorId === user?.id;
                  const authorName = `${discussion.author.firstName} ${discussion.author.lastName}`;
                  const prevMessage = index > 0 ? dayDiscussions[index - 1] : null;
                  const showAvatar = !prevMessage || prevMessage.authorId !== discussion.authorId;
                  const isLastInGroup = index === dayDiscussions.length - 1 ||
                    (index < dayDiscussions.length - 1 && dayDiscussions[index + 1].authorId !== discussion.authorId);
                  
                  return (
                    <div
                      key={discussion.id}
                      className={`flex items-end space-x-2 ${isCurrentUser ? 'flex-row-reverse space-x-reverse' : ''} ${
                        !showAvatar ? (isCurrentUser ? 'mr-12' : 'ml-12') : ''
                      }`}
                    >
                      {/* Profile Image - only show for first message in group */}
                      {showAvatar && !isCurrentUser && (
                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium shadow-sm ${getProfileImageColor(discussion.authorId, discussion.author.role)}`}>
                          {getAuthorInitials(discussion.author)}
                        </div>
                      )}
                      
                      {/* Message bubble */}
                      <div className={`max-w-[75%] ${isCurrentUser ? 'items-end' : 'items-start'} flex flex-col`}>
                        {/* Author name with role icon - only show for first message in group from others */}
                        {showAvatar && !isCurrentUser && (
                          <div className="text-xs text-gray-600 mb-1 ml-3 flex items-center space-x-1">
                            <span className={getRoleColor(discussion.author?.role)}>
                              {getRoleIcon(discussion.author?.role)}
                            </span>
                            <span>{authorName}</span>
                            <span className={`text-xs font-medium ${getRoleColor(discussion.author?.role)}`}>
                              ({discussion.author?.role ? discussion.author.role.charAt(0).toUpperCase() + discussion.author.role.slice(1) : 'User'})
                            </span>
                          </div>
                        )}
                        
                        <div className={`relative ${
                          isCurrentUser
                            ? (discussion.isVisibleToPatient
                                ? 'bg-[#dcf8c6] rounded-tl-2xl rounded-tr-sm rounded-bl-2xl rounded-br-2xl'
                                : 'bg-gradient-to-r from-[#e8d5ff] to-[#f3f0ff] rounded-tl-2xl rounded-tr-sm rounded-bl-2xl rounded-br-2xl border-l-4 border-purple-300')
                            : (discussion.isVisibleToPatient
                                ? 'bg-white rounded-tl-sm rounded-tr-2xl rounded-bl-2xl rounded-br-2xl'
                                : 'bg-gradient-to-r from-[#f3f0ff] to-[#e8d5ff] rounded-tl-sm rounded-tr-2xl rounded-bl-2xl rounded-br-2xl border-l-4 border-purple-300')
                        } p-3 shadow-sm max-w-full`}>
                          {/* Message content */}
                          <div className="whitespace-pre-wrap break-words text-sm leading-relaxed">
                            {discussion.content}
                          </div>
                          
                          {/* Attachments */}
                          {discussion.attachments && discussion.attachments.length > 0 && (
                            <div className="mt-2 space-y-2">
                              {discussion.attachments.map(attachment => (
                                <a
                                  key={attachment.id}
                                  href={attachment.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center p-2 rounded-lg bg-black bg-opacity-5 hover:bg-opacity-10 transition-colors"
                                >
                                  {getFileIcon(attachment.fileType)}
                                  <div className="ml-2 flex-1 min-w-0">
                                    <div className="text-sm font-medium truncate text-gray-800">
                                      {attachment.filename}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      {formatFileSize(attachment.fileSize)}
                                    </div>
                                  </div>
                                  <Download className="h-4 w-4 ml-2 text-gray-500" />
                                </a>
                              ))}
                            </div>
                          )}
                          
                          {/* Message time and status */}
                          <div className={`flex items-center justify-end mt-1 space-x-1 ${
                            isCurrentUser ? 'text-gray-600' : 'text-gray-500'
                          }`}>
                            {/* Patient visibility indicator */}
                            {!isPatient && (
                              <div className="flex items-center mr-1">
                                {discussion.isVisibleToPatient ? (
                                  <Eye className="h-3 w-3 text-green-500" />
                                ) : (
                                  <EyeOff className="h-3 w-3 text-purple-500" />
                                )}
                              </div>
                            )}
                            {/* Private message indicator for visual clarity */}
                            {!discussion.isVisibleToPatient && (
                              <div className="flex items-center mr-1">
                                <span className="text-xs text-purple-600 font-medium">Private</span>
                              </div>
                            )}
                            <span className="text-xs">
                              {formatMessageTime(discussion.createdAt)}
                            </span>
                            {/* Read status for sent messages */}
                            {isCurrentUser && (
                              <CheckCheck className="h-3 w-3 text-blue-500" />
                            )}
                          </div>
                          
                          {/* Action buttons for current user's messages */}
                          {isCurrentUser && (
                            <div className="absolute -right-2 top-0 opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="flex space-x-1 bg-white rounded-full shadow-lg p-1">
                                <button
                                  onClick={() => handleEditClick(discussion)}
                                  className="text-gray-400 hover:text-gray-600 transition-colors p-1"
                                  aria-label="Edit message"
                                >
                                  <Edit className="h-3 w-3" />
                                </button>
                                <button
                                  onClick={() => deleteMessage(discussion.id)}
                                  className="text-gray-400 hover:text-red-600 transition-colors p-1"
                                  aria-label="Delete message"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Spacer for current user messages to align with avatar */}
                      {showAvatar && isCurrentUser && (
                        <div className="w-8 h-8"></div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Selected file preview */}
      {selectedFile && (
        <div className="bg-[#f0f0f0] border-t border-gray-200 p-3">
          <div className="bg-white rounded-2xl p-3 shadow-sm flex items-center justify-between">
            <div className="flex items-center">
              {selectedFile.type.startsWith('image/') ? (
                <div className="bg-green-100 p-2 rounded-full mr-3">
                  <Image className="h-5 w-5 text-green-600" />
                </div>
              ) : (
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <File className="h-5 w-5 text-blue-600" />
                </div>
              )}
              <div>
                <div className="text-sm font-medium text-gray-800 truncate max-w-[200px]">
                  {selectedFile.name}
                </div>
                <div className="text-xs text-gray-500">
                  {formatFileSize(selectedFile.size)}
                </div>
              </div>
            </div>
            <button
              onClick={() => setSelectedFile(null)}
              className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Remove file"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
      
      {/* Message filtering and patient visibility controls - only show for non-patients */}
      {!editingDiscussionId && !isPatient && (
        <div className="fixed bottom-16 left-0 right-0 bg-[#f0f0f0] border-t border-gray-200 p-3 z-10">
          <div className="bg-white rounded-2xl p-3 shadow-sm">

            <div className="flex items-center justify-between">
              {/* Patient visibility control for doctors/admins */}
              {canControlPatientVisibility && (
                <label className={`flex items-center space-x-3 text-sm ${forcePrivateMessages ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}>
                  <input
                    type="checkbox"
                    checked={isVisibleToPatient}
                    onChange={(e) => !forcePrivateMessages && setIsVisibleToPatient(e.target.checked)}
                    disabled={forcePrivateMessages}
                    className="rounded border-gray-300 text-[#00a884] focus:ring-[#00a884] focus:ring-offset-0 disabled:opacity-50"
                  />
                  <span className="text-gray-700 flex items-center">
                    {isVisibleToPatient ? (
                      <>
                        <Eye className="h-4 w-4 text-green-500 mr-2" />
                        Visible to patient
                      </>
                    ) : (
                      <>
                        <EyeOff className="h-4 w-4 text-red-500 mr-2" />
                        Hidden from patient {forcePrivateMessages && '(Required)'}
                      </>
                    )}
                  </span>
                </label>
              )}
              
              {/* Message filtering toggle buttons */}
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <button
                  onClick={() => setShowPublicMessages(!showPublicMessages)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    showPublicMessages
                      ? 'bg-green-100 text-green-700 border border-green-200'
                      : 'bg-gray-100 text-gray-500 border border-gray-200'
                  }`}
                >
                  <Eye className="h-3 w-3 inline mr-1" />
                  Public
                </button>
                <button
                  onClick={() => setShowPrivateMessages(!showPrivateMessages)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    showPrivateMessages
                      ? 'bg-purple-100 text-purple-700 border border-purple-200'
                      : 'bg-gray-100 text-gray-500 border border-gray-200'
                  }`}
                >
                  <EyeOff className="h-3 w-3 inline mr-1" />
                  Private
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* WhatsApp-style message input */}
      <div className={`fixed left-0 right-0 bg-[#f0f0f0] p-3 border-t border-gray-200 z-20 ${isPatient ? 'bottom-0' : 'bottom-0'}`}>
        {editingDiscussionId ? (
          <div className="flex flex-col space-y-2">
            <div className="text-sm text-[#00a884] font-medium flex items-center">
              <Edit className="h-4 w-4 mr-1" />
              Editing message
            </div>
            <div className="flex items-end space-x-2">
              <div className="flex-1 bg-white rounded-3xl px-4 py-2 shadow-sm">
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  onKeyDown={handleKeyPress}
                  className="w-full border-none outline-none resize-none bg-transparent text-gray-800 placeholder-gray-500"
                  placeholder="Edit your message..."
                  rows={1}
                  style={{ minHeight: '24px', maxHeight: '120px' }}
                />
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setEditingDiscussionId(null)}
                  className="bg-gray-400 hover:bg-gray-500 text-white rounded-full p-2 transition-colors shadow-sm"
                  disabled={sending}
                  aria-label="Cancel"
                >
                  <Trash2 className="h-5 w-5" />
                </button>
                <button
                  onClick={updateMessage}
                  className="bg-[#00a884] hover:bg-[#008f72] text-white rounded-full p-2 transition-colors shadow-sm"
                  disabled={sending || !editContent.trim()}
                  aria-label="Update message"
                >
                  <Send className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-end space-x-3">
            {/* Main input container */}
            <div className="flex-1 bg-white rounded-3xl px-4 py-2 shadow-sm flex items-end space-x-2">
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                className="flex-1 border-none outline-none resize-none bg-transparent text-gray-800 placeholder-gray-500"
                placeholder="Type a message..."
                rows={1}
                style={{ minHeight: '24px', maxHeight: '120px' }}
              />
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                className="hidden"
              />
              {!forcePrivateMessages && (
                <button
                  onClick={triggerFileInput}
                  className="text-gray-500 hover:text-gray-700 transition-colors p-1"
                  aria-label="Attach file"
                >
                  <Paperclip className="h-5 w-5" />
                </button>
              )}
            </div>
            
            {/* Send button */}
            <button
              onClick={sendMessage}
              className="bg-[#00a884] hover:bg-[#008f72] text-white rounded-full p-3 transition-colors shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={sending || (!message.trim() && !selectedFile) || (forcePrivateMessages && !!selectedFile)}
              aria-label="Send message"
              title={forcePrivateMessages && selectedFile ? 'Cannot send files until you accept the case assignment' : 'Send message'}
            >
              {sending ? (
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
              ) : (
                <Send className="h-5 w-5" />
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CaseDiscussions;
