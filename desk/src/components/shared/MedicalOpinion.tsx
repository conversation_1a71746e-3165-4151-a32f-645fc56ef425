import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { CollabClinicalEditor } from './CollabClinicalEditor';
import { apiClient } from '@/services/api';
import { CaseData } from '@/types/case';
import { useAuth } from '@/hooks/useAuth';

interface MedicalOpinionProps {
  caseData: CaseData;
  isNotesReadOnly: () => boolean;
  allNotes: { [noteType: string]: any };
  notesLoading: boolean;
  doctorAcceptanceStatus: {
    isDoctor: boolean;
    isAssigned: boolean;
    hasAccepted: boolean;
    message: string;
  } | null;
}

const MedicalOpinion: React.FC<MedicalOpinionProps> = ({
  caseData,
  isNotesReadOnly,
  allNotes,
  notesLoading
}) => {
  const { user } = useAuth();
  const [isSubmittingOpinion, setIsSubmittingOpinion] = useState(false);
  const [opinionSubmitted, setOpinionSubmitted] = useState(false);

  const handleSubmitMedicalOpinion = async () => {
    if (!caseData || !user) return;

    const opinionContent = (allNotes.medical_opinion?.content || '').trim();

    if (opinionContent.length < 10) {
      toast.error('Please enter a Medical Opinion of at least 10 characters.');
      return;
    }

    setIsSubmittingOpinion(true);
    try {
      // For now, we'll pass the full content as both diagnosis and recommendations
      // to satisfy the current backend API. This may need to be updated later.
      const medicalOpinionData = {
        caseId: caseData.id,
        diagnosis: opinionContent.substring(0, 255), // Example: use a substring for diagnosis
        recommendations: opinionContent,
        treatmentPlan: '',
        followUpInstructions: '',
        urgencyLevel: 'medium',
        confidenceLevel: 'high',
        notes: opinionContent
      };

      const createResp = await apiClient.createMedicalOpinion(caseData.id, medicalOpinionData);
      const createdOpinion = (createResp && (createResp as any).opinion) ? (createResp as any).opinion : createResp?.opinion;
      const createdOpinionId = createdOpinion?.id;

      if (createdOpinionId) {
        await apiClient.submitMedicalOpinion(createdOpinionId);
      }

      setOpinionSubmitted(true);
      toast.success('Medical Opinion submitted successfully!');
    } catch (error) {
      console.error('Error submitting medical opinion:', error);
      toast.error('Failed to submit medical opinion. Please try again.');
    } finally {
      setIsSubmittingOpinion(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {user?.role === 'doctor' ? (
        <div className="flex-1 flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
            <div className="flex items-center">
              <Stethoscope className="h-6 w-6 mr-3 text-red-500" />
              <h2 className="text-xl font-bold text-gray-900">Medical Opinion</h2>
            </div>
            <div className="flex items-center space-x-3">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Doctor Only
              </span>
              <div className="text-sm text-gray-500">
                Auto-save enabled
              </div>
              {allNotes.medical_opinion?.content && !opinionSubmitted && (
                <button
                  onClick={handleSubmitMedicalOpinion}
                  disabled={isSubmittingOpinion}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmittingOpinion ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Submit Opinion
                    </>
                  )}
                </button>
              )}
              {opinionSubmitted && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Submitted
                </span>
              )}
            </div>
          </div>
          <div className="flex-1 bg-white">
            <CollabClinicalEditor
              caseId={caseData.id}
              noteType="medical_opinion"
              placeholder="📋 COMPREHENSIVE MEDICAL OPINION..."
              disabled={isNotesReadOnly()}
              className="w-full h-full border-0 focus:ring-0 focus:border-0"
              height={800}
            />
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center p-8">
          {allNotes.medical_opinion?.content ? (
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-primary-200 p-12 hover:shadow-2xl transition-all duration-300 max-w-4xl">
              <div className="text-left">
                <div className="flex items-center mb-6">
                  <Stethoscope className="h-8 w-8 mr-4 text-primary-500" />
                  <h3 className="text-3xl font-bold text-gray-900">Medical Opinion</h3>
                </div>
                <div
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: allNotes.medical_opinion.content }}
                />
              </div>
            </div>
          ) : (
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-primary-200 p-12 hover:shadow-2xl transition-all duration-300 max-w-md">
              <div className="text-center">
                <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Stethoscope className="h-10 w-10 text-primary-500" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Medical Opinion</h3>
                <p className="text-lg text-gray-600 mb-6">The attending physician's comprehensive medical opinion will appear here once provided.</p>
                <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Awaiting Doctor's Assessment
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MedicalOpinion;