import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  FileText,
  Image,
  File,
  Eye,
  AlertCircle,
  Loader
} from 'lucide-react'
import {
  textColors,
  typography,
  sizes,
  animations,
  commonClasses
} from '@/constants/theme'
import { UnifiedDocumentViewer } from '@/components/ui/UnifiedDocumentViewer'

interface CaseDocument {
  id: string
  title: string
  description?: string
  originalFileName: string
  fileName: string
  fileSize: number
  mimeType: string
  documentType: 'medical_record' | 'lab_result' | 'prescription' | 'imaging' | 'insurance' | 'consent_form' | 'discharge_summary' | 'other'
  uploadedBy: string
  createdAt: string
  updatedAt: string
  patientId: string
  caseId?: string
  isConfidential?: boolean
  hipaaCompliant?: boolean
  documentSource?: 'patient' | 'medical'
}

interface InlineCaseDocumentViewerProps {
  patientId?: string
  caseId?: string
  onDocumentSelect?: (document: CaseDocument | null) => void
}

// Helper functions
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileIcon = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return Image
  if (mimeType === 'application/pdf') return FileText
  return File
}

const getDocumentTypeInfo = (type: string) => {
  const types = {
    medical_record: { label: '🏥 Medical Record', color: 'bg-blue-100 text-blue-800', priority: 'high' },
    lab_result: { label: '🧪 Lab Result', color: 'bg-green-100 text-green-800', priority: 'high' },
    prescription: { label: '💊 Prescription', color: 'bg-purple-100 text-purple-800', priority: 'high' },
    imaging: { label: '📸 Medical Imaging', color: 'bg-indigo-100 text-indigo-800', priority: 'high' },
    insurance: { label: '🛡️ Insurance Document', color: 'bg-cyan-100 text-cyan-800', priority: 'medium' },
    consent_form: { label: '📝 Consent Form', color: 'bg-orange-100 text-orange-800', priority: 'high' },
    discharge_summary: { label: '📋 Discharge Summary', color: 'bg-teal-100 text-teal-800', priority: 'high' },
    other: { label: '📄 Other Document', color: 'bg-gray-100 text-gray-800', priority: 'low' }
  }
  return types[type as keyof typeof types] || types.other
}

export function InlineCaseDocumentViewer({ 
  patientId,
  caseId,
  onDocumentSelect
}: InlineCaseDocumentViewerProps) {
  const { user } = useAuth()
  const [documents, setDocuments] = useState<CaseDocument[]>([])
  const [selectedDocument, setSelectedDocument] = useState<CaseDocument | null>(null)
  const [selectedIndex, setSelectedIndex] = useState<number>(-1)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load documents
  useEffect(() => {
    if (user && (patientId || caseId)) {
      loadDocuments()
    }
  }, [user, patientId, caseId])

  const loadDocuments = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiClient.getDocuments(caseId || undefined)
      let documents: any[] = []
      
      if (Array.isArray(response)) {
        documents = response
      } else if (response && typeof response === 'object') {
        documents = (response as any).documents || (response as any).data || []
      }
      
      // Filter for case documents if patientId is provided
      if (patientId) {
        documents = documents.filter(doc => doc.patientId === patientId)
      }
      
      const transformedDocuments: CaseDocument[] = documents.map((doc: any) => ({
        id: doc.id,
        title: doc.title || doc.originalFileName || 'Unknown Document',
        description: doc.description,
        originalFileName: doc.originalFileName || doc.fileName || 'Unknown Document',
        fileName: doc.fileName || doc.originalFileName || 'Unknown Document',
        fileSize: doc.fileSize || 0,
        mimeType: doc.mimeType || 'application/octet-stream',
        documentType: doc.documentType || 'other',
        patientId: doc.patientId || patientId || '',
        caseId: doc.caseId || caseId || undefined,
        uploadedBy: doc.uploadedBy || 'Unknown',
        createdAt: doc.createdAt || new Date().toISOString(),
        updatedAt: doc.updatedAt || doc.createdAt || new Date().toISOString(),
        isConfidential: doc.isConfidential || false,
        hipaaCompliant: doc.hipaaCompliant !== false,
        documentSource: doc.documentSource || 'patient'
      }))
      
      const sortedDocuments = transformedDocuments
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      
      setDocuments(sortedDocuments)
      
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load case documents:', error)
      setError('Failed to load case documents. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDocumentSelect = (document: CaseDocument, index: number) => {
    setSelectedDocument(document)
    setSelectedIndex(index)
    onDocumentSelect?.(document)
  }

  const handleCloseDocument = () => {
    setSelectedDocument(null)
    setSelectedIndex(-1)
    onDocumentSelect?.(null)
  }

  const handleNavigate = (newIndex: number) => {
    if (newIndex >= 0 && newIndex < documents.length) {
      handleDocumentSelect(documents[newIndex], newIndex)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader className={`${sizes.iconLarge} text-primary-400 mx-auto mb-4 ${animations.spin}`} />
          <p className={`${typography.body} ${textColors.secondary}`}>Loading case documents...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className={`${sizes.iconLarge} text-red-400 mx-auto mb-4`} />
          <p className={`${typography.body} ${textColors.error}`}>{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
      {/* Document List - Left Side */}
      <div className="lg:col-span-1">
        <div className={commonClasses.card}>
          <div className="p-4">
            <h3 className={`${typography.h4} mb-4`}>📄 Case Documents</h3>
            
            {documents.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className={`${typography.body} ${textColors.secondary}`}>
                  No documents available for this case
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {documents.map((doc, index) => {
                  const FileIcon = getFileIcon(doc.mimeType)
                  const typeInfo = getDocumentTypeInfo(doc.documentType)
                  const isSelected = selectedDocument?.id === doc.id
                  
                  return (
                    <div
                      key={doc.id}
                      onClick={() => handleDocumentSelect(doc, index)}
                      className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                        isSelected 
                          ? 'border-primary-500 bg-primary-50 shadow-md' 
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <FileIcon className={`${sizes.iconMedium} text-primary-600 mt-1 flex-shrink-0`} />
                        <div className="flex-1 min-w-0">
                          <h4 className={`${typography.body} font-medium truncate`}>
                            {doc.title}
                          </h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${typeInfo.color}`}>
                              {typeInfo.label}
                            </span>
                          </div>
                          <div className={`${typography.caption} ${textColors.muted} mt-1`}>
                            <div>{formatFileSize(doc.fileSize)}</div>
                            <div>{new Date(doc.createdAt).toLocaleDateString()}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Document Viewer - Right Side */}
      <div className="lg:col-span-2">
        <div className={commonClasses.card}>
          {selectedDocument ? (
            <UnifiedDocumentViewer
              document={{
                id: selectedDocument.id,
                title: selectedDocument.title,
                originalFileName: selectedDocument.originalFileName,
                mimeType: selectedDocument.mimeType,
                fileSize: selectedDocument.fileSize,
                documentType: selectedDocument.documentType,
                patientId: selectedDocument.patientId,
                caseId: selectedDocument.caseId,
                uploadedBy: selectedDocument.uploadedBy,
                createdAt: selectedDocument.createdAt,
                isConfidential: selectedDocument.isConfidential,
                hipaaCompliant: selectedDocument.hipaaCompliant,
                documentSource: selectedDocument.documentSource
              }}
              onClose={handleCloseDocument}
              showControls={true}
              showPatientInfo={true}
              auditLog={true}
              className="w-full h-full"
              documents={documents.map(doc => ({
                id: doc.id,
                title: doc.title,
                originalFileName: doc.originalFileName,
                mimeType: doc.mimeType,
                fileSize: doc.fileSize,
                documentType: doc.documentType,
                patientId: doc.patientId,
                caseId: doc.caseId,
                uploadedBy: doc.uploadedBy,
                createdAt: doc.createdAt,
                isConfidential: doc.isConfidential,
                hipaaCompliant: doc.hipaaCompliant,
                documentSource: doc.documentSource
              }))}
              currentIndex={selectedIndex}
              onNavigate={handleNavigate}
            />
          ) : (
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <Eye className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className={`${typography.h3} mb-2`}>📋 Select a Document</h3>
                <p className={`${typography.body} ${textColors.secondary}`}>
                  Choose a document from the list to view it inline with the case details
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}