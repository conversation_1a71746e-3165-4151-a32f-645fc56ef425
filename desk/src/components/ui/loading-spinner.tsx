import * as React from "react"
import { cn } from "@/lib/utils"
import { animations, sizes, layouts } from "@/constants/theme"

export interface LoadingSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl"
  text?: string
  fullScreen?: boolean
}

const sizeVariants = {
  sm: "h-4 w-4",
  md: "h-6 w-6", 
  lg: "h-8 w-8",
  xl: "h-12 w-12"
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ className, size = "lg", text, fullScreen = false, ...props }, ref) => {
    const spinnerContent = (
      <div className={cn(layouts.flexCenter, "space-y-4", className)} {...props} ref={ref}>
        <div className="text-center">
          <div className={cn(
            animations.spin,
            "rounded-full border-b-4 border-blue-600 mx-auto",
            sizeVariants[size]
          )} />
          {text && (
            <p className="text-xl text-gray-600 font-medium mt-4">{text}</p>
          )}
        </div>
      </div>
    )

    if (fullScreen) {
      return (
        <div className={cn(layouts.page, layouts.flexCenter)}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {spinnerContent}
          </div>
        </div>
      )
    }

    return spinnerContent
  }
)
LoadingSpinner.displayName = "LoadingSpinner"

export { LoadingSpinner }