import React, { useEffect, useState, useRef } from 'react';
import EasyMD<PERSON> from 'easymde';
import 'easymde/dist/easymde.min.css';

// Define a type for EasyMDE instance with all needed methods
type EasyMDEInstance = EasyMDE & {
  togglePreview: () => void;
  toggleSideBySide: () => void;
  codemirror: any;
  value: (val?: string) => string;
  toTextArea: () => void;
}

interface MarkdownViewerProps {
  content: string;
  height?: number;
  className?: string;
}

/**
 * A markdown viewer component using EasyMDE
 * Provides a rich viewing experience for markdown content
 */
export const MarkdownViewer: React.FC<MarkdownViewerProps> = ({
  content,
  height = 300,
  className = '',
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const viewerRef = useRef<HTMLTextAreaElement | null>(null);
  const easyMDERef = useRef<EasyMDEInstance | null>(null);

  // Initialize EasyMDE when component mounts
  useEffect(() => {
    setIsMounted(true);
    
    if (viewerRef.current) {
      // Create EasyMDE instance in preview-only mode
      const easyMDE = new EasyMDE({
        element: viewerRef.current,
        autofocus: false,
        spellChecker: false,
        status: false,
        initialValue: content,
        minHeight: `${height}px`,
        maxHeight: `${height * 1.5}px`,
        toolbar: false, // No toolbar in viewer mode
        renderingConfig: {
          singleLineBreaks: true,
          codeSyntaxHighlighting: true,
        }
      });

      // Store the instance for cleanup
      easyMDERef.current = easyMDE as EasyMDEInstance;

      // Set initial content
      if (content) {
        easyMDE.value(content);
      }

      // Force preview mode
      try {
        (easyMDE as any).togglePreview();
        
        // Make it read-only by disabling the CodeMirror instance
        if (easyMDE.codemirror) {
          easyMDE.codemirror.setOption('readOnly', true);
        }
        
        // Add custom styles to the preview area
        setTimeout(() => {
          const previewContainer = document.querySelector('.EasyMDEContainer .editor-preview');
          if (previewContainer) {
            previewContainer.classList.add('prose', 'max-w-none', 'p-4');
          }
        }, 0);
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Error setting preview mode:', error);
      }

      // Clean up on unmount
      return () => {
        if (easyMDERef.current) {
          easyMDERef.current.toTextArea();
          easyMDERef.current = null;
        }
      };
    }
  }, []);

  // Update viewer content when prop changes
  useEffect(() => {
    if (easyMDERef.current && content !== easyMDERef.current.value()) {
      easyMDERef.current.value(content);
    }
  }, [content]);

  if (!isMounted) {
    return (
      <div 
        className={`border rounded-md p-4 bg-gray-50 ${className}`}
        style={{ height: `${height}px` }}
      >
        <div className="animate-pulse flex space-x-4">
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Custom styling code moved to the main useEffect to avoid hooks order issues

  return (
    <div className={`markdown-viewer ${className}`}>
      <textarea ref={viewerRef} style={{ display: 'none' }} />
      {!content && (
        <div className="text-gray-500 italic p-4">No content available</div>
      )}
    </div>
  );
};

export default MarkdownViewer;
