import * as React from "react"
import { cn } from "@/lib/utils"
import { shadows, backgroundColors, spacing, typography } from "@/constants/theme"

export interface DashboardCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  value: string | number
  description?: string
  icon?: React.ComponentType<any>
  variant?: "default" | "gradient"
  color?: "blue" | "green" | "purple" | "yellow" | "red" | "orange"
}

const colorVariants = {
  blue: {
    gradient: "bg-gradient-to-br from-blue-50 to-blue-100",
    border: "border-blue-200",
    iconBg: "bg-blue-500",
    titleColor: "text-blue-700",
    valueColor: "text-blue-900",
    descColor: "text-blue-600"
  },
  green: {
    gradient: "bg-gradient-to-br from-green-50 to-green-100",
    border: "border-green-200",
    iconBg: "bg-green-500",
    titleColor: "text-green-700",
    valueColor: "text-green-900",
    descColor: "text-green-600"
  },
  purple: {
    gradient: "bg-gradient-to-br from-purple-50 to-purple-100",
    border: "border-purple-200",
    iconBg: "bg-purple-500",
    titleColor: "text-purple-700",
    valueColor: "text-purple-900",
    descColor: "text-purple-600"
  },
  yellow: {
    gradient: "bg-gradient-to-br from-yellow-50 to-yellow-100",
    border: "border-yellow-200",
    iconBg: "bg-yellow-500",
    titleColor: "text-yellow-700",
    valueColor: "text-yellow-900",
    descColor: "text-yellow-600"
  },
  red: {
    gradient: "bg-gradient-to-br from-red-50 to-red-100",
    border: "border-red-200",
    iconBg: "bg-red-500",
    titleColor: "text-red-700",
    valueColor: "text-red-900",
    descColor: "text-red-600"
  },
  orange: {
    gradient: "bg-gradient-to-br from-orange-50 to-orange-100",
    border: "border-orange-200",
    iconBg: "bg-orange-500",
    titleColor: "text-orange-700",
    valueColor: "text-orange-900",
    descColor: "text-orange-600"
  }
}

const DashboardCard = React.forwardRef<HTMLDivElement, DashboardCardProps>(
  ({ className, title, value, description, icon: Icon, variant = "default", color = "blue", ...props }, ref) => {
    const colorScheme = colorVariants[color]
    
    return (
      <div
        ref={ref}
        className={cn(
          "overflow-hidden rounded-xl border",
          shadows.lg,
          variant === "gradient" ? colorScheme.gradient : backgroundColors.card,
          variant === "gradient" ? colorScheme.border : "border-gray-100",
          className
        )}
        {...props}
      >
        <div className={spacing.cardPadding}>
          <div className="flex items-center">
            {Icon && (
              <div className="flex-shrink-0">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center shadow-md",
                  colorScheme.iconBg
                )}>
                  <Icon className="w-4 h-4 text-white" />
                </div>
              </div>
            )}
            <div className={cn("w-0 flex-1", Icon ? "ml-3" : "")}>
              <dl>
                <dt className={cn(
                  "text-sm font-medium truncate",
                  variant === "gradient" ? colorScheme.titleColor : "text-gray-500"
                )}>
                  {title}
                </dt>
                <dd className={cn(
                  typography.h2.replace("text-gray-900", ""),
                  variant === "gradient" ? colorScheme.valueColor : "text-gray-900"
                )}>
                  {value}
                </dd>
                {description && (
                  <dd className={cn(
                    "text-xs",
                    variant === "gradient" ? colorScheme.descColor : "text-gray-500"
                  )}>
                    {description}
                  </dd>
                )}
              </dl>
            </div>
          </div>
        </div>
      </div>
    )
  }
)
DashboardCard.displayName = "DashboardCard"

export { DashboardCard }