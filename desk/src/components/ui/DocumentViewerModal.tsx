import { useEffect } from 'react'
import { X } from 'lucide-react'
import { UnifiedDocumentViewer } from './UnifiedDocumentViewer'

interface DocumentViewerModalProps {
  isOpen: boolean
  documentData: {
    id: string
    title: string
    fileName: string
    fileSize: number
    mimeType: string
    uploadedAt: string
    streamUrl: string
    blobUrl?: string | null
  } | null
  onClose: () => void
}

export function DocumentViewerModal({ 
  isOpen, 
  documentData, 
  onClose 
}: DocumentViewerModalProps) {
  if (!isOpen || !documentData) return null

  const handleClose = () => {
    // Clean up blob URLs to prevent memory leaks
    if (documentData?.blobUrl) {
      URL.revokeObjectURL(documentData.blobUrl)
    }
    onClose()
  }

  // Add Escape key binding to close modal with improved focus management
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        event.preventDefault()
        event.stopPropagation()
        handleClose()
      }
    }

    // Only add event listener when modal is open
    if (isOpen) {
      // Add event listener with capture to ensure it gets the event first
      window.document.addEventListener('keydown', handleEscapeKey, true)
      
      // Focus the modal container to ensure it receives keyboard events
      const modalElement = window.document.querySelector('[data-modal="document-viewer"]') as HTMLElement
      if (modalElement) {
        modalElement.focus()
      }
      
      // Cleanup event listener on unmount or when modal closes
      return () => {
        window.document.removeEventListener('keydown', handleEscapeKey, true)
      }
    }
  }, [isOpen])

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div 
        className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden"
        data-modal="document-viewer"
        tabIndex={-1}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
      >
        {/* Modal Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div>
            <h3 id="modal-title" className="text-lg font-medium text-gray-900">{documentData.title}</h3>
            <p className="text-sm text-gray-500">
              {documentData.fileName} • {Math.round(documentData.fileSize / 1024)} KB
            </p>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Modal Body - Unified Document Viewer */}
        <div className="max-h-[70vh] overflow-hidden">
          <UnifiedDocumentViewer
            document={{
              id: documentData.id,
              title: documentData.title,
              originalFileName: documentData.fileName,
              mimeType: documentData.mimeType,
              fileSize: documentData.fileSize,
              documentType: 'other'
            }}
            showControls={false} // Modal has its own header
            showPatientInfo={false}
            auditLog={true}
            className="w-full h-[70vh]"
          />
        </div>

        {/* Modal Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-500">
            🔒 Secure document access • All views are audited for HIPAA compliance • Download disabled for compliance
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
