/**
 * Standardized Modal Component for UI Consistency
 * 
 * This component provides a consistent modal pattern across the application
 * to eliminate modal styling inconsistencies and improve UI consistency toward 100/100.
 */

import React from 'react'
import { X } from 'lucide-react'
import {
  backgroundColors,
  textColors,
  buttonColors,
  animations
} from '@/constants/theme'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  showCloseButton?: boolean
  closeOnBackdropClick?: boolean
  className?: string
}

const sizeClasses = {
  sm: 'max-w-md',
  md: 'max-w-lg', 
  lg: 'max-w-2xl',
  xl: 'max-w-4xl',
  full: 'max-w-full mx-4'
}

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnBackdropClick = true,
  className = ''
}: ModalProps) {
  if (!isOpen) return null

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && closeOnBackdropClick) {
      onClose()
    }
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 ${animations.transition}`}
      onClick={handleBackdropClick}
    >
      <div
        className={`
          ${backgroundColors.card} 
          rounded-lg 
          shadow-xl 
          w-full 
          ${sizeClasses[size]} 
          max-h-[90vh] 
          overflow-hidden 
          ${animations.transition}
          ${className}
        `}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className={`flex items-center justify-between p-6 border-b ${backgroundColors.cardHover}`}>
          <h2 className={`text-lg font-semibold ${textColors.primary}`}>
            {title}
          </h2>
          {showCloseButton && (
            <button
              onClick={onClose}
              className={`
                p-1 
                rounded-md 
                ${textColors.muted} 
                hover:${textColors.primary} 
                ${backgroundColors.interactiveHover}
                ${animations.transition}
              `}
              aria-label="Close modal"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Modal Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {children}
        </div>
      </div>
    </div>
  )
}

// Modal Footer component for consistent footer styling
interface ModalFooterProps {
  children: React.ReactNode
  className?: string
}

export function ModalFooter({ children, className = '' }: ModalFooterProps) {
  return (
    <div className={`flex items-center justify-end space-x-3 p-6 border-t ${backgroundColors.cardHover} ${className}`}>
      {children}
    </div>
  )
}

// Modal Button components for consistent button styling
interface ModalButtonProps {
  children: React.ReactNode
  onClick: () => void
  variant?: 'primary' | 'secondary' | 'destructive'
  disabled?: boolean
  className?: string
}

export function ModalButton({ 
  children, 
  onClick, 
  variant = 'secondary', 
  disabled = false,
  className = '' 
}: ModalButtonProps) {
  const variantClasses = {
    primary: buttonColors.primary,
    secondary: buttonColors.secondary,
    destructive: buttonColors.destructive
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        px-4 py-2 
        rounded-md 
        font-medium 
        text-sm 
        ${variantClasses[variant]} 
        disabled:opacity-50 
        disabled:cursor-not-allowed 
        ${animations.transition}
        ${className}
      `}
    >
      {children}
    </button>
  )
}

// Export default modal component
export default Modal
