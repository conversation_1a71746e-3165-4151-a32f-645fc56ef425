import * as React from "react"
import { cn } from "@/lib/utils"
import { typography, layouts, shadows, backgroundColors } from "@/constants/theme"

export interface PageHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  description?: string
  action?: React.ReactNode
  centered?: boolean
  gradient?: boolean
}

const PageHeader = React.forwardRef<HTMLDivElement, PageHeaderProps>(
  ({ className, title, description, action, centered = true, gradient = true, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          backgroundColors.card,
          shadows.lg,
          "rounded-xl border border-gray-100",
          className
        )}
        {...props}
      >
        <div className="px-6 py-8 sm:p-8">
          <div className={cn(
            layouts.flexBetween,
            "flex-col space-y-4",
            "sm:flex-row sm:space-y-0 sm:items-center"
          )}>
            <div className={cn(centered ? "text-center sm:text-left" : "text-left", "flex-1")}>
              <h1 className={cn(
                typography.h1,
                "mb-3",
                gradient && "bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
              )}>
                {title}
              </h1>
              {description && (
                <p className={cn(typography.bodyLarge, "leading-relaxed")}>
                  {description}
                </p>
              )}
            </div>
            {action && (
              <div className="flex-shrink-0">
                {action}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }
)
PageHeader.displayName = "PageHeader"

export { PageHeader }