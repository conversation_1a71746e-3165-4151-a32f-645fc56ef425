import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Upload,
  FileText,
  Image,
  Trash2,
  Eye,
  Search,
  List,
  ArrowLeft,
  Grid3X3,
  Loader,
  File,
  AlertCircle,
  Calendar,
  User,
  X,
  Clock,
  FolderOpen,
  Plus,
  Shield,
  Filter,
  UserCheck,
  Activity
} from 'lucide-react'
import {
  backgroundColors,
  buttonColors,
  textColors,
  typography,
  shadows,
  layouts,
  forms,
  spacing,
  sizes,
  animations,
  commonClasses
} from '@/constants/theme'
import { PatientDocumentViewer } from './PatientDocumentViewer'

interface PatientDocument {
  id: string
  title: string
  description?: string
  originalFileName: string
  fileName: string
  fileSize: number
  mimeType: string
  documentType: 'medical_record' | 'lab_result' | 'prescription' | 'imaging' | 'insurance' | 'consent_form' | 'discharge_summary' | 'other'
  uploadedBy: string
  createdAt: string
  updatedAt: string
  patientId: string
  caseId?: string
  isConfidential?: boolean
  hipaaCompliant?: boolean
  documentSource?: 'patient' | 'medical'
}

interface PatientDocumentsProps {
  patientId?: string
  caseId?: string
  hideUpload?: boolean
  hideHeader?: boolean
  title?: string
  subtitle?: string
  emptyStateTitle?: string
  emptyStateDescription?: string
  showDelete?: boolean
  showDocumentType?: boolean
  showFileSize?: boolean
  showUploadedBy?: boolean
  compactMode?: boolean
  showPatientInfo?: boolean
  auditLog?: boolean
}

// Helper functions
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileIcon = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return Image
  if (mimeType === 'application/pdf') return FileText
  return File
}

const getFileTypeColor = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return 'bg-green-100 text-green-800 border-green-200'
  if (mimeType === 'application/pdf') return 'bg-red-100 text-red-800 border-red-200'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'bg-blue-100 text-blue-800 border-blue-200'
  return 'bg-gray-100 text-gray-800 border-gray-200'
}

const getPatientDocumentTypeInfo = (type: string) => {
  const types = {
    medical_record: { label: '🏥 Medical Record', color: 'bg-blue-100 text-blue-800', priority: 'high' },
    lab_result: { label: '🧪 Lab Result', color: 'bg-green-100 text-green-800', priority: 'high' },
    prescription: { label: '💊 Prescription', color: 'bg-purple-100 text-purple-800', priority: 'high' },
    imaging: { label: '📸 Medical Imaging', color: 'bg-indigo-100 text-indigo-800', priority: 'high' },
    insurance: { label: '🛡️ Insurance Document', color: 'bg-cyan-100 text-cyan-800', priority: 'medium' },
    consent_form: { label: '📝 Consent Form', color: 'bg-orange-100 text-orange-800', priority: 'high' },
    discharge_summary: { label: '📋 Discharge Summary', color: 'bg-teal-100 text-teal-800', priority: 'high' },
    other: { label: '📄 Other Document', color: 'bg-gray-100 text-gray-800', priority: 'low' }
  }
  return types[type as keyof typeof types] || types.other
}

export function PatientDocuments({ 
  patientId,
  caseId, 
  hideUpload = false, 
  hideHeader = false,
  title = "Patient Documents",
  subtitle = "Manage and view patient medical documents",
  emptyStateTitle = "No patient documents uploaded",
  emptyStateDescription = "Upload patient documents to get started",
  showDelete = true,
  showDocumentType = true,
  showFileSize = true,
  showUploadedBy = false,
  compactMode = false,
  showPatientInfo = true,
  auditLog = true
}: PatientDocumentsProps) {
  const { user } = useAuth()
  const [documents, setDocuments] = useState<PatientDocument[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')
  const [selectedDocument, setSelectedDocument] = useState<PatientDocument | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [documentPreviews, setDocumentPreviews] = useState<Record<string, string>>({})
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set())
  const [dragActive, setDragActive] = useState(false)
  
  // Ref to prevent duplicate API calls
  const loadingRef = useRef(false)

  // Reusable function to load patient documents
  const loadDocuments = async () => {
    // Prevent duplicate requests
    if (loadingRef.current) {
      return
    }
    
    loadingRef.current = true
    try {
      setLoading(true)
      setError(null)
      
      // Use existing API methods with patient-specific filtering
      const response = await apiClient.getDocuments(caseId || undefined)
      let documents: any[] = []
      if (Array.isArray(response)) {
        documents = response
      } else if (response && typeof response === 'object') {
        documents = (response as any).documents || (response as any).data || []
      } else {
        documents = []
      }
      
      // Filter for patient documents if patientId is provided
      if (patientId) {
        documents = documents.filter(doc => doc.patientId === patientId)
      }
      
      // Transform documents with patient-specific fields
      const transformedDocuments: PatientDocument[] = documents.map((doc: any) => ({
        id: doc.id,
        title: doc.title || doc.originalFileName || 'Unknown Document',
        description: doc.description,
        originalFileName: doc.originalFileName || doc.fileName || 'Unknown Document',
        fileName: doc.fileName || doc.originalFileName || 'Unknown Document',
        fileSize: doc.fileSize || 0,
        mimeType: doc.mimeType || 'application/octet-stream',
        documentType: doc.documentType || 'other',
        patientId: doc.patientId || patientId || '',
        caseId: doc.caseId || caseId || undefined,
        uploadedBy: doc.uploadedBy || 'Unknown',
        createdAt: doc.createdAt || new Date().toISOString(),
        updatedAt: doc.updatedAt || doc.createdAt || new Date().toISOString(),
        isConfidential: doc.isConfidential || false,
        hipaaCompliant: doc.hipaaCompliant !== false, // Default to true for patient documents
        documentSource: doc.documentSource || 'patient'
      }))
      
      const sortedDocuments = transformedDocuments
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      
      setDocuments(sortedDocuments)
      loadDocumentPreviews(sortedDocuments)
      
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load patient documents:', error)
      setError('Failed to load patient documents. Please try again.')
    } finally {
      setLoading(false)
      loadingRef.current = false
    }
  }

  // Load documents effect
  useEffect(() => {
    if (user && (patientId || caseId)) {
      loadDocuments()
    }
  }, [user, patientId, caseId])

  // Load document previews for images
  const loadDocumentPreviews = async (docs: PatientDocument[]) => {
    const imageDocuments = docs.filter(doc => doc.mimeType.startsWith('image/'))
    
    for (const doc of imageDocuments) {
      try {
        const blob = await apiClient.streamDocument(doc.id)
        const blobUrl = URL.createObjectURL(blob)
        setDocumentPreviews(prev => ({ ...prev, [doc.id]: blobUrl }))
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error(`Failed to load preview for patient document ${doc.id}:`, error)
      }
    }
  }

  // Drag and drop handlers
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(Array.from(e.dataTransfer.files))
    }
  }

  // File upload functionality with patient-specific metadata
  const handleFileUpload = async (files: File[] | React.ChangeEvent<HTMLInputElement>) => {
    let fileList: File[]
    
    if (Array.isArray(files)) {
      fileList = files
    } else {
      fileList = Array.from(files.target.files || [])
    }
    
    if (fileList.length === 0) return

    setIsUploading(true)
    
    try {
      for (const file of fileList) {
        const metadata = {
          title: file.name.split('.')[0],
          description: `Patient document uploaded ${new Date().toLocaleDateString()}`,
          documentType: 'other', // Default, should be selected by user in real implementation
          isConfidential: true, // Default to confidential for patient documents
          hipaaCompliant: true
        }
        
        const uploadResult = await apiClient.uploadDocument(caseId || null, file, metadata)
        // Log upload for audit
        if (auditLog) {
          }
      }
      
      // Reload documents using the reusable function
      await loadDocuments()
      
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Patient document upload failed:', error)
      
      if (typeof error === 'object' && error !== null) {
        if (error.message && typeof error.message === 'string') {
          if (error.message.includes('File too large')) {
            setError('File is too large. Maximum file size is 10MB.')
          } else if (error.message.includes('Invalid file type')) {
            setError('Invalid file type. Only medical document formats are allowed.')
          } else if (error.status === 400) {
            setError('Invalid patient document information. Please check your file and try again.')
          } else if (error.status === 401 || error.status === 403) {
            setError('You don\'t have permission to upload patient documents.')
          } else if (error.status >= 500) {
            setError('Server error. Please try again later or contact support.')
          } else {
            setError('Failed to upload patient document. Please try again or use a different file.')
          }
        } else {
          setError('Failed to upload patient document. Please try again or use a different file.')
        }
      } else {
        setError('Failed to upload patient document. Please try again or use a different file.')
      }
    } finally {
      setIsUploading(false)
    }
  }

  // View document functionality
  const handleViewDocument = async (doc: PatientDocument) => {
    try {
      setSelectedDocument(doc)
      
      // Log access for audit
      if (auditLog) {
        }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to view patient document:', error)
      setError('Failed to view patient document. Please try again.')
    }
  }

  // Get the correct streaming URL for patient documents
  const getPatientDocumentStreamUrl = (doc: PatientDocument) => {
    return apiClient.getDocumentStreamUrl(doc.id)
  }

  // Close document viewer
  const handleCloseDocument = () => {
    setSelectedDocument(null)
  }

  // Delete document functionality
  const handleDeleteDocument = async (documentId: string) => {
    if (!showDelete) return
    
    if (!confirm('Are you sure you want to delete this patient document? This action cannot be undone and will be logged for HIPAA compliance.')) {
      return
    }

    try {
      await apiClient.deleteDocument(documentId)
      
      // Log deletion for audit
      if (auditLog) {
        }
      
      setDocuments(prev => prev.filter(doc => doc.id !== documentId))
      
      if (documentPreviews[documentId]) {
        URL.revokeObjectURL(documentPreviews[documentId])
        setDocumentPreviews(prev => {
          const { [documentId]: removed, ...rest } = prev
          return rest
        })
      }
      
      if (selectedDocument?.id === documentId) {
        setSelectedDocument(null)
      }
      
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to delete patient document:', error)
      setError('Failed to delete patient document. Please try again.')
    }
  }

  // Toggle document selection
  const toggleDocumentSelection = (docId: string) => {
    const newSelected = new Set(selectedDocuments)
    if (newSelected.has(docId)) {
      newSelected.delete(docId)
    } else {
      newSelected.add(docId)
    }
    setSelectedDocuments(newSelected)
  }

  // Filter documents with patient-specific filters
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.originalFileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.patientId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === 'all' || doc.documentType === typeFilter
    const matchesPriority = priorityFilter === 'all' || getPatientDocumentTypeInfo(doc.documentType).priority === priorityFilter
    return matchesSearch && matchesType && matchesPriority
  })

  if (loading) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            <div className={commonClasses.loading}>
              <div className={commonClasses.loadingSpinner}></div>
              <span className={`ml-4 ${typography.bodyLarge} ${textColors.secondary}`}>✨ Loading patient documents...</span>
              <div className="mt-2 flex items-center justify-center space-x-2 text-xs text-gray-500">
                <Shield className="h-3 w-3" />
                <span>HIPAA Compliant Access</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error && documents.length === 0) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            <div className={commonClasses.errorAlert}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <AlertCircle className={`${sizes.iconLarge} ${textColors.error} mt-1 flex-shrink-0`} />
                  <div>
                    <h3 className={`${typography.h5} ${textColors.error} mb-2`}>⚠️ Something went wrong</h3>
                    <p className={`${typography.body} ${textColors.error}`}>{error}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setError(null)} 
                  className={`${textColors.error} hover:opacity-75`}
                >
                  <X className={sizes.iconMedium} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Document viewer
  if (selectedDocument) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            {/* Document Viewer Header */}
            <div className={commonClasses.card}>
              <div className={spacing.cardPadding}>
                <div className={layouts.flexBetween}>
                  <div className="flex items-center space-x-6">
                    <button
                      onClick={handleCloseDocument}
                      className={`${commonClasses.secondaryButton} ${animations.transitionAll}`}
                    >
                      <ArrowLeft className={`${sizes.iconSmall} mr-2`} />
                      Back to Patient Documents
                    </button>
                    <div>
                      <h1 className={typography.h2}>{selectedDocument.title}</h1>
                      <p className={`mt-2 ${typography.caption} flex items-center space-x-6`}>
                        <span className="flex items-center">
                          <File className={`${sizes.iconSmall} mr-2`} />
                          {selectedDocument.originalFileName}
                        </span>
                        <span className="flex items-center">
                          <Calendar className={`${sizes.iconSmall} mr-2`} />
                          {formatFileSize(selectedDocument.fileSize)}
                        </span>
                        {showPatientInfo && (
                          <span className="flex items-center">
                            <UserCheck className={`${sizes.iconSmall} mr-2`} />
                            Patient: {selectedDocument.patientId}
                          </span>
                        )}
                        {selectedDocument.isConfidential && (
                          <span className="flex items-center text-red-600">
                            <Shield className={`${sizes.iconSmall} mr-2`} />
                            Confidential
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    {showDelete && (
                      <button
                        onClick={() => handleDeleteDocument(selectedDocument.id)}
                        className="inline-flex items-center px-4 py-2 text-sm text-red-600 hover:text-red-900 border-2 border-red-300 rounded-full hover:bg-red-50 transition-all duration-200"
                      >
                        <Trash2 className={`${sizes.iconSmall} mr-2`} />
                        Delete
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Patient Document Viewer */}
            <div className={commonClasses.card}>
              <PatientDocumentViewer
                document={selectedDocument}
                streamUrl={getPatientDocumentStreamUrl(selectedDocument)}
                showControls={true}
                showPatientInfo={showPatientInfo}
                auditLog={auditLog}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div 
      className={layouts.page}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      <div className={layouts.container}>
        <div className={compactMode ? "space-y-6" : layouts.pageContent}>
          {/* Header */}
          {!hideHeader && (
            <div className={commonClasses.card}>
              <div className="px-8 py-12">
                <div className="text-center">
                  <h1 className={`${typography.h1} mb-4`}>
                    🏥 {title}
                  </h1>
                  <p className={`${typography.bodyLarge} ${textColors.secondary} mb-8 max-w-2xl mx-auto`}>{subtitle}</p>
                  <div className="flex items-center justify-center space-x-4">
                    <div className="flex items-center space-x-2 bg-primary-100 rounded-2xl p-2">
                      <button
                        onClick={() => setViewMode('card')}
                        className={`p-4 rounded-xl ${animations.transitionAll} ${
                          viewMode === 'card'
                            ? 'bg-white text-primary-600 shadow-lg transform scale-105'
                            : 'text-primary-500 hover:text-primary-700 hover:bg-primary-50'
                        }`}
                        title="Card view"
                      >
                        <Grid3X3 className={sizes.iconMedium} />
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`p-4 rounded-xl ${animations.transitionAll} ${
                          viewMode === 'list'
                            ? 'bg-white text-primary-600 shadow-lg transform scale-105'
                            : 'text-primary-500 hover:text-primary-700 hover:bg-primary-50'
                        }`}
                        title="List view"
                      >
                        <List className={sizes.iconMedium} />
                      </button>
                    </div>
                  </div>
                  <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-blue-700">
                    <Shield className="h-4 w-4" />
                    <span>🔒 HIPAA Compliant Document Management</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Controls & Upload Section */}
          <div className={commonClasses.card}>
            <div className={spacing.cardPadding}>
              <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between space-y-6 lg:space-y-0">
                <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6 flex-1">
                  {/* Search */}
                  <div className="relative flex-1 max-w-md">
                    <Search className={`absolute left-4 top-1/2 transform -translate-y-1/2 ${sizes.iconMedium} text-primary-400`} />
                    <input
                      type="text"
                      placeholder="🔍 Search patient documents..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className={`${forms.input} pl-12`}
                    />
                  </div>

                  {/* Filters */}
                  <div className="flex items-center space-x-4">
                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      className={forms.select}
                    >
                      <option value="all">📋 All Types</option>
                      <option value="medical_record">🏥 Medical Records</option>
                      <option value="lab_result">🧪 Lab Results</option>
                      <option value="prescription">💊 Prescriptions</option>
                      <option value="imaging">📸 Medical Imaging</option>
                      <option value="insurance">🛡️ Insurance</option>
                      <option value="consent_form">📝 Consent Forms</option>
                      <option value="discharge_summary">📋 Discharge Summaries</option>
                      <option value="other">📄 Other</option>
                    </select>

                    <select
                      value={priorityFilter}
                      onChange={(e) => setPriorityFilter(e.target.value)}
                      className={forms.select}
                    >
                      <option value="all">🎯 All Priority</option>
                      <option value="high">🔴 High Priority</option>
                      <option value="medium">🟡 Medium Priority</option>
                      <option value="low">🟢 Low Priority</option>
                    </select>

                    {selectedDocuments.size > 0 && (
                      <button
                        onClick={() => {
                          if (confirm(`Are you sure you want to delete ${selectedDocuments.size} patient document(s)? This action will be logged for HIPAA compliance.`)) {
                            // Bulk delete implementation would go here
                          }
                        }}
                        className="inline-flex items-center px-4 py-3 text-sm text-red-600 hover:text-red-900 border-2 border-red-300 rounded-full hover:bg-red-50 transition-all duration-200"
                      >
                        <Trash2 className={`${sizes.iconSmall} mr-2`} />
                        Delete ({selectedDocuments.size})
                      </button>
                    )}
                  </div>
                </div>

                {/* Upload Section */}
                {!hideUpload && (
                  <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <label className={`${commonClasses.primaryButton} cursor-pointer ${dragActive ? 'scale-105 shadow-2xl' : ''}`}>
                      <Upload className={`${sizes.iconMedium} mr-3`} />
                      📤 Upload Patient Documents
                      <input
                        type="file"
                        className="hidden"
                        onChange={handleFileUpload}
                        multiple
                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,.dcm"
                        disabled={isUploading}
                      />
                    </label>
                    {isUploading && (
                      <div className="flex items-center space-x-3 text-primary-600">
                        <Loader className={`${sizes.iconMedium} ${animations.spin}`} />
                        <span className={`${typography.body} font-medium`}>Uploading...</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className={commonClasses.errorAlert}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <AlertCircle className={`${sizes.iconMedium} ${textColors.error} mt-1 flex-shrink-0`} />
                  <div>
                    <h3 className={`${typography.h5} ${textColors.error} mb-1`}>⚠️ Something went wrong</h3>
                    <p className={`${typography.body} ${textColors.error}`}>{error}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setError(null)} 
                  className={`${textColors.error} hover:opacity-75`}
                >
                  <X className={sizes.iconMedium} />
                </button>
              </div>
            </div>
          )}

          {/* Drag and Drop Overlay */}
          {dragActive && (
            <div className="fixed inset-0 bg-blue-600 bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50">
              <div className={`${commonClasses.card} p-12 border-2 border-dashed border-blue-400`}>
                <div className="text-center">
                  <Upload className={`${sizes.iconXLarge} text-blue-600 mx-auto mb-6`} />
                  <h3 className={`${typography.h3} mb-3`}>📤 Drop patient documents here</h3>
                  <p className={`${typography.body} ${textColors.secondary}`}>Release to upload securely with HIPAA compliance</p>
                </div>
              </div>
            </div>
          )}

          {/* Documents Display */}
          {filteredDocuments.length > 0 ? (
            <div className={viewMode === 'card' ? layouts.gridCols4 : 'space-y-4'}>
              {filteredDocuments.map((doc) => {
                const FileIcon = getFileIcon(doc.mimeType)
                const typeInfo = getPatientDocumentTypeInfo(doc.documentType)
                const isSelected = selectedDocuments.has(doc.id)
                
                return (
                  <div 
                    key={doc.id} 
                    className={`group relative ${commonClasses.card} cursor-pointer ${
                      isSelected ? 'ring-2 ring-primary-500 shadow-2xl' : ''
                    } ${viewMode === 'list' ? 'flex items-center p-6' : ''} ${
                      doc.isConfidential ? 'border-l-4 border-red-400' : ''
                    }`}
                    onClick={() => handleViewDocument(doc)}
                  >
                    {/* Selection Checkbox */}
                    <div
                      className="absolute top-4 left-4 z-10"
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleDocumentSelection(doc.id)
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => {}}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                    </div>

                    {viewMode === 'card' ? (
                      <>
                        {/* Card View */}
                        <div className="aspect-square bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden rounded-xl mb-4">
                          {doc.mimeType.startsWith('image/') && documentPreviews[doc.id] ? (
                            <img
                              src={documentPreviews[doc.id]}
                              alt={doc.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <FileIcon className={`${sizes.iconXLarge} text-blue-400`} />
                            </div>
                          )}
                          
                          {/* File Type Badge */}
                          <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-medium border ${getFileTypeColor(doc.mimeType)}`}>
                            {doc.mimeType === 'application/pdf' ? 'PDF' :
                             doc.mimeType.startsWith('image/') ? 'IMG' :
                             doc.mimeType.includes('word') ? 'DOC' : 'FILE'}
                          </div>

                          {/* Confidential Badge */}
                          {doc.isConfidential && (
                            <div className="absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                              <Shield className="h-3 w-3 inline mr-1" />
                              Confidential
                            </div>
                          )}

                          {/* Quick Actions Overlay */}
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <div className="flex items-center space-x-3">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleViewDocument(doc)
                                }}
                                className="p-3 bg-white rounded-full text-gray-700 hover:text-primary-600 transition-colors shadow-lg"
                                title="View patient document"
                              >
                                <Eye className={sizes.iconSmall} />
                              </button>
                              {showDelete && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleDeleteDocument(doc.id)
                                  }}
                                  className="p-3 bg-white rounded-full text-gray-700 hover:text-red-600 transition-colors shadow-lg"
                                  title="Delete patient document"
                                >
                                  <Trash2 className={sizes.iconSmall} />
                                </button>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Card Content */}
                        <div className={spacing.cardPaddingSmall}>
                          <div className="mb-4">
                            <h3 className={`${typography.h5} truncate mb-2`}>
                              {doc.title}
                            </h3>
                            {showDocumentType && (
                              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                                {typeInfo.label}
                              </span>
                            )}
                          </div>
                          
                          <div className={`space-y-2 ${typography.caption} ${textColors.muted}`}>
                            {showPatientInfo && (
                              <div className="flex items-center">
                                <UserCheck className={`${sizes.iconSmall} mr-2`} />
                                <span>Patient: {doc.patientId}</span>
                              </div>
                            )}
                            {showFileSize && (
                              <div className="flex items-center">
                                <Calendar className={`${sizes.iconSmall} mr-2`} />
                                <span>{formatFileSize(doc.fileSize)}</span>
                              </div>
                            )}
                            <div className="flex items-center">
                              <Clock className={`${sizes.iconSmall} mr-2`} />
                              <span>{new Date(doc.createdAt).toLocaleDateString()}</span>
                            </div>
                            {showUploadedBy && (
                              <div className="flex items-center">
                                <User className={`${sizes.iconSmall} mr-2`} />
                                <span>{doc.uploadedBy}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* List View */}
                        <div className="flex items-center space-x-6 flex-1">
                          <div className="flex-shrink-0">
                            <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl flex items-center justify-center">
                              <FileIcon className={`${sizes.iconLarge} text-blue-400`} />
                            </div>
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className={`${typography.h5} truncate`}>
                                {doc.title}
                              </h3>
                              {showDocumentType && (
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                                  {typeInfo.label}
                                </span>
                              )}
                              {doc.isConfidential && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  <Shield className="h-3 w-3 mr-1" />
                                  Confidential
                                </span>
                              )}
                            </div>
                            
                            <div className={`flex items-center space-x-6 ${typography.caption} ${textColors.muted}`}>
                              <span className="flex items-center">
                                <File className={`${sizes.iconSmall} mr-2`} />
                                {doc.originalFileName}
                              </span>
                              {showPatientInfo && (
                                <span className="flex items-center">
                                  <UserCheck className={`${sizes.iconSmall} mr-2`} />
                                  Patient: {doc.patientId}
                                </span>
                              )}
                              {showFileSize && (
                                <span className="flex items-center">
                                  <Calendar className={`${sizes.iconSmall} mr-2`} />
                                  {formatFileSize(doc.fileSize)}
                                </span>
                              )}
                              <span className="flex items-center">
                                <Clock className={`${sizes.iconSmall} mr-2`} />
                                {new Date(doc.createdAt).toLocaleDateString()}
                              </span>
                              {showUploadedBy && (
                                <span className="flex items-center">
                                  <User className={`${sizes.iconSmall} mr-2`} />
                                  {doc.uploadedBy}
                                </span>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-3">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleViewDocument(doc)
                              }}
                              className="p-3 text-primary-400 hover:text-primary-600 rounded-xl hover:bg-primary-50 transition-colors"
                              title="View patient document"
                            >
                              <Eye className={sizes.iconSmall} />
                            </button>
                            {showDelete && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleDeleteDocument(doc.id)
                                }}
                                className="p-3 text-gray-400 hover:text-red-600 rounded-xl hover:bg-red-50 transition-colors"
                                title="Delete patient document"
                              >
                                <Trash2 className={sizes.iconSmall} />
                              </button>
                            )}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                )
              })}
            </div>
          ) : (
            <div className={commonClasses.card}>
              <div className="text-center py-20 px-8">
                <div className="mb-8">
                  <FolderOpen className={`h-24 w-24 text-blue-300 mx-auto mb-6`} />
                </div>
                <h3 className={`${typography.h2} mb-6`}>
                  🏥 {emptyStateTitle}
                </h3>
                <p className={`${typography.bodyLarge} ${textColors.secondary} mb-10 max-w-md mx-auto`}>
                  {emptyStateDescription}
                </p>
                {!hideUpload && (
                  <label className={`${commonClasses.primaryButton} cursor-pointer text-xl px-8 py-4`}>
                    <Upload className={`${sizes.iconLarge} mr-3`} />
                    🚀 Upload Your First Patient Document
                    <input
                      type="file"
                      className="hidden"
                      onChange={handleFileUpload}
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,.dcm"
                    />
                  </label>
                )}
                <div className={`mt-10 ${typography.caption} ${textColors.muted} space-y-2`}>
                  <p>📝 Supported formats: PDF, JPG, PNG, DOC, DOCX, TXT, DICOM</p>
                  <p>💡 Drag and drop files anywhere to upload</p>
                  <div className="flex items-center justify-center space-x-2 text-blue-700 mt-4">
                    <Shield className="h-4 w-4" />
                    <span>🔒 HIPAA Compliant Storage</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}