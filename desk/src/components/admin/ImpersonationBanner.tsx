import { useNavigate } from 'react-router-dom'
import { apiClient } from '@/services/api'
import { AlertCircle, UserX } from 'lucide-react'

export function ImpersonationBanner() {
  const navigate = useNavigate()
  const isImpersonating = apiClient.isImpersonating()
  const impersonatedUser = apiClient.getImpersonatedUserInfo()
  
  if (!isImpersonating || !impersonatedUser) {
    return null
  }
  
  const handleEndImpersonation = async () => {
    try {
      await apiClient.endImpersonation()
      navigate('/admin/impersonation')
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to end impersonation:', error)
    }
  }
  
  return (
    <div className="bg-amber-500 text-white px-4 py-2 flex items-center justify-between">
      <div className="flex items-center">
        <AlertCircle className="h-5 w-5 mr-2" />
        <span>
          You are impersonating <strong>{impersonatedUser.name}</strong> ({impersonatedUser.role}). 
          All actions will be logged.
        </span>
      </div>
      <button
        onClick={handleEndImpersonation}
        className="bg-white text-amber-700 px-3 py-1 rounded-md text-sm font-medium flex items-center hover:bg-amber-50 focus:outline-none focus:ring-2 focus:ring-white"
      >
        <UserX className="h-4 w-4 mr-1" />
        End Impersonation
      </button>
    </div>
  )
}
