import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Upload,
  FileText,
  Image,
  Trash2,
  Eye,
  Search,
  List,
  ArrowLeft,
  Grid3X3,
  Loader,
  File,
  AlertCircle,
  Calendar,
  User,
  X,
  Clock,
  FolderOpen,
  Plus
} from 'lucide-react'
import {
  backgroundColors,
  buttonColors,
  textColors,
  typography,
  shadows,
  layouts,
  forms,
  spacing,
  sizes,
  animations,
  commonClasses
} from '@/constants/theme'
import { UniversalDocumentViewer } from './UniversalDocumentViewer'

interface Document {
  id: string
  title: string
  description?: string
  originalFileName: string
  fileName: string
  fileSize: number
  mimeType: string
  documentType: string
  uploadedBy: string
  createdAt: string
  updatedAt: string
  caseId?: string
  credentialId?: string
  documentSource?: 'medical' | 'credential'
}

interface DocumentsProps {
  caseId?: string
  hideUpload?: boolean
  hideHeader?: boolean
  title?: string
  subtitle?: string
  emptyStateTitle?: string
  emptyStateDescription?: string
  showDelete?: boolean
  showDocumentType?: boolean
  showFileSize?: boolean
  showUploadedBy?: boolean
  compactMode?: boolean
  doctorAcceptanceStatus?: 'pending' | 'accepted' | 'declined' | 'closed' | null
  caseStatus?: 'draft' | 'submitted' | 'in_review' | 'assigned' | 'in_progress' | 'completed' | 'archived' | 'cancelled';
}

// Helper functions
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileIcon = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return Image
  if (mimeType === 'application/pdf') return FileText
  return File
}

const getFileTypeColor = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return 'bg-green-100 text-green-800 border-green-200'
  if (mimeType === 'application/pdf') return 'bg-red-100 text-red-800 border-red-200'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'bg-blue-100 text-blue-800 border-blue-200'
  return 'bg-gray-100 text-gray-800 border-gray-200'
}

const getDocumentTypeInfo = (type: string) => {
  const types = {
    medical_record: { label: '🏥 Medical Record', color: 'bg-blue-100 text-blue-800' },
    lab_result: { label: '🧪 Lab Result', color: 'bg-green-100 text-green-800' },
    prescription: { label: '💊 Prescription', color: 'bg-purple-100 text-purple-800' },
    imaging: { label: '📸 Imaging', color: 'bg-indigo-100 text-indigo-800' },
    certificate: { label: '📜 Certificate', color: 'bg-yellow-100 text-yellow-800' },
    license: { label: '🏆 License', color: 'bg-orange-100 text-orange-800' },
    other: { label: '📄 Other', color: 'bg-gray-100 text-gray-800' }
  }
  return types[type as keyof typeof types] || types.other
}

export function Documents({
  caseId,
  hideUpload = false,
  hideHeader = false,
  title = "Documents",
  subtitle = "Manage and view your documents",
  emptyStateTitle = "No documents uploaded",
  emptyStateDescription = "Upload documents to get started",
  showDelete = true,
  showDocumentType = true,
  showFileSize = true,
  showUploadedBy = false,
  compactMode = false,
  doctorAcceptanceStatus,
  caseStatus
}: DocumentsProps) {
  const { user } = useAuth()
  
  // Check if doctor has accepted the case (for upload/delete restrictions)
  const isDoctorAccepted = user?.role === 'doctor' ? doctorAcceptanceStatus === 'accepted' : true;
  const isCaseCompleted = caseStatus === 'completed';
  
  // For unaccepted doctors, disable upload and delete operations
  const canUpload = user?.role === 'doctor' ? isDoctorAccepted && !isCaseCompleted : !isCaseCompleted;
  const canDelete = user?.role === 'doctor' ? isDoctorAccepted && !isCaseCompleted : !isCaseCompleted;
  const [documents, setDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [documentPreviews, setDocumentPreviews] = useState<Record<string, string>>({})
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set())
  const [dragActive, setDragActive] = useState(false)
  
  // Ref to prevent duplicate API calls
  const loadingRef = useRef(false)

  // Reusable function to load documents
  const loadDocuments = async () => {
    // Prevent duplicate requests
    if (loadingRef.current) {
      return
    }
    
    loadingRef.current = true
    try {
      setLoading(true)
      setError(null)
      
      // Pass caseId to API for server-side filtering with proper permission checks
      const response = await apiClient.getDocuments(caseId || undefined)
      let documents: any[] = []
      if (Array.isArray(response)) {
        documents = response
      } else if (response && typeof response === 'object') {
        documents = (response as any).documents || (response as any).data || []
      } else {
        documents = []
      }
      
      // No client-side filtering needed - API handles all permission checks and filtering
      const transformedDocuments: Document[] = documents.map((doc: any) => ({
        id: doc.id,
        title: doc.title || doc.originalFileName || 'Unknown Document',
        description: doc.description,
        originalFileName: doc.originalFileName || doc.fileName || 'Unknown Document',
        fileName: doc.fileName || doc.originalFileName || 'Unknown Document',
        fileSize: doc.fileSize || 0,
        mimeType: doc.mimeType || 'application/octet-stream',
        documentType: doc.documentType || 'other',
        caseId: doc.caseId || undefined,
        uploadedBy: doc.uploadedBy || 'Unknown',
        createdAt: doc.createdAt || new Date().toISOString(),
        updatedAt: doc.updatedAt || doc.createdAt || new Date().toISOString()
      }))
      
      const sortedDocuments = transformedDocuments
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      
      setDocuments(sortedDocuments)
      loadDocumentPreviews(sortedDocuments)
      
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load documents:', error)
      setError('Failed to load documents. Please try again.')
    } finally {
      setLoading(false)
      loadingRef.current = false
    }
  }

  // Load documents effect
  useEffect(() => {
    if (user) {
      loadDocuments()
    }
  }, [user, caseId])

  // Load document previews for images
  const loadDocumentPreviews = async (docs: Document[]) => {
    const imageDocuments = docs.filter(doc => doc.mimeType.startsWith('image/'))
    
    for (const doc of imageDocuments) {
      try {
        const blob = await apiClient.streamDocument(doc.id)
        const blobUrl = URL.createObjectURL(blob)
        setDocumentPreviews(prev => ({ ...prev, [doc.id]: blobUrl }))
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error(`Failed to load preview for document ${doc.id}:`, error)
      }
    }
  }

  // Drag and drop handlers
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(Array.from(e.dataTransfer.files))
    }
  }

  // File upload functionality
  const handleFileUpload = async (files: File[] | React.ChangeEvent<HTMLInputElement>) => {
    if (!canUpload) {
      setError('You must accept the case assignment before uploading documents.')
      return
    }
    
    let fileList: File[]
    
    if (Array.isArray(files)) {
      fileList = files
    } else {
      fileList = Array.from(files.target.files || [])
    }
    
    if (fileList.length === 0) return

    setIsUploading(true)
    
    try {
      for (const file of fileList) {
        const metadata = {
          title: file.name.split('.')[0],
          description: `Uploaded ${new Date().toLocaleDateString()}`,
          documentType: 'other'
        }
        
        const uploadResult = await apiClient.uploadDocument(caseId || null, file, metadata)
        }
      
      // Reload documents using the reusable function
      await loadDocuments()
      
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Upload failed:', error)
      
      if (typeof error === 'object' && error !== null) {
        if (error.message && typeof error.message === 'string') {
          if (error.message.includes('File too large')) {
            setError('File is too large. Maximum file size is 10MB.')
          } else if (error.message.includes('Invalid file type')) {
            setError('Invalid file type. Only images, PDFs, Word docs, and text files are allowed.')
          } else if (error.status === 400) {
            setError('Invalid document information. Please check your file and try again.')
          } else if (error.status === 401 || error.status === 403) {
            setError('You don\'t have permission to upload documents.')
          } else if (error.status >= 500) {
            setError('Server error. Please try again later or contact support.')
          } else {
            setError('Failed to upload document. Please try again or use a different file.')
          }
        } else {
          setError('Failed to upload document. Please try again or use a different file.')
        }
      } else {
        setError('Failed to upload document. Please try again or use a different file.')
      }
    } finally {
      setIsUploading(false)
    }
  }

  // View document functionality
  const handleViewDocument = async (doc: Document) => {
    try {
      setSelectedDocument(doc)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to view document:', error)
      setError('Failed to view document. Please try again.')
    }
  }

  // Get the correct streaming URL based on document source
  const getDocumentStreamUrl = (doc: Document) => {
    if (doc.documentSource === 'credential' && doc.credentialId) {
      return apiClient.getCredentialDocumentStreamUrl(doc.credentialId, doc.id)
    }
    return apiClient.getDocumentStreamUrl(doc.id)
  }

  // Close document viewer
  const handleCloseDocument = () => {
    setSelectedDocument(null)
  }

  // Delete document functionality
  const handleDeleteDocument = async (documentId: string) => {
    if (!showDelete || !canDelete) {
      if (!canDelete) {
        setError('You must accept the case assignment before deleting documents.')
      }
      return
    }
    
    if (!confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      return
    }

    try {
      await apiClient.deleteDocument(documentId)
      
      setDocuments(prev => prev.filter(doc => doc.id !== documentId))
      
      if (documentPreviews[documentId]) {
        URL.revokeObjectURL(documentPreviews[documentId])
        setDocumentPreviews(prev => {
          const { [documentId]: removed, ...rest } = prev
          return rest
        })
      }
      
      if (selectedDocument?.id === documentId) {
        setSelectedDocument(null)
      }
      
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to delete document:', error)
      setError('Failed to delete document. Please try again.')
    }
  }

  // Bulk delete functionality
  const handleBulkDelete = async () => {
    if (selectedDocuments.size === 0 || !canDelete) {
      if (!canDelete) {
        setError('You must accept the case assignment before deleting documents.')
      }
      return
    }
    
    if (!confirm(`Are you sure you want to delete ${selectedDocuments.size} document(s)? This action cannot be undone.`)) {
      return
    }

    try {
      for (const docId of selectedDocuments) {
        await apiClient.deleteDocument(docId)
      }
      
      setDocuments(prev => prev.filter(doc => !selectedDocuments.has(doc.id)))
      setSelectedDocuments(new Set())
      
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to delete documents:', error)
      setError('Failed to delete some documents. Please try again.')
    }
  }

  // Toggle document selection
  const toggleDocumentSelection = (docId: string) => {
    const newSelected = new Set(selectedDocuments)
    if (newSelected.has(docId)) {
      newSelected.delete(docId)
    } else {
      newSelected.add(docId)
    }
    setSelectedDocuments(newSelected)
  }

  // Filter documents
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.originalFileName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === 'all' || doc.documentType === typeFilter
    return matchesSearch && matchesType
  })

  if (loading) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            <div className={commonClasses.loading}>
              <div className={commonClasses.loadingSpinner}></div>
              <span className={`ml-4 ${typography.bodyLarge} ${textColors.secondary}`}>✨ Loading your documents...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error && documents.length === 0) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            <div className={commonClasses.errorAlert}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <AlertCircle className={`${sizes.iconLarge} ${textColors.error} mt-1 flex-shrink-0`} />
                  <div>
                    <h3 className={`${typography.h5} ${textColors.error} mb-2`}>⚠️ Something went wrong</h3>
                    <p className={`${typography.body} ${textColors.error}`}>{error}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setError(null)} 
                  className={`${textColors.error} hover:opacity-75`}
                >
                  <X className={sizes.iconMedium} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Document viewer - Optimized for full screen real estate
  if (selectedDocument) {
    return (
      <div className="fixed inset-0 bg-white z-50 flex flex-col">
        {/* Compact Header Bar */}
        <div className="flex-shrink-0 bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleCloseDocument}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div className="flex items-center space-x-3">
                <h1 className="text-lg font-semibold text-gray-900 truncate max-w-md">
                  {selectedDocument.title}
                </h1>
                <span className="text-sm text-gray-500">•</span>
                <span className="text-sm text-gray-500">
                  {formatFileSize(selectedDocument.fileSize)}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {showDelete && (
                <button
                  onClick={() => handleDeleteDocument(selectedDocument.id)}
                  className="inline-flex items-center px-3 py-2 text-sm text-red-600 hover:text-red-700 bg-red-50 rounded-lg hover:bg-red-100 transition-colors"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Full Screen Document Viewer */}
        <div className="flex-1 overflow-hidden">
          <UniversalDocumentViewer
            document={{
              id: selectedDocument.id,
              title: selectedDocument.title,
              originalFileName: selectedDocument.originalFileName,
              mimeType: selectedDocument.mimeType,
              fileSize: selectedDocument.fileSize
            }}
            streamUrl={getDocumentStreamUrl(selectedDocument)}
            showControls={true}
          />
        </div>
      </div>
    )
  }

  return (
    <div
      className={compactMode ? "h-full flex flex-col" : layouts.page}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      <div className={compactMode ? "h-full flex flex-col" : layouts.container}>
        <div className={compactMode ? "h-full flex flex-col space-y-4" : layouts.pageContent}>
          {/* Header */}
          {!hideHeader && (
            <div className={commonClasses.card}>
              <div className="px-8 py-12">
                <div className="text-center">
                  <h1 className={`${typography.h1} mb-4`}>
                    📄 {title}
                  </h1>
                  <p className={`${typography.bodyLarge} ${textColors.secondary} mb-8 max-w-2xl mx-auto`}>{subtitle}</p>
                  <div className="flex items-center justify-center space-x-4">
                    <div className="flex items-center space-x-2 bg-primary-100 rounded-2xl p-2">
                      <button
                        onClick={() => setViewMode('card')}
                        className={`p-4 rounded-xl ${animations.transitionAll} ${
                          viewMode === 'card'
                            ? 'bg-white text-primary-600 shadow-lg transform scale-105'
                            : 'text-primary-500 hover:text-primary-700 hover:bg-primary-50'
                        }`}
                        title="Card view"
                      >
                        <Grid3X3 className={sizes.iconMedium} />
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`p-4 rounded-xl ${animations.transitionAll} ${
                          viewMode === 'list'
                            ? 'bg-white text-primary-600 shadow-lg transform scale-105'
                            : 'text-primary-500 hover:text-primary-700 hover:bg-primary-50'
                        }`}
                        title="List view"
                      >
                        <List className={sizes.iconMedium} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Controls & Upload Section */}
          <div className={compactMode ? "bg-white border-b border-gray-200 px-4 py-3 flex-shrink-0" : commonClasses.card}>
            <div className={compactMode ? "" : spacing.cardPadding}>
              <div className={compactMode ? "flex items-center justify-between space-x-4" : "flex flex-col lg:flex-row items-start lg:items-center justify-between space-y-6 lg:space-y-0"}>
                <div className={compactMode ? "flex items-center space-x-4 flex-1" : "flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6 flex-1"}>
                  {/* Search */}
                  <div className={compactMode ? "relative flex-1 max-w-xs" : "relative flex-1 max-w-md"}>
                    <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${compactMode ? 'h-4 w-4' : sizes.iconMedium} text-primary-400`} />
                    <input
                      type="text"
                      placeholder={compactMode ? "Search..." : "🔍 Search documents..."}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className={compactMode ? "w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent" : `${forms.input} pl-12`}
                    />
                  </div>

                  {/* Filters */}
                  <div className="flex items-center space-x-2">
                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      className={compactMode ? "px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent" : forms.select}
                    >
                      <option value="all">{compactMode ? "All" : "📋 All Types"}</option>
                      <option value="medical_record">{compactMode ? "Medical" : "🏥 Medical Records"}</option>
                      <option value="lab_result">{compactMode ? "Lab" : "🧪 Lab Results"}</option>
                      <option value="prescription">{compactMode ? "Prescription" : "💊 Prescriptions"}</option>
                      <option value="imaging">{compactMode ? "Imaging" : "📸 Imaging"}</option>
                      <option value="certificate">{compactMode ? "Certificate" : "📜 Certificates"}</option>
                      <option value="license">{compactMode ? "License" : "🏆 Licenses"}</option>
                      <option value="other">{compactMode ? "Other" : "📄 Other"}</option>
                    </select>

                    {selectedDocuments.size > 0 && (
                      <button
                        onClick={handleBulkDelete}
                        className={compactMode ? "inline-flex items-center px-3 py-2 text-sm text-red-600 hover:text-red-700 bg-red-50 rounded-lg hover:bg-red-100 transition-colors" : "inline-flex items-center px-4 py-3 text-sm text-red-600 hover:text-red-900 border-2 border-red-300 rounded-full hover:bg-red-50 transition-all duration-200"}
                      >
                        <Trash2 className={`${compactMode ? 'h-4 w-4' : sizes.iconSmall} mr-1`} />
                        Delete ({selectedDocuments.size})
                      </button>
                    )}
                  </div>
                </div>

                {/* Upload Section */}
                {!hideUpload && (
                  <div className="flex items-center space-x-2">
                    <label className={compactMode ? "inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors cursor-pointer text-sm font-medium" : `${commonClasses.primaryButton} cursor-pointer ${dragActive ? 'scale-105 shadow-2xl' : ''}`}>
                      <Upload className={`${compactMode ? 'h-4 w-4' : sizes.iconMedium} mr-2`} />
                      {compactMode ? "Upload" : "📤 Upload Documents"}
                      <input
                        type="file"
                        className="hidden"
                        onChange={handleFileUpload}
                        multiple
                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt"
                        disabled={isUploading}
                      />
                    </label>
                    {isUploading && (
                      <div className="flex items-center space-x-2 text-primary-600">
                        <Loader className={`${compactMode ? 'h-4 w-4' : sizes.iconMedium} ${animations.spin}`} />
                        <span className={`${compactMode ? 'text-sm' : typography.body} font-medium`}>Uploading...</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className={commonClasses.errorAlert}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <AlertCircle className={`${sizes.iconMedium} ${textColors.error} mt-1 flex-shrink-0`} />
                  <div>
                    <h3 className={`${typography.h5} ${textColors.error} mb-1`}>⚠️ Something went wrong</h3>
                    <p className={`${typography.body} ${textColors.error}`}>{error}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setError(null)} 
                  className={`${textColors.error} hover:opacity-75`}
                >
                  <X className={sizes.iconMedium} />
                </button>
              </div>
            </div>
          )}

          {/* Drag and Drop Overlay */}
          {dragActive && (
            <div className="fixed inset-0 bg-primary-600 bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50">
              <div className={`${commonClasses.card} p-12 border-2 border-dashed border-primary-400`}>
                <div className="text-center">
                  <Upload className={`${sizes.iconXLarge} text-primary-600 mx-auto mb-6`} />
                  <h3 className={`${typography.h3} mb-3`}>📤 Drop files here</h3>
                  <p className={`${typography.body} ${textColors.secondary}`}>Release to upload your documents</p>
                </div>
              </div>
            </div>
          )}

          {/* Documents Display */}
          {filteredDocuments.length > 0 ? (
            <div className={compactMode ? "flex-1 overflow-y-auto px-4 pb-4" : ""}>
              <div className={viewMode === 'card' ? layouts.gridCols4 : 'space-y-4'}>
                {filteredDocuments.map((doc) => {
                  const FileIcon = getFileIcon(doc.mimeType)
                  const typeInfo = getDocumentTypeInfo(doc.documentType)
                  const isSelected = selectedDocuments.has(doc.id)
                  
                  return (
                    <div
                      key={doc.id}
                      className={`group relative ${commonClasses.card} cursor-pointer ${
                        isSelected ? 'ring-2 ring-primary-500 shadow-2xl' : ''
                      } ${viewMode === 'list' ? 'flex items-center p-6' : ''}`}
                      onClick={() => handleViewDocument(doc)}
                    >
                      {/* Selection Checkbox */}
                      <div
                        className="absolute top-4 left-4 z-10"
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleDocumentSelection(doc.id)
                        }}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => {}}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      </div>

                      {viewMode === 'card' ? (
                        <>
                          {/* Card View */}
                          <div className="aspect-square bg-gradient-to-br from-primary-50 to-primary-100 relative overflow-hidden rounded-xl mb-4">
                            {doc.mimeType.startsWith('image/') && documentPreviews[doc.id] ? (
                              <img
                                src={documentPreviews[doc.id]}
                                alt={doc.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <FileIcon className={`${sizes.iconXLarge} text-primary-400`} />
                              </div>
                            )}
                            
                            {/* File Type Badge */}
                            <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-medium border ${getFileTypeColor(doc.mimeType)}`}>
                              {doc.mimeType === 'application/pdf' ? 'PDF' :
                               doc.mimeType.startsWith('image/') ? 'IMG' :
                               doc.mimeType.includes('word') ? 'DOC' : 'FILE'}
                            </div>

                            {/* Quick Actions Overlay */}
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="flex items-center space-x-3">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleViewDocument(doc)
                                  }}
                                  className="p-3 bg-white rounded-full text-gray-700 hover:text-primary-600 transition-colors shadow-lg"
                                  title="View document"
                                >
                                  <Eye className={sizes.iconSmall} />
                                </button>
                                {showDelete && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleDeleteDocument(doc.id)
                                    }}
                                    className="p-3 bg-white rounded-full text-gray-700 hover:text-red-600 transition-colors shadow-lg"
                                    title="Delete document"
                                  >
                                    <Trash2 className={sizes.iconSmall} />
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Card Content */}
                          <div className={spacing.cardPaddingSmall}>
                            <div className="mb-4">
                              <h3 className={`${typography.h5} truncate mb-2`}>
                                {doc.title}
                              </h3>
                              {showDocumentType && (
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                                  {typeInfo.label}
                                </span>
                              )}
                            </div>
                            
                            <div className={`space-y-2 ${typography.caption} ${textColors.muted}`}>
                              {showFileSize && (
                                <div className="flex items-center">
                                  <Calendar className={`${sizes.iconSmall} mr-2`} />
                                  <span>{formatFileSize(doc.fileSize)}</span>
                                </div>
                              )}
                              <div className="flex items-center">
                                <Clock className={`${sizes.iconSmall} mr-2`} />
                                <span>{new Date(doc.createdAt).toLocaleDateString()}</span>
                              </div>
                              {showUploadedBy && (
                                <div className="flex items-center">
                                  <User className={`${sizes.iconSmall} mr-2`} />
                                  <span>{doc.uploadedBy}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </>
                      ) : (
                        <>
                          {/* List View */}
                          <div className="flex items-center space-x-6 flex-1">
                            <div className="flex-shrink-0">
                              <div className="w-16 h-16 bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl flex items-center justify-center">
                                <FileIcon className={`${sizes.iconLarge} text-primary-400`} />
                              </div>
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className={`${typography.h5} truncate`}>
                                  {doc.title}
                                </h3>
                                {showDocumentType && (
                                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                                    {typeInfo.label}
                                  </span>
                                )}
                              </div>
                              
                              <div className={`flex items-center space-x-6 ${typography.caption} ${textColors.muted}`}>
                                <span className="flex items-center">
                                  <File className={`${sizes.iconSmall} mr-2`} />
                                  {doc.originalFileName}
                                </span>
                                {showFileSize && (
                                  <span className="flex items-center">
                                    <Calendar className={`${sizes.iconSmall} mr-2`} />
                                    {formatFileSize(doc.fileSize)}
                                  </span>
                                )}
                                <span className="flex items-center">
                                  <Clock className={`${sizes.iconSmall} mr-2`} />
                                  {new Date(doc.createdAt).toLocaleDateString()}
                                </span>
                                {showUploadedBy && (
                                  <span className="flex items-center">
                                    <User className={`${sizes.iconSmall} mr-2`} />
                                    {doc.uploadedBy}
                                  </span>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-3">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleViewDocument(doc)
                                }}
                                className="p-3 text-primary-400 hover:text-primary-600 rounded-xl hover:bg-primary-50 transition-colors"
                                title="View document"
                              >
                                <Eye className={sizes.iconSmall} />
                              </button>
                              {showDelete && canDelete && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleDeleteDocument(doc.id)
                                  }}
                                  className="p-3 text-gray-400 hover:text-red-600 rounded-xl hover:bg-red-50 transition-colors"
                                  title="Delete document"
                                >
                                  <Trash2 className={sizes.iconSmall} />
                                </button>
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          ) : (
            <div className={commonClasses.card}>
              <div className="text-center py-20 px-8">
                <div className="mb-8">
                  <FolderOpen className={`h-24 w-24 text-primary-300 mx-auto mb-6`} />
                </div>
                <h3 className={`${typography.h2} mb-6`}>
                  📁 {emptyStateTitle}
                </h3>
                <p className={`${typography.bodyLarge} ${textColors.secondary} mb-10 max-w-md mx-auto`}>
                  {emptyStateDescription}
                </p>
                {!hideUpload && canUpload && (
                  <label className={`${commonClasses.primaryButton} cursor-pointer text-xl px-8 py-4`}>
                    <Upload className={`${sizes.iconLarge} mr-3`} />
                    🚀 Upload Your First Document
                    <input
                      type="file"
                      className="hidden"
                      onChange={handleFileUpload}
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt"
                    />
                  </label>
                )}
                
                {/* Show message for unaccepted doctors in empty state */}
                {!hideUpload && !canUpload && user?.role === 'doctor' && (
                  <div className="text-center text-amber-600 bg-amber-50 px-6 py-4 rounded-lg border border-amber-200 max-w-md mx-auto">
                    <AlertCircle className="h-6 w-6 mx-auto mb-2" />
                    <p className="font-medium">Accept case assignment to upload documents</p>
                  </div>
                )}
                <div className={`mt-10 ${typography.caption} ${textColors.muted} space-y-2`}>
                  <p>📝 Supported formats: PDF, JPG, PNG, DOC, DOCX, TXT</p>
                  <p>💡 Drag and drop files anywhere to upload</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
