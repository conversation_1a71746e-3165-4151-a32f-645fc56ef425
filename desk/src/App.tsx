import { Routes, Route, Navigate } from 'react-router-dom'

// Import components
import { AuthProvider } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Layout } from '@/components/layout/Layout'

// Auth Pages
import { LoginPage } from '@/pages/auth/LoginPage'
import { RegisterPage } from '@/pages/auth/RegisterPage'

// Dashboard Pages
import { DashboardPage } from '@/pages/DashboardPage'

// Doctor Pages
import { CreateOpinion } from '@/pages/doctor/CreateOpinion'
import { PendingOpinions } from '@/pages/doctor/PendingOpinions'
import { CompletedOpinions } from '@/pages/doctor/CompletedOpinions'
import { CurrentWorkload } from '@/pages/doctor/CurrentWorkload'
import { DoctorCredentials } from '@/pages/doctor/DoctorCredentials'
import { DoctorDocuments } from '@/pages/doctor/DoctorDocuments'
import { DoctorPublicSettings } from '@/pages/doctor/DoctorPublicSettings'

// Agent Pages
import { CaseAssignment } from '@/pages/agent/CaseAssignment'

// Admin Pages
import { UserManagement } from '@/pages/admin/UserManagement'
import { PatientManagement } from '@/pages/admin/PatientManagement'
import { UserImpersonation } from '@/pages/admin/UserImpersonation'
import LegalCompliance from '@/pages/admin/LegalCompliance';
import LegalComplianceList from '@/pages/admin/LegalComplianceList';
import LegalComplianceEdit from '@/pages/admin/LegalComplianceEdit';
import LegalComplianceView from '@/pages/admin/LegalComplianceView';
import AdminAuditLogs from '@/pages/admin/AuditLogs'
import CaseCoordination from '@/pages/admin/CaseCoordination'
import { CredentialManagement } from '@/pages/admin/CredentialManagement'
import { DoctorManagement } from '@/pages/admin/DoctorManagement'
import CollaborativeEditorTest from '@/pages/test/CollaborativeEditorTest'

// Shared Components
import { Cases } from '@/components/shared/Cases'
import { CaseDetail } from '@/components/shared/CaseDetail'
import { Appointments } from '@/components/shared/Appointments'
import { AppointmentDetails } from '@/components/shared/AppointmentDetails'



// Utility Pages
import { NotFoundPage } from '@/pages/NotFoundPage'

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-background">
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          
          {/* Protected Dashboard */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout>
                  <DashboardPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Unified Cases Route - accessible to doctor/admin/agent roles */}
          <Route
            path="/cases"
            element={
              <ProtectedRoute requiredRole={["doctor", "admin", "agent"]}>
                <Layout>
                  <Cases />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/cases/:id"
            element={
              <ProtectedRoute requiredRole={["doctor", "admin", "agent"]}>
                <Layout>
                  <CaseDetail />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Doctor Routes */}
          <Route
            path="/doctor/cases/:id/opinion"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <CreateOpinion />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Doctor Opinion Routes */}
          <Route
            path="/doctor/opinions/pending"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <PendingOpinions />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/doctor/opinions/completed"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <CompletedOpinions />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Doctor Current Workload Route */}
          <Route
            path="/doctor/workload/current"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <CurrentWorkload />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Doctor Appointment Routes */}
          <Route
            path="/doctor/appointments"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <Appointments 
                    userRole="doctor"
                    showCreateButton={true}
                    title="My Appointments"
                    description="View and manage your scheduled appointments"
                  />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/doctor/appointments/:appointmentId"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <AppointmentDetails userRole="doctor" />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Doctor Professional Profile Routes */}
          <Route
            path="/doctor/credentials"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <DoctorCredentials />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/doctor/documents"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <DoctorDocuments />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/doctor/public-settings"
            element={
              <ProtectedRoute requiredRole="doctor">
                <Layout>
                  <DoctorPublicSettings />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Agent Routes */}
          <Route
            path="/agent/assignments"
            element={
              <ProtectedRoute requiredRole={["agent", "admin"]}>
                <Layout>
                  <CaseAssignment />
                </Layout>
              </ProtectedRoute>
            }
          />
          

          {/* Admin Routes */}
          <Route
            path="/admin/users"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <UserManagement />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/patients"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <PatientManagement />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/impersonation"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <UserImpersonation />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Legal & Compliance Routes - Admin */}
          <Route
            path="/admin/legal-compliance"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <LegalComplianceList />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/legal-compliance/cards"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <LegalCompliance />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/legal-compliance/new"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <LegalComplianceEdit />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/legal-compliance/edit/:id"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <LegalComplianceEdit />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/legal-compliance/view/:id"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <LegalComplianceView />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Audit Logs Route */}
          <Route
            path="/admin/audit"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <AdminAuditLogs />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Case Coordination Route */}
          <Route
            path="/admin/case-coordination"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <CaseCoordination />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Credential Management Route */}
          <Route
            path="/admin/credentials"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <CredentialManagement />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Doctor Management Route */}
          <Route
            path="/admin/doctors"
            element={
              <ProtectedRoute requiredRole="admin">
                <Layout>
                  <DoctorManagement />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Test Route for Collaborative Editor */}
          <Route
            path="/test/collaborative-editor"
            element={
              <ProtectedRoute>
                <Layout>
                  <CollaborativeEditorTest />
                </Layout>
              </ProtectedRoute>
            }
          />

          {/* Role-based redirects */}
          <Route
            path="/doctor"
            element={<Navigate to="/cases" replace />}
          />
          <Route
            path="/agent"
            element={<Navigate to="/agent/assignments" replace />}
          />
          <Route
            path="/admin"
            element={<Navigate to="/cases" replace />}
          />
          
          {/* Fallback routes */}
          <Route path="/404" element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </div>
    </AuthProvider>
  )
}

export default App
