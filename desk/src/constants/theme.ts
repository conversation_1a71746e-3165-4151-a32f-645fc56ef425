/**
 * Centralized Theme Constants for UI Consistency
 * 
 * This file centralizes all color classes, status configurations, and UI patterns
 * to ensure 100% consistency across the application and eliminate hardcoded classes.
 */

// Status configurations for consistent UI across all components
export const statusColors = {
  // Case statuses
  draft: 'bg-gray-100 text-gray-800',
  submitted: 'bg-blue-100 text-blue-800', 
  assigned: 'bg-yellow-100 text-yellow-800',
  in_review: 'bg-purple-100 text-purple-800',
  completed: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
  
  // Appointment statuses
  scheduled: 'bg-blue-100 text-blue-800',
  confirmed: 'bg-green-100 text-green-800',
  in_progress: 'bg-yellow-100 text-yellow-800',
  no_show: 'bg-gray-100 text-gray-800',
  
  // Opinion statuses
  approved: 'bg-green-100 text-green-800',
  reviewed: 'bg-blue-100 text-blue-800',
  
  // Audit action statuses
  create: 'bg-green-100 text-green-800',
  read: 'bg-blue-100 text-blue-800',
  update: 'bg-yellow-100 text-yellow-800',
  delete: 'bg-red-100 text-red-800',
  admin: 'bg-purple-100 text-purple-800',
  auth: 'bg-gray-100 text-gray-800'
} as const

// Urgency level configurations
export const urgencyColors = {
  low: 'bg-gray-100 text-gray-600',
  medium: 'bg-blue-100 text-blue-600', 
  high: 'bg-orange-100 text-orange-600',
  urgent: 'bg-red-100 text-red-600'
} as const

// Appointment type configurations
export const appointmentTypeColors = {
  consultation: 'bg-blue-50 text-blue-700',
  follow_up: 'bg-green-50 text-green-700',
  review: 'bg-purple-50 text-purple-700',
  emergency: 'bg-red-50 text-red-700'
} as const

// Role-based quick login button colors
export const roleColors = {
  patient: 'bg-blue-100 text-blue-700 hover:bg-blue-200',
  doctor: 'bg-green-100 text-green-700 hover:bg-green-200',
  admin: 'bg-purple-100 text-purple-700 hover:bg-purple-200',
  agent: 'bg-orange-100 text-orange-700 hover:bg-orange-200'
} as const

// Common background colors - Updated to match Continuia website
export const backgroundColors = {
  // Page backgrounds
  page: 'bg-gradient-to-br from-primary-50 via-white to-primary-100',
  card: 'bg-white',
  cardHover: 'hover:bg-primary-50',
  
  // Interactive backgrounds
  interactive: 'bg-primary-50 hover:bg-primary-100',
  interactiveHover: 'hover:bg-primary-100',
  
  // Document/file type backgrounds
  documentPreview: 'bg-primary-50',
  documentGrid: 'bg-primary-100',
  
  // Kanban column backgrounds
  kanbanColumn: 'bg-primary-50',
  kanbanColumnHeader: 'bg-primary-200',
  
  // Error states
  errorBackground: 'bg-red-50',
  errorBorder: 'border-red-200',
  errorText: 'text-red-700',
  
  // Success states
  successBackground: 'bg-green-50',
  successBorder: 'border-green-200',
  successText: 'text-green-700',
  
  // Info states
  infoBackground: 'bg-primary-50',
  infoBorder: 'border-primary-200',
  infoText: 'text-primary-700'
} as const

// Button color configurations - Updated to match Continuia website
export const buttonColors = {
  primary: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5',
  secondary: 'bg-white text-primary-600 hover:bg-primary-50 focus:ring-primary-500 border-2 border-primary-200 hover:border-primary-300 shadow-md hover:shadow-lg',
  outline: 'border-2 border-primary-500 bg-transparent text-primary-600 hover:bg-primary-500 hover:text-white focus:ring-primary-500',
  destructive: 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500 shadow-lg hover:shadow-xl',
  ghost: 'text-primary-600 hover:bg-primary-50 focus:ring-primary-500',
  
  // Action-specific buttons
  edit: 'bg-primary-500 text-white hover:bg-primary-600 shadow-lg hover:shadow-xl',
  cancel: 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300',
  save: 'bg-green-500 text-white hover:bg-green-600 shadow-lg hover:shadow-xl',
  delete: 'bg-red-500 text-white hover:bg-red-600 shadow-lg hover:shadow-xl'
} as const

// Border colors
export const borderColors = {
  default: 'border-gray-300',
  hover: 'hover:border-gray-200',
  focus: 'focus:border-primary-500',
  error: 'border-red-300',
  success: 'border-green-300',
  transparent: 'border-transparent'
} as const

// Text colors
export const textColors = {
  primary: 'text-gray-900',
  secondary: 'text-gray-600', 
  muted: 'text-gray-500',
  error: 'text-red-700',
  success: 'text-green-700',
  warning: 'text-yellow-700',
  info: 'text-blue-700',
  
  // Interactive text
  link: 'text-primary-600 hover:text-primary-500',
  linkSecondary: 'text-indigo-600 hover:text-indigo-500'
} as const

// Common spacing patterns
export const spacing = {
  // Padding patterns
  cardPadding: 'p-6',
  cardPaddingSmall: 'p-4',
  buttonPadding: 'px-4 py-2',
  buttonPaddingSmall: 'px-3 py-1.5',
  buttonPaddingLarge: 'px-6 py-3',
  
  // Margin patterns
  sectionMargin: 'mb-6',
  elementMargin: 'mb-4',
  smallMargin: 'mb-2',
  
  // Gap patterns
  gridGap: 'gap-6',
  flexGap: 'gap-4',
  smallGap: 'gap-2'
} as const

// Common size patterns
export const sizes = {
  // Icon sizes
  iconSmall: 'h-4 w-4',
  iconMedium: 'h-5 w-5', 
  iconLarge: 'h-6 w-6',
  iconXLarge: 'h-8 w-8',
  
  // Avatar sizes
  avatarSmall: 'h-8 w-8',
  avatarMedium: 'h-10 w-10',
  avatarLarge: 'h-12 w-12',
  
  // Button sizes
  buttonSmall: 'text-xs',
  buttonMedium: 'text-sm',
  buttonLarge: 'text-base'
} as const

// Animation patterns
export const animations = {
  transition: 'transition-colors duration-200',
  transitionAll: 'transition-all duration-200',
  transitionFast: 'transition-colors duration-150',
  spin: 'animate-spin',
  
  // Hover effects
  hoverScale: 'hover:scale-105 transition-transform duration-200',
  hoverShadow: 'hover:shadow-md transition-shadow duration-200'
} as const


// Helper function to get status color
export const getStatusColor = (status: string): string => {
  return statusColors[status as keyof typeof statusColors] || statusColors.draft
}

// Helper function to get urgency color  
export const getUrgencyColor = (urgency: string): string => {
  return urgencyColors[urgency as keyof typeof urgencyColors] || urgencyColors.low
}

// Helper function to get appointment type color
export const getAppointmentTypeColor = (type: string): string => {
  return appointmentTypeColors[type as keyof typeof appointmentTypeColors] || appointmentTypeColors.consultation
}

// Helper function to get role color
export const getRoleColor = (role: string): string => {
  return roleColors[role as keyof typeof roleColors] || roleColors.patient
}

// Typography system for consistent text styling
export const typography = {
  // Headings
  h1: 'text-3xl font-bold text-gray-900',
  h2: 'text-2xl font-bold text-gray-900',
  h3: 'text-xl font-semibold text-gray-900',
  h4: 'text-lg font-semibold text-gray-900',
  h5: 'text-base font-semibold text-gray-900',
  
  // Body text
  body: 'text-base text-gray-600',
  bodyLarge: 'text-lg text-gray-600',
  bodySmall: 'text-sm text-gray-600',
  
  // Captions and labels
  caption: 'text-sm text-gray-500',
  label: 'text-sm font-medium text-gray-700',
  
  // Interactive text
  link: 'text-primary-600 hover:text-primary-500 font-medium',
  linkSecondary: 'text-indigo-600 hover:text-indigo-500 font-medium'
} as const

// Shadow system for consistent elevation
export const shadows = {
  none: 'shadow-none',
  sm: 'shadow-sm',
  default: 'shadow',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
  '2xl': 'shadow-2xl',
  
  // Interactive shadows
  hover: 'hover:shadow-lg',
  hoverXl: 'hover:shadow-xl',
  focus: 'focus:shadow-outline',
  
  // Card shadows
  card: 'shadow-lg',
  cardHover: 'hover:shadow-xl transition-shadow duration-200'
} as const

// Layout patterns for consistent spacing and structure - Updated to match Continuia website
export const layouts = {
  // Container patterns
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  containerSmall: 'max-w-4xl mx-auto px-4 sm:px-6 lg:px-8',
  containerLarge: 'max-w-full mx-auto px-4 sm:px-6 lg:px-8',
  
  // Page patterns - Updated to match website gradient
  page: 'min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100',
  pageContent: 'py-8 space-y-8',
  
  // Grid patterns
  gridCols2: 'grid grid-cols-1 md:grid-cols-2 gap-8',
  gridCols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8',
  gridCols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8',
  
  // Flex patterns
  flexBetween: 'flex items-center justify-between',
  flexCenter: 'flex items-center justify-center',
  flexStart: 'flex items-center justify-start'
} as const

// Form patterns for consistent form styling
export const forms = {
  // Input patterns
  input: 'w-full px-4 py-3 text-base border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
  inputSmall: 'w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
  
  // Select patterns
  select: 'w-full px-4 py-3 text-base border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white',
  selectSmall: 'px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white',
  
  // Label patterns
  label: 'block text-base font-medium text-gray-700 mb-2',
  labelSmall: 'block text-sm font-medium text-gray-700 mb-1',
  
  // Error patterns
  error: 'text-sm text-red-600 mt-1',
  errorInput: 'border-red-300 focus:border-red-500 focus:ring-red-500'
} as const

// Update common classes to use new typography and shadows - Updated to match Continuia website
export const commonClasses = {
  // Card patterns - Updated with rounded corners and enhanced shadows
  card: `${backgroundColors.card} rounded-2xl ${shadows.lg} border border-primary-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1`,
  cardHover: `${backgroundColors.card} rounded-2xl ${shadows.cardHover} border border-primary-100 ${backgroundColors.cardHover}`,
  
  // Button patterns - Updated with rounded-full and enhanced styling
  primaryButton: `${buttonColors.primary} px-6 py-3 rounded-full font-medium ${animations.transitionAll}`,
  secondaryButton: `${buttonColors.secondary} px-6 py-3 rounded-full font-medium ${animations.transitionAll}`,
  outlineButton: `${buttonColors.outline} px-6 py-3 rounded-full font-medium ${animations.transitionAll}`,
  
  // Input patterns
  input: forms.input,
  
  // Badge patterns - Updated with more rounded corners
  badge: 'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium',
  
  // Loading patterns
  loading: `flex justify-center items-center py-12`,
  loadingSpinner: `${animations.spin} ${sizes.iconXLarge} text-primary-500`,
  
  // Alert patterns - Updated with rounded corners
  errorAlert: `${backgroundColors.errorBackground} ${backgroundColors.errorBorder} border ${textColors.error} px-4 py-3 rounded-xl`,
  successAlert: `${backgroundColors.successBackground} ${backgroundColors.successBorder} border ${textColors.success} px-4 py-3 rounded-xl`,
  infoAlert: `${backgroundColors.infoBackground} ${backgroundColors.infoBorder} border ${textColors.info} px-4 py-3 rounded-xl`
} as const

// Export all theme constants as default
export default {
  statusColors,
  urgencyColors,
  appointmentTypeColors,
  roleColors,
  backgroundColors,
  buttonColors,
  borderColors,
  textColors,
  typography,
  shadows,
  layouts,
  forms,
  spacing,
  sizes,
  animations,
  commonClasses,
  getStatusColor,
  getUrgencyColor,
  getAppointmentTypeColor,
  getRoleColor
}
