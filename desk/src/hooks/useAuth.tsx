import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { apiClient } from '@/services/api'

interface User {
  id: string
  email: string
  role: 'patient' | 'doctor' | 'agent' | 'admin' // Keep for backward compatibility
  roles?: ('patient' | 'doctor' | 'agent' | 'admin')[] // Add OPAL roles array
  firstName: string
  lastName: string
  isActive: boolean
  isEmailVerified: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
  phone?: string
  profileImage?: string
}

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  register: (userData: RegisterData) => Promise<void>
  isLoading: boolean
}

interface RegisterData {
  email: string
  password: string
  name: string
  role: 'patient' | 'doctor' | 'agent' | 'admin'
  phone?: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for existing session on mount
    const token = localStorage.getItem('auth_token')
    if (token) {
      // Validate token with API
      apiClient.getCurrentUser()
        .then(response => {
          setUser(response.data)
        })
        .catch(error => {
          // TODO: Replace with proper error reporting
  console.error('Token validation failed:', error)
          apiClient.clearToken()
          setUser(null)
        })
        .finally(() => {
          setIsLoading(false)
        })
    } else {
      setIsLoading(false)
    }
  }, [])

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const response = await apiClient.login(email, password)
      setUser(response.data.user)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Login error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: RegisterData) => {
    setIsLoading(true)
    try {
      const response = await apiClient.register(userData)
      setUser(response.data.user)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Registration error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      await apiClient.logout()
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Logout error:', error)
    } finally {
      setUser(null)
    }
  }

  return (
    <AuthContext.Provider value={{ user, login, logout, register, isLoading }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
