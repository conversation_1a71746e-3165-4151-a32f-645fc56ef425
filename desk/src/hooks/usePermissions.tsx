import { useAuth } from './useAuth'
import { PERMISSION_MATRIX, PermissionService, UserContext, ResourceContext } from '@/shared/permissions.js'

export function usePermissions() {
  const { user } = useAuth()

  const checkPermission = (resourceType: string, action: string, resource?: any): boolean => {
    if (!user) return false

    // Convert user to shared UserContext format
    const userContext: UserContext = {
      id: user.id,
      role: user.role,
      email: user.email
    };

    // Convert resource to shared ResourceContext format
    const resourceContext: ResourceContext | undefined = resource ? {
      ...resource
    } : undefined;

    // Use shared permission service
    return PermissionService.canAccessResource(userContext, `${resourceType}:${action}`, resourceContext);
  }

  return {
    checkPermission,
    canUpdateCase: (caseData?: any) => checkPermission('cases', 'update', caseData),
    canCreateCase: () => checkPermission('cases', 'create'),
    canReadCase: (caseData?: any) => checkPermission('cases', 'read', caseData),
    canDeleteCase: (caseData?: any) => checkPermission('cases', 'delete', caseData),
    canSubmitCase: (caseData?: any) => checkPermission('cases', 'submit', caseData),
    canAssignCase: (caseData?: any) => checkPermission('cases', 'assign', caseData),
    canCompleteCase: (caseData?: any) => checkPermission('cases', 'complete', caseData)
  }
}
