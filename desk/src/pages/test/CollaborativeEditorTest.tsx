import React from 'react';
import { CollabClinicalEditor } from '../../components/shared/CollabClinicalEditor';

const CollaborativeEditorTest: React.FC = () => {
  const handleSave = (content: string) => {
    console.log('Content saved:', content);
  };

  const handleError = (error: string) => {
    console.error('Editor error:', error);
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Collaborative Editor Test</h1>
      
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-lg font-semibold mb-4">Test Case Notes</h2>
        
        <CollabClinicalEditor
          caseId="test-case-123"
          noteType="test-note-type"
          placeholder="Start typing to test collaborative editing..."
          height={500}
          onSave={handleSave}
          onError={handleError}
        />
      </div>
      
      <div className="mt-6 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Test Instructions:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Open this page in multiple browser tabs/windows</li>
          <li>Type in one tab and see if changes appear in others</li>
          <li>Check the connection status indicator</li>
          <li>Look for collaborator presence indicators</li>
          <li>Check browser console for any errors</li>
        </ul>
      </div>
    </div>
  );
};

export default CollaborativeEditorTest;
