import { useAuth } from '@/hooks/useAuth'
import { Doctor<PERSON>ashboard } from '@/pages/doctor/DoctorDashboard'
import { AgentDashboard } from '@/pages/agent/AgentDashboard'

export function DashboardPage() {
  const { user } = useAuth()

  // Route to the appropriate dashboard based on user role
  switch (user?.role) {
    case 'doctor':
      return <DoctorDashboard />
    case 'agent':
      return <AgentDashboard />
    case 'admin':
      // For now, use agent dashboard for admin - can be enhanced later
      return <AgentDashboard />
    case 'patient':
      // Patients should not access desk app
      return (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Access Restricted</h3>
          <p className="mt-2 text-sm text-gray-500">
            This application is for healthcare professionals only. Please use the patient portal.
          </p>
        </div>
      )
    default:
      return (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Welcome to Continuia Desk</h3>
          <p className="mt-2 text-sm text-gray-500">Please contact support for account setup.</p>
        </div>
      )
  }
}
