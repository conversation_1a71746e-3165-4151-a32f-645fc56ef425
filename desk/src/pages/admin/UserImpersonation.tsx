import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Search,
  AlertCircle,
  X,
  Eye,
  ArrowRight,
  UserCog
} from 'lucide-react'

interface User {
  id: string
  email: string
  role: 'patient' | 'doctor' | 'agent' | 'admin'
  name: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  verificationStatus: 'verified' | 'unverified' | 'pending'
  createdAt: string
  phone?: string
  profileImage?: string
}

export function UserImpersonation() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [impersonating, setImpersonating] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showConfirmModal, setShowConfirmModal] = useState(false)

  // Load users from API
  useEffect(() => {
    loadUsers()
  }, [])

  // Filter users when search term or role filter changes
  useEffect(() => {
    filterUsers()
  }, [searchTerm, roleFilter, users])

  const loadUsers = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await apiClient.getUsers()
      const allUsers = (response as any).data || response || []
      
      // Filter out the current admin user and other admin users
      const filteredUsers = allUsers.filter((u: User) => 
        u.id !== user?.id && u.status === 'active'
      )
      
      setUsers(filteredUsers)
      setIsLoading(false)
    } catch (err: any) {
      setError(err.message || 'Failed to load users')
      setIsLoading(false)
    }
  }

  const filterUsers = () => {
    let filtered = [...users]
    
    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        u => u.name.toLowerCase().includes(term) || 
             u.email.toLowerCase().includes(term)
      )
    }
    
    // Apply role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(u => u.role === roleFilter)
    }
    
    setFilteredUsers(filtered)
  }

  const handleImpersonate = async (userId: string) => {
    const userToImpersonate = users.find(u => u.id === userId)
    if (!userToImpersonate) return
    
    setSelectedUser(userToImpersonate)
    setShowConfirmModal(true)
  }

  const confirmImpersonation = async () => {
    if (!selectedUser) return
    
    try {
      setImpersonating(true)
      
      // Call the impersonation API
      const response = await apiClient.impersonateUser(selectedUser.id)
      
      // Store original admin token for returning later
      localStorage.setItem('admin_token', localStorage.getItem('auth_token') || '')
      
      // Set the impersonation token
      apiClient.setToken(response.data.token)
      
      // Store impersonation info
      localStorage.setItem('impersonating', 'true')
      localStorage.setItem('impersonated_user_id', selectedUser.id)
      localStorage.setItem('impersonated_user_name', selectedUser.name)
      localStorage.setItem('impersonated_user_role', selectedUser.role)
      
      // Redirect based on role
      let redirectPath = '/'
      switch (selectedUser.role) {
        case 'patient':
          redirectPath = '/patient/cases'
          break
        case 'doctor':
          redirectPath = '/doctor/cases'
          break
        case 'agent':
          redirectPath = '/agent/assignments'
          break
      }
      
      // Close modal and navigate
      setShowConfirmModal(false)
      navigate(redirectPath)
    } catch (err: any) {
      setError(err.message || 'Failed to impersonate user')
      setImpersonating(false)
    }
  }

  // const _formatDate = (dateString: string) => {
  //   const date = new Date(dateString)
  //   return new Intl.DateTimeFormat('en-US', {
  //     year: 'numeric',
  //     month: 'short',
  //     day: 'numeric'
  //   }).format(date)
  // }

  const getRoleBadgeClass = (role: string) => {
    switch (role) {
      case 'patient':
        return 'bg-blue-100 text-blue-800'
      case 'doctor':
        return 'bg-green-100 text-green-800'
      case 'agent':
        return 'bg-purple-100 text-purple-800'
      case 'admin':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">User Impersonation</h1>
        <p className="mt-2 text-gray-600">
          Impersonate users to troubleshoot issues or provide support. All actions taken while impersonating will be logged.
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <span className="text-red-800">{error}</span>
          <button 
            onClick={() => setError(null)}
            className="ml-auto text-red-500 hover:text-red-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search users by name or email"
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="sm:w-48">
          <select
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
          >
            <option value="all">All Roles</option>
            <option value="patient">Patients</option>
            <option value="doctor">Doctors</option>
            <option value="agent">Agents</option>
          </select>
        </div>
      </div>

      {/* User List */}
      <div className="bg-white shadow overflow-hidden rounded-md">
        {isLoading ? (
          <div className="p-6 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <p className="mt-2 text-gray-600">Loading users...</p>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="p-6 text-center">
            <p className="text-gray-600">No users found matching your criteria.</p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {filteredUsers.map((user) => (
              <li key={user.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                      {user.profileImage ? (
                        <img
                          src={apiClient.getProfilePhotoUrl(user.id)}
                          alt={user.name}
                          className="h-10 w-10 rounded-full"
                        />
                      ) : (
                        <span className="text-gray-500 text-lg font-medium">
                          {user.name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeClass(user.role)}`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                    <button
                      onClick={() => handleImpersonate(user.id)}
                      className="ml-4 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Impersonate
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Confirmation Modal */}
      {showConfirmModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Confirm Impersonation</h3>
                <button
                  onClick={() => setShowConfirmModal(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="mb-6">
                <p className="text-gray-600">
                  You are about to impersonate:
                </p>
                <div className="mt-4 flex items-center p-4 bg-gray-50 rounded-md">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                    {selectedUser.profileImage ? (
                      <img
                        src={apiClient.getProfilePhotoUrl(selectedUser.id)}
                        alt={selectedUser.name}
                        className="h-10 w-10 rounded-full"
                      />
                    ) : (
                      <span className="text-gray-500 text-lg font-medium">
                        {selectedUser.name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">{selectedUser.name}</div>
                    <div className="text-sm text-gray-500">{selectedUser.email}</div>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeClass(selectedUser.role)}`}>
                        {selectedUser.role.charAt(0).toUpperCase() + selectedUser.role.slice(1)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-sm text-gray-500">
                    <UserCog className="h-4 w-4 inline mr-1" />
                    All actions taken while impersonating will be logged and attributed to your admin account.
                  </p>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowConfirmModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmImpersonation}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 flex items-center"
                  disabled={impersonating}
                >
                  {impersonating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Impersonating...
                    </>
                  ) : (
                    <>
                      <ArrowRight className="h-4 w-4 mr-1" />
                      Start Impersonation
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
