import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '@/services/api';
import { PlusCircle, Trash2, Edit, Eye, List, LayoutGrid } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

const LegalCompliance: React.FC = () => {
  const [templates, setTemplates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        
        const response = await apiClient.getLegalTemplates();
        
        // Handle multiple response formats
        let templatesData: any[] = [];
        
        if (response && response.templates) {
          // Direct response format: { success: true, templates: [...] }
          templatesData = response.templates;
        } else if (response && (response as any).data && (response as any).data.templates) {
          // Nested response format: { data: { templates: [...] } }
          templatesData = (response as any).data.templates;
        } else if (Array.isArray(response)) {
          // Direct array response
          templatesData = response;
        } else {
          templatesData = [];
        }
        
        setTemplates(templatesData);
      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error fetching consent form templates:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchTemplates();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this consent form template?')) {
      try {
        await apiClient.deleteConsentTemplate(id.toString());
        
        // Refresh the templates list after deletion
        const response = await apiClient.getLegalTemplates();
        
        // Handle multiple response formats
        let templatesData: any[] = [];
        
        if (response && response.templates) {
          // Direct response format: { success: true, templates: [...] }
          templatesData = response.templates;
        } else if (response && (response as any).data && (response as any).data.templates) {
          // Nested response format: { data: { templates: [...] } }
          templatesData = (response as any).data.templates;
        } else if (Array.isArray(response)) {
          // Direct array response
          templatesData = response;
        } else {
          templatesData = [];
        }
        
        setTemplates(templatesData);
      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error deleting template:', err);
        alert('Failed to delete template. Please try again.');
      }
    }
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Legal & Compliance Documents</h1>
        <div className="flex items-center space-x-3">
          {/* View Toggle */}
          <div className="flex items-center border border-gray-300 rounded-md">
            <button
              onClick={() => navigate('/admin/legal-compliance')}
              className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-l-md bg-white text-gray-700 hover:bg-gray-50"
            >
              <List className="h-4 w-4 mr-2" />
              List
            </button>
            <button
              className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-r-md bg-blue-600 text-white"
            >
              <LayoutGrid className="h-4 w-4 mr-2" />
              Cards
            </button>
          </div>
          
          <button 
            onClick={() => navigate('/admin/legal-compliance/new')}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <PlusCircle size={16} />
            Create New Document
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : templates.length === 0 ? (
        <div className="border rounded-lg shadow-sm">
          <div className="pt-6 p-6 text-center">
            <p className="text-gray-500">No legal documents found.</p>
            <button 
              onClick={() => navigate('/admin/legal-compliance/new')}
              className="mt-4 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Create your first document
            </button>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {templates.map((template: any) => (
            <div key={template.id} className="border rounded-lg shadow-sm overflow-hidden">
              <div className="p-4 border-b pb-2">
                <div className="flex justify-between items-start">
                  <h3 className="text-lg font-semibold">{template.title}</h3>
                  <span className={`text-xs px-2 py-1 rounded-full ${template.isActive ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                    {template.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
                <p className="text-sm text-gray-500">
                  Last updated {formatDistanceToNow(new Date(template.updatedAt), { addSuffix: true })}
                </p>
              </div>
              <div className="p-4">
                <p className="text-sm mb-4 line-clamp-2">
                  {template.description || "No description provided."}
                </p>
                <div className="flex justify-between mt-4">
                  <div className="flex gap-2">
                    <button 
                      onClick={() => navigate(`/admin/legal-compliance/view/${template.id}`)}
                      className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      <Eye size={14} />
                      View
                    </button>
                    <button 
                      onClick={() => navigate(`/admin/legal-compliance/edit/${template.id}`)}
                      className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      <Edit size={14} />
                      Edit
                    </button>
                  </div>
                  <button 
                    onClick={() => handleDelete(template.id)}
                    className="flex items-center gap-1 px-3 py-1 text-sm bg-red-50 text-red-700 border border-red-200 rounded-md hover:bg-red-100"
                  >
                    <Trash2 size={14} />
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LegalCompliance;
