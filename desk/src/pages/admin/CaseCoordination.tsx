import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { apiClient } from '@/services/api'
import DoctorAssignmentModal from '@/components/shared/DoctorAssignmentModal'
import {
  Refresh<PERSON><PERSON>,
  Eye,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  X,
  Clock
} from 'lucide-react'

// Types
interface Doctor {
  id: string
  firstName: string
  lastName: string
  email: string
  profile?: {
    phoneNumber?: string
    bio?: string
    profilePictureUrl?: string
  }
}

interface Case {
  id: string
  title: string
  description: string
  patientId: string
  status: string
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent'
  specialtyRequired?: string
  submittedAt: string
  createdAt: string
  updatedAt: string
}

// Urgency configurations for consistent UI
const urgencyConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-600' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-600' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-600' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-600' }
}

// Note: Using existing ApiClient methods:
// - getDoctors() already exists
// - assignCase() already exists for doctor assignment

export default function CaseCoordination() {
  const navigate = useNavigate()
  
  // State management
  const [cases, setCases] = useState<Case[]>([])
  const [doctors, setDoctors] = useState<Doctor[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Assignment modal state
  const [assignModalOpen, setAssignModalOpen] = useState(false)
  const [selectedCase, setSelectedCase] = useState<Case | null>(null)
  
  // Success/error messages
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Load submitted cases
  const loadCases = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getCases({ status: 'submitted' })
      // Handle different response formats
      const casesData = response?.data || response || []
      setCases(Array.isArray(casesData) ? casesData : [])
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error loading cases:', err)
      setError('Failed to load cases')
    } finally {
      setLoading(false)
    }
  }

  // Load doctors
  const loadDoctors = async () => {
    try {
      const response = await apiClient.getDoctors()
      // Handle different response formats - backend returns { doctors: [...], count: number }
      const doctorsData = (response as any)?.doctors || (response as any)?.data || response || []
      setDoctors(Array.isArray(doctorsData) ? doctorsData : [])
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error loading doctors:', err)
    }
  }

  // Initial data load
  useEffect(() => {
    loadCases()
    loadDoctors()
  }, [])

  // Auto-clear messages after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [successMessage])

  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => setErrorMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [errorMessage])

  // Event handlers
  const handleOpenAssignModal = (caseItem: Case) => {
    setSelectedCase(caseItem)
    setAssignModalOpen(true)
  }

  const handleCloseAssignModal = () => {
    setAssignModalOpen(false)
    setSelectedCase(null)
  }

  const handleAssignmentChange = async () => {
    // Reload cases to reflect the assignment
    await loadCases()
    setSuccessMessage(`Doctor assignment updated successfully for case "${selectedCase?.title}"`)
  }

  const handleViewCase = (caseId: string) => {
    navigate(`/cases/${caseId}`)
  }

  const handleRefresh = () => {
    loadCases()
    loadDoctors()
  }

  // Render loading state
  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-lg text-gray-600">Loading cases...</span>
        </div>
      </div>
    )
  }

  // Render error state
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={handleRefresh}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Case Coordination</h1>
          <p className="text-gray-600 mt-1">Assign doctors to submitted cases</p>
        </div>
        <button
          onClick={handleRefresh}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="mb-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-green-800">{successMessage}</span>
            </div>
            <button
              onClick={() => setSuccessMessage(null)}
              className="text-green-600 hover:text-green-800"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              <span className="text-red-800">{errorMessage}</span>
            </div>
            <button
              onClick={() => setErrorMessage(null)}
              className="text-red-600 hover:text-red-800"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Cases Table */}
      {cases.length === 0 ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No submitted cases</h3>
          <p className="text-gray-600">There are currently no cases waiting for doctor assignment.</p>
        </div>
      ) : (
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Case
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Urgency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Specialty
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Submitted
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {cases.map((caseItem) => (
                <tr key={caseItem.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{caseItem.title}</div>
                      <div className="text-sm text-gray-500 mt-1 max-w-xs truncate">
                        {caseItem.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${urgencyConfig[caseItem.urgencyLevel].color}`}>
                      {urgencyConfig[caseItem.urgencyLevel].label}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {caseItem.specialtyRequired || 'General'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {new Date(caseItem.submittedAt || caseItem.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleViewCase(caseItem.id)}
                        className="text-blue-600 hover:text-blue-900 flex items-center"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </button>
                      <button
                        onClick={() => handleOpenAssignModal(caseItem)}
                        className="text-green-600 hover:text-green-900 flex items-center"
                      >
                        <UserCheck className="h-4 w-4 mr-1" />
                        Assign
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Doctor Assignment Modal */}
      {selectedCase && (
        <DoctorAssignmentModal
          caseId={selectedCase.id}
          isOpen={assignModalOpen}
          onClose={handleCloseAssignModal}
          onAssignmentChange={handleAssignmentChange}
          currentAssignments={[]} // For new case assignments, start with empty array
        />
      )}
    </div>
  )
}
