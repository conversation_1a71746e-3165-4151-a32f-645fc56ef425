import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Search,
  Award,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Calendar,
  User,
  Building,
  Filter,
  Download
} from 'lucide-react'

interface Credential {
  id: string
  doctorId: string
  doctor?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  verifiedByUser?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  credentialType: string
  credentialNumber: string
  issuingAuthority: string
  issuedDate: string
  expirationDate?: string
  status: 'pending' | 'verified' | 'revoked' | 'expired'
  verificationMethod?: string
  verifiedAt?: string
  verifiedBy?: string
  rejectionReason?: string
  notes?: string
  createdAt: string
  updatedAt: string
  documents?: Array<{
    id: string
    title: string
    fileName: string
    fileSize: number
    uploadedAt: string
  }>
}

const statusConfig = {
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  verified: { label: 'Verified', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  revoked: { label: 'Rejected', color: 'bg-red-100 text-red-800', icon: XCircle },
  expired: { label: 'Expired', color: 'bg-gray-100 text-gray-800', icon: AlertTriangle }
}

const credentialTypeConfig = {
  medical_license: { label: 'Medical License', icon: Award },
  board_certification: { label: 'Board Certification', icon: Award },
  fellowship: { label: 'Fellowship', icon: Award },
  residency: { label: 'Residency', icon: Award },
  medical_degree: { label: 'Medical Degree', icon: Award },
  continuing_education: { label: 'Continuing Education', icon: Award },
  other: { label: 'Other', icon: FileText }
}

export function CredentialManagement() {
  const { user: _user } = useAuth()
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilters, setStatusFilters] = useState<string[]>(['pending'])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCredential, setSelectedCredential] = useState<Credential | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [showVerifyModal, setShowVerifyModal] = useState(false)
  const [showRejectModal, setShowRejectModal] = useState(false)
  const [verificationNotes, setVerificationNotes] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  // Load credentials from API
  useEffect(() => {
    const loadCredentials = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await apiClient.getAdminCredentials({
          status: statusFilters.length > 0 ? statusFilters.join(',') : undefined,
          search: searchTerm || undefined
        })
        
        // Handle response format - API returns { credentials: [...], pagination: {...} }
        let credentialData = []
        if (response && response.credentials && Array.isArray(response.credentials)) {
          credentialData = response.credentials
        } else if (response && response.data && Array.isArray(response.data)) {
          credentialData = response.data
        } else if (Array.isArray(response)) {
          credentialData = response
        }
        
        setCredentials(credentialData)
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load credentials:', error)
        setError('Failed to load credentials. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    loadCredentials()
  }, [statusFilters, searchTerm])

  const filteredCredentials = credentials.filter(credential => {
    if (!credential) return false
    
    const doctorName = credential.doctor ? `${credential.doctor.firstName} ${credential.doctor.lastName}`.toLowerCase() : ''
    const doctorEmail = credential.doctor?.email?.toLowerCase() || ''
    const credentialNumber = credential.credentialNumber?.toLowerCase() || ''
    const issuingAuthority = credential.issuingAuthority?.toLowerCase() || ''
    const searchTermLower = searchTerm.toLowerCase()
    
    const matchesSearch = doctorName.includes(searchTermLower) ||
                         doctorEmail.includes(searchTermLower) ||
                         credentialNumber.includes(searchTermLower) ||
                         issuingAuthority.includes(searchTermLower)
    
    const matchesStatus = statusFilters.length === 0 || statusFilters.includes(credential.status)
    
    return matchesSearch && matchesStatus
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const handleVerifyCredential = async () => {
    if (!selectedCredential) return
    
    setIsProcessing(true)
    try {
      await apiClient.verifyCredential(selectedCredential.id, verificationNotes)
      
      // Update local state
      setCredentials(prev => prev.map(c => 
        c.id === selectedCredential.id 
          ? { ...c, status: 'verified' as const, verifiedAt: new Date().toISOString(), notes: verificationNotes }
          : c
      ))
      
      setShowVerifyModal(false)
      setVerificationNotes('')
      setSelectedCredential(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error verifying credential:', error)
      setError('Failed to verify credential. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRejectCredential = async () => {
    if (!selectedCredential || !rejectionReason.trim()) return
    
    setIsProcessing(true)
    try {
      await apiClient.rejectCredential(selectedCredential.id, rejectionReason)
      
      // Update local state
      setCredentials(prev => prev.map(c =>
        c.id === selectedCredential.id
          ? { ...c, status: 'revoked' as const, rejectionReason }
          : c
      ))
      
      setShowRejectModal(false)
      setRejectionReason('')
      setSelectedCredential(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error rejecting credential:', error)
      setError('Failed to reject credential. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleViewDetails = (credential: Credential) => {
    setSelectedCredential(credential)
    setShowDetailModal(true)
  }

  const handleDownloadDocument = async (credentialId: string, documentId: string, fileName: string) => {
    try {
      const blob = await apiClient.streamDocument(documentId)
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error downloading document:', error)
      setError('Failed to download document. Please try again.')
    }
  }

  const stats = {
    total: credentials.length,
    pending: credentials.filter(c => c.status === 'pending').length,
    verified: credentials.filter(c => c.status === 'verified').length,
    rejected: credentials.filter(c => c.status === 'revoked').length,
    expired: credentials.filter(c => c.status === 'expired').length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Credential Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Review and approve doctor credentials and certifications
          </p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Award className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Credentials</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.pending}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Verified</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.verified}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Rejected</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.rejected}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Expired</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.expired}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search credentials..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          
          {/* Status Filter Buttons */}
          <div className="flex flex-col space-y-2">
            <label className="text-sm font-medium text-gray-700 lg:sr-only">Filter by Status:</label>
            <div className="flex flex-wrap gap-2 justify-end">
              {Object.entries(statusConfig).map(([status, config]) => {
                const isSelected = statusFilters.includes(status)
                const StatusIcon = config.icon
                
                return (
                  <button
                    key={status}
                    onClick={() => {
                      if (isSelected) {
                        setStatusFilters(prev => prev.filter(s => s !== status))
                      } else {
                        setStatusFilters(prev => [...prev, status])
                      }
                    }}
                    className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isSelected
                        ? `${config.color} ring-2 ring-offset-1 ring-gray-400`
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <StatusIcon className="h-4 w-4 mr-1" />
                    {config.label}
                  </button>
                )
              })}
            </div>
            {statusFilters.length > 0 && (
              <button
                onClick={() => setStatusFilters([])}
                className="text-sm text-gray-500 hover:text-gray-700 self-end"
              >
                Clear all filters
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Credentials Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
              <p className="mt-4 text-gray-500">Loading credentials...</p>
            </div>
          ) : filteredCredentials.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-400 mb-2">
                <Award className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-700">No credentials found</p>
              <p className="text-gray-500 text-sm mt-1">
                {searchTerm || statusFilters.length > 0 ?
                  'Try adjusting your filters' : 'No credentials have been submitted yet'}
              </p>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Doctor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Credential
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dates
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCredentials.map((credential) => {
                  const StatusIcon = statusConfig[credential.status].icon
                  const TypeIcon = credentialTypeConfig[credential.credentialType as keyof typeof credentialTypeConfig]?.icon || FileText
                  
                  return (
                    <tr key={credential.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <User className="h-5 w-5 text-gray-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {credential.doctor ? `${credential.doctor.firstName} ${credential.doctor.lastName}` : 'Unknown Doctor'}
                            </div>
                            <div className="text-sm text-gray-500">{credential.doctor?.email || 'No email'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <TypeIcon className="h-5 w-5 text-gray-400 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {credentialTypeConfig[credential.credentialType as keyof typeof credentialTypeConfig]?.label || credential.credentialType}
                            </div>
                            <div className="text-sm text-gray-500">{credential.credentialNumber}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <Building className="h-3 w-3 mr-1" />
                              {credential.issuingAuthority}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[credential.status].color}`}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusConfig[credential.status].label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          <div>
                            <div>Issued: {formatDate(credential.issuedDate)}</div>
                            {credential.expirationDate && (
                              <div>Expires: {formatDate(credential.expirationDate)}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewDetails(credential)}
                            className="text-primary-600 hover:text-primary-900"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          
                          {credential.status === 'pending' && (
                            <>
                              <button
                                onClick={() => {
                                  setSelectedCredential(credential)
                                  setShowVerifyModal(true)
                                }}
                                className="text-green-600 hover:text-green-900"
                                title="Verify Credential"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </button>
                              
                              <button
                                onClick={() => {
                                  setSelectedCredential(credential)
                                  setShowRejectModal(true)
                                }}
                                className="text-red-600 hover:text-red-900"
                                title="Reject Credential"
                              >
                                <XCircle className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedCredential && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Credential Details</h3>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircle className="h-6 w-6" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Doctor</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedCredential.doctor ? `${selectedCredential.doctor.firstName} ${selectedCredential.doctor.lastName}` : 'Unknown Doctor'}
                    </p>
                    <p className="text-sm text-gray-500">{selectedCredential.doctor?.email || 'No email'}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Credential Type</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {credentialTypeConfig[selectedCredential.credentialType as keyof typeof credentialTypeConfig]?.label || selectedCredential.credentialType}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Credential Number</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedCredential.credentialNumber}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Issuing Authority</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedCredential.issuingAuthority}</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[selectedCredential.status].color}`}>
                      {statusConfig[selectedCredential.status].label}
                    </span>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Issued Date</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(selectedCredential.issuedDate)}</p>
                  </div>
                  
                  {selectedCredential.expirationDate && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Expiration Date</label>
                      <p className="mt-1 text-sm text-gray-900">{formatDate(selectedCredential.expirationDate)}</p>
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Submitted</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(selectedCredential.createdAt)}</p>
                  </div>
                </div>
              </div>
              
              {selectedCredential.notes && (
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700">Notes</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedCredential.notes}</p>
                </div>
              )}
              
              {selectedCredential.rejectionReason && (
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700">Rejection Reason</label>
                  <p className="mt-1 text-sm text-red-600">{selectedCredential.rejectionReason}</p>
                </div>
              )}
              
              {selectedCredential.documents && selectedCredential.documents.length > 0 && (
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Supporting Documents</label>
                  <div className="space-y-2">
                    {selectedCredential.documents.map((doc) => (
                      <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                        <div className="flex items-center">
                          <FileText className="h-5 w-5 text-gray-400 mr-2" />
                          <div>
                            <p className="text-sm font-medium text-gray-900">{doc.title}</p>
                            <p className="text-sm text-gray-500">{doc.fileName} • {(doc.fileSize / 1024).toFixed(1)} KB</p>
                          </div>
                        </div>
                        <button
                          onClick={() => handleDownloadDocument(selectedCredential.id, doc.id, doc.fileName)}
                          className="text-primary-600 hover:text-primary-900"
                          title="Download Document"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Verify Modal */}
      {showVerifyModal && selectedCredential && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Verify Credential</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to verify this credential for {selectedCredential.doctor ? `${selectedCredential.doctor.firstName} ${selectedCredential.doctor.lastName}` : 'this doctor'}?
                </p>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Verification Notes (Optional)</label>
                  <textarea
                    value={verificationNotes}
                    onChange={(e) => setVerificationNotes(e.target.value)}
                    rows={3}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Add any notes about the verification..."
                  />
                </div>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={handleVerifyCredential}
                    disabled={isProcessing}
                    className="flex-1 px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-300 disabled:opacity-50"
                  >
                    {isProcessing ? 'Verifying...' : 'Verify'}
                  </button>
                  <button
                    onClick={() => setShowVerifyModal(false)}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && selectedCredential && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Reject Credential</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Please provide a reason for rejecting this credential for {selectedCredential.doctor ? `${selectedCredential.doctor.firstName} ${selectedCredential.doctor.lastName}` : 'this doctor'}.
                </p>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rejection Reason *</label>
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    rows={3}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Explain why this credential is being rejected..."
                    required
                  />
                </div>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={handleRejectCredential}
                    disabled={isProcessing || !rejectionReason.trim()}
                    className="flex-1 px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                  >
                    {isProcessing ? 'Rejecting...' : 'Reject'}
                  </button>
                  <button
                    onClick={() => setShowRejectModal(false)}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}