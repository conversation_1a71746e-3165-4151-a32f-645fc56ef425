import React from 'react';
import { useNavigate } from 'react-router-dom';
import LegalDocumentView from '@/components/shared/LegalDocumentView';

const LegalComplianceView: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/admin/legal-compliance');
  };

  return (
    <LegalDocumentView
      showEditButton={true}
      onBack={handleBack}
      embedded={false}
    />
  );
};

export default LegalComplianceView;
