import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { apiClient } from '@/services/api';
import { AlertCircle, Save, ArrowLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import MarkdownEditor from '@/components/ui/markdown-editor';
import TextViewer from '@/components/ui/text-viewer';

interface FormValues {
  title: string;
  description: string;
  content: string;
  notes: string;
}

const LegalComplianceEdit: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;
  
  const [activeTab, setActiveTab] = useState<string>("edit");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<FormValues>({
    defaultValues: {
      title: '',
      description: '',
      content: '',
      notes: isEditMode ? 'Updated version' : 'Initial version',
    }
  });
  
  const content = watch('content');
  
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isCreatingVersion, setIsCreatingVersion] = useState(false);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(isEditMode);
  const [templateData, setTemplateData] = useState<any>(null);
  
  // Fetch template data if in edit mode
  useEffect(() => {
    const fetchTemplate = async () => {
      if (!isEditMode || !id) return;
      
      try {
        setIsLoadingTemplate(true);
        const response = await apiClient.getLegalTemplate(id);
        setTemplateData(response);
      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error fetching template:', err);
        setErrorMessage('Failed to load template data. Please try again.');
      } finally {
        setIsLoadingTemplate(false);
      }
    };
    
    fetchTemplate();
  }, [isEditMode, id]);
  
  useEffect(() => {
    if (isEditMode && templateData?.data?.template) {
      const template = templateData.data.template;
      setValue('title', template.title);
      setValue('description', template.description || '');
      
      // If there are versions, set the content to the latest version
      if (template.versions && template.versions.length > 0) {
        const latestVersion = template.versions[0]; // Versions are ordered by desc
        setValue('content', latestVersion.content);
      }
    }
  }, [isEditMode, templateData, setValue]);
  
  const onSubmit = async (data: FormValues) => {
    setErrorMessage(null);
    
    try {
      if (isEditMode) {
        // If editing, we update the template metadata and create a new version
        setIsUpdating(true);
        try {
          // Update template metadata
          await apiClient.updateLegalTemplate(id!, {
            title: data.title,
            description: data.description,
          });
          
          // After updating the template, create a new version
          setIsCreatingVersion(true);
          try {
            await apiClient.createLegalTemplateVersion(id!, {
              content: data.content,
              notes: data.notes,
            });
            
            navigate('/admin/legal-compliance');
          } catch (error) {
            // TODO: Replace with proper error reporting
  console.error('Error creating version:', error);
            setErrorMessage('Error creating new version: ' + (error instanceof Error ? error.message : String(error)));
          } finally {
            setIsCreatingVersion(false);
          }
        } catch (error) {
          // TODO: Replace with proper error reporting
  console.error('Error updating template:', error);
          setErrorMessage('Error updating template: ' + (error instanceof Error ? error.message : String(error)));
        } finally {
          setIsUpdating(false);
        }
      } else {
        // If creating new, we create a template with an initial version
        setIsCreating(true);
        try {
          await apiClient.createLegalTemplate({
            title: data.title,
            description: data.description,
            content: data.content,
            notes: data.notes,
          });
          
          navigate('/admin/legal-compliance');
        } catch (error) {
          // TODO: Replace with proper error reporting
  console.error('Error creating template:', error);
          setErrorMessage('Error creating template: ' + (error instanceof Error ? error.message : String(error)));
        } finally {
          setIsCreating(false);
        }
      }
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error in form submission:', err);
      setErrorMessage('An unexpected error occurred. Please try again.');
    }
  };
  
  if (isEditMode && isLoadingTemplate) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  return (
    <div className="p-4">
      <div className="flex items-center gap-2 mb-6">
        <button 
          onClick={() => navigate('/admin/legal-compliance')}
          className="flex items-center gap-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
        >
          <ArrowLeft size={16} />
          Back to Legal & Compliance
        </button>
        <h1 className="text-2xl font-bold">
          {isEditMode ? 'Edit Legal Document' : 'Create Legal Document'}
        </h1>
      </div>
      
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            <h3 className="font-medium">Error</h3>
          </div>
          <p className="mt-1 text-sm">
            {errorMessage}
          </p>
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold">Template Information</h2>
            </div>
            <div className="p-4 space-y-4">
              <div className="space-y-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">Title</label>
                <input 
                  id="title" 
                  {...register('title', { required: 'Title is required' })} 
                  placeholder="Enter consent form title"
                  className={`w-full px-3 py-2 border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
                {errors.title && (
                  <p className="text-sm text-red-500">{errors.title.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
                <textarea 
                  id="description" 
                  {...register('description')} 
                  placeholder="Enter a description for this consent form"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold">Content</h2>
            </div>
            <div className="p-4">
              <div className="space-y-4">
                <div className="flex space-x-1 border-b border-gray-200 mb-4">
                  <button 
                    type="button"
                    onClick={() => setActiveTab('edit')} 
                    className={`px-4 py-2 ${activeTab === 'edit' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                  >
                    Edit
                  </button>
                  <button 
                    type="button"
                    onClick={() => setActiveTab('preview')} 
                    className={`px-4 py-2 ${activeTab === 'preview' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                  >
                    Preview
                  </button>
                  <button 
                    type="button"
                    onClick={() => setActiveTab('split')} 
                    className={`px-4 py-2 ${activeTab === 'split' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                  >
                    Split View
                  </button>
                </div>
                
                {activeTab === 'edit' && (
                  <div className="space-y-4">
                    <MarkdownEditor
                      value={content}
                      onChange={(value) => setValue('content', value || '')}
                      height={500}
                      preview="edit"
                    />
                  </div>
                )}
                
                {activeTab === 'preview' && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6">
                      <TextViewer content={content} />
                    </div>
                  </div>
                )}
                
                {activeTab === 'split' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <MarkdownEditor
                        value={content}
                        onChange={(value) => setValue('content', value || '')}
                        height={500}
                        preview="edit"
                      />
                    </div>
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                      <div className="p-6">
                        <TextViewer content={content} />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {isEditMode && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
              <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold">Version Notes</h2>
              </div>
              <div className="p-4">
                <div className="space-y-2">
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes for this version</label>
                  <textarea 
                    id="notes" 
                    {...register('notes')} 
                    placeholder="Describe what changed in this version"
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          )}
          
          <div className="flex justify-end">
            <button 
              type="submit" 
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
              disabled={isCreating || isUpdating || isCreatingVersion}
            >
              <Save size={16} />
              {isEditMode ? 'Save Changes' : 'Create Template'}
              {(isCreating || isUpdating || isCreatingVersion) && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default LegalComplianceEdit;
