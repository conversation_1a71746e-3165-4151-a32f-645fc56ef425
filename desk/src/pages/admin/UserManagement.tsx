import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import { useNavigate } from 'react-router-dom'
import {
  Search,
  UserPlus,
  Edit,
  Trash2,
  Shield,
  Mail,
  Phone,
  UserCheck,
  UserX,
  Users,
  UserCog,
  AlertTriangle,
  ShieldCheck,
  ShieldX,
  CheckCircle
} from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  phone?: string
  role: 'patient' | 'doctor' | 'agent' | 'admin'
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  createdAt: string
  lastLogin?: string
  // verificationStatus removed - field does not exist in database
  casesCount?: number
  specialization?: string
  licenseNumber?: string
  department?: string
}

// No mock data - using real API data

const roleConfig = {
  patient: { label: 'Patient', color: 'bg-blue-100 text-blue-800', icon: UserCheck },
  doctor: { label: 'Doctor', color: 'bg-green-100 text-green-800', icon: <PERSON><PERSON><PERSON><PERSON> },
  agent: { label: 'Agent', color: 'bg-purple-100 text-purple-800', icon: Shield },
  admin: { label: 'Admin', color: 'bg-red-100 text-red-800', icon: ShieldX }
}

const statusConfig = {
  active: { label: 'Active', color: 'bg-green-100 text-green-800' },
  inactive: { label: 'Inactive', color: 'bg-gray-100 text-gray-800' },
  suspended: { label: 'Suspended', color: 'bg-red-100 text-red-800' },
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800' }
}

// verificationConfig removed - verification functionality has been removed
// as the verification status field does not exist in the users table

export function UserManagement() {
  const { user: _user } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()

  // Load users from API
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Get users from API
        const response = await apiClient.getUsers()
        
        // Check if we have valid user data
        let apiUserData = [];
        
        if (response && (response as any).data && Array.isArray((response as any).data)) {
          apiUserData = (response as any).data
        } else if (Array.isArray(response)) {
          apiUserData = response
        }
        
        // Transform API user data to match expected User interface
        const mappedUsers: User[] = apiUserData.map((apiUser: any) => ({
          id: apiUser.id,
          name: apiUser.firstName && apiUser.lastName ? `${apiUser.firstName} ${apiUser.lastName}` : apiUser.email,
          email: apiUser.email,
          phone: apiUser.phone || undefined,
          role: apiUser.role as User['role'], // Backend returns a single role string, not an array
          status: apiUser.isActive ? 'active' : 'inactive',
          createdAt: apiUser.createdAt,
          lastLogin: apiUser.lastLoginAt || undefined, // Using lastLoginAt which is the correct field name
          // verificationStatus removed - field does not exist in database
          casesCount: apiUser.casesCount || 0,
          specialization: apiUser.specialization || undefined,
          licenseNumber: apiUser.licenseNumber || undefined,
          department: apiUser.department || undefined,
        }))
        
        setUsers(mappedUsers)
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load users:', error)
        setError('Failed to load users. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    loadUsers()
  }, []) // Empty dependency array to run only once on mount
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [_showUserModal, setShowUserModal] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Users state is populated from API

  // Add defensive checks to handle potentially missing properties
  const filteredUsers = users.filter(user => {
    if (!user) return false;
    
    // Check if name and email exist before using toLowerCase()
    const userName = user.name?.toLowerCase() || '';
    const userEmail = user.email?.toLowerCase() || '';
    const searchTermLower = searchTerm.toLowerCase();
    
    const matchesSearch = userName.includes(searchTermLower) || userEmail.includes(searchTermLower);
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleStatusChange = async (userId: string, newStatus: User['status']) => {
    setIsLoading(true)
    try {
      // API call to update user status

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setUsers(prev => prev.map(u => 
        u.id === userId ? { ...u, status: newStatus } : u
      ))
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error updating user status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteUser = async (userId: string) => {
    setIsLoading(true)
    try {
      // API call to delete user

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setUsers(prev => prev.filter(u => u.id !== userId))
      setShowDeleteConfirm(false)
      setSelectedUser(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error deleting user:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // handleVerificationChange removed - verification functionality has been removed
  // as the verification status field does not exist in the users table

  // Handle user impersonation
  const handleImpersonate = async (userId: string) => {
    try {
      setIsLoading(true)
      const userToImpersonate = users.find(u => u.id === userId)
      
      if (!userToImpersonate) {
        // TODO: Replace with proper error reporting
  console.error('User not found')
        setIsLoading(false)
        return
      }
      
      // Call the impersonation API
      const response = await apiClient.impersonateUser(userId)
      
      if (!response || !response.token) {
        // TODO: Replace with proper error reporting
  console.error('Failed to impersonate user: Invalid response format')
        setIsLoading(false)
        return
      }
      
      // Store original admin token for returning later
      localStorage.setItem('admin_token', localStorage.getItem('auth_token') || '')
      
      // Set the impersonation token
      apiClient.setToken(response.token)
      
      // Store impersonation info
      localStorage.setItem('impersonating', 'true')
      localStorage.setItem('impersonated_user_id', userId)
      localStorage.setItem('impersonated_user_name', userToImpersonate.name)
      localStorage.setItem('impersonated_user_role', userToImpersonate.role)
      
      // Redirect based on role
      let redirectPath = '/'
      switch (userToImpersonate.role) {
        case 'patient':
          redirectPath = '/patient/cases'
          break
        case 'doctor':
          redirectPath = '/doctor/cases'
          break
        case 'agent':
          redirectPath = '/agent/assignments'
          break
      }
      
      // Store the token in localStorage first to ensure it's available
      localStorage.setItem('auth_token', response.token)
      
      // Use React Router navigation instead of full page reload
      navigate(redirectPath)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to impersonate user:', error)
      setIsLoading(false)
    }
  }

  const stats = {
    total: users.length,
    active: users.filter(u => u.status === 'active').length,
    pending: users.filter(u => u.status === 'pending').length,
    doctors: users.filter(u => u.role === 'doctor').length,
    patients: users.filter(u => u.role === 'patient').length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage platform users, roles, and permissions
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Users
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.active}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.pending}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShieldCheck className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Doctors</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.doctors}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Patients</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.patients}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
            >
              <option value="all">All Roles</option>
              <option value="patient">Patients</option>
              <option value="doctor">Doctors</option>
              <option value="agent">Agents</option>
              <option value="admin">Admins</option>
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
      </div>


      {/* Users Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
              <p className="mt-4 text-gray-500">Loading users...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <div className="text-red-500 mb-2">
                <AlertTriangle className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-700">{error}</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-400 mb-2">
                <Users className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-700">No users found</p>
              <p className="text-gray-500 text-sm mt-1">
                {searchTerm || roleFilter !== 'all' || statusFilter !== 'all' ? 
                  'Try adjusting your filters' : 'No users have been added yet'}
              </p>
            </div>
          ) : (
            <>
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    {/* Verification column removed - verification functionality has been removed */}
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Login
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.map((user) => {
                // Add defensive check for role config
                const roleConfigEntry = roleConfig[user.role as keyof typeof roleConfig]
                const RoleIcon = roleConfigEntry?.icon || UserCheck // Fallback icon
                // Verification icon removed - verification functionality has been removed
                
                return (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {user.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {user.email}
                          </div>
                          {user.phone && (
                            <div className="text-sm text-gray-500 flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {user.phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleConfigEntry?.color || 'bg-gray-100 text-gray-800'}`}>
                          <RoleIcon className="h-3 w-3 mr-1" />
                          {roleConfigEntry?.label || user.role || 'Unknown'}
                        </span>
                      </div>
                      {user.specialization && (
                        <div className="text-xs text-gray-500 mt-1">{user.specialization}</div>
                      )}
                      {user.department && (
                        <div className="text-xs text-gray-500 mt-1">{user.department}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[user.status].color}`}>
                        {statusConfig[user.status].label}
                      </span>
                    </td>
                    {/* Verification status cell removed - verification functionality has been removed */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.lastLogin ? formatDate(user.lastLogin) : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {user.status === 'active' ? (
                          <button
                            onClick={() => handleStatusChange(user.id, 'suspended')}
                            className="text-red-600 hover:text-red-900"
                            title="Suspend User"
                          >
                            <UserX className="h-4 w-4" />
                          </button>
                        ) : user.status === 'suspended' ? (
                          <button
                            onClick={() => handleStatusChange(user.id, 'active')}
                            className="text-green-600 hover:text-green-900"
                            title="Activate User"
                          >
                            <UserCheck className="h-4 w-4" />
                          </button>
                        ) : null}
                        
                        {/* Verification action buttons removed - verification functionality has been removed */}
                        
                        <button
                          onClick={() => {
                            setSelectedUser(user)
                            setShowUserModal(true)
                          }}
                          className="text-primary-600 hover:text-primary-900"
                          title="Edit User"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        
                        <button
                          onClick={() => {
                            setSelectedUser(user)
                            setShowDeleteConfirm(true)
                          }}
                          className="text-red-600 hover:text-red-900"
                          title="Delete User"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                        
                        {/* Impersonate User Button */}
                        {user.status === 'active' && (
                          <button
                            onClick={() => handleImpersonate(user.id)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Impersonate User"
                          >
                            <UserCog className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                )
              })}
              </tbody>
            </table>
            </>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete User</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete {selectedUser.name}? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleDeleteUser(selectedUser.id)}
                    disabled={isLoading}
                    className="flex-1 px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                  >
                    {isLoading ? 'Deleting...' : 'Delete'}
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
