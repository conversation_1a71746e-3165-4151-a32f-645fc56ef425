import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FileText, 
  Edit,
  Eye,
  Calendar,
  User,
  LayoutGrid,
  List,
  Plus,
  Search,
  ChevronRight
} from 'lucide-react';
import { apiClient } from '@/services/api';

interface LegalDocument {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  isActive: boolean;
  versions?: LegalDocumentVersion[];
}

interface LegalDocumentVersion {
  id: string;
  templateId: string;
  version: number;
  content: string;
  notes: string;
  createdAt: string;
  createdBy: string;
}

const LegalComplianceList: React.FC = () => {
  const navigate = useNavigate();
  const [documents, setDocuments] = useState<LegalDocument[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<LegalDocument | null>(null);
  const [selectedVersion, setSelectedVersion] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>('list');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterActive] = useState(true);

  // Mock data for now (will be replaced with real API)
  const mockDocuments: LegalDocument[] = [
    {
      id: '1',
      title: 'Continuia Patient Consent Form',
      description: 'Primary consent form for all Continuia patients',
      createdAt: '2025-07-26T10:00:00Z',
      updatedAt: '2025-07-26T10:00:00Z',
      createdBy: 'admin',
      isActive: true,
      versions: [
        {
          id: 'v1',
          templateId: '1',
          version: 1,
          content: '# Continuia Patient Consent Form\n\n**Applies to:** All patients receiving care through Continuia\n**Version 1.0 – July 2025**\n\n## 📋 Patient Information\n\n| Item | Details |\n|------|---------|\n| Full Name | [Patient Name] |\n| Date of Birth | [DOB] |\n| Medical Record Number | [MRN] |\n\n## 🏥 Consent for Treatment\n\nI hereby consent to and authorize the performance of medical care, treatment, and services by licensed physicians and healthcare providers through the Continuia platform.',
          notes: 'Initial version',
          createdAt: '2025-07-26T10:00:00Z',
          createdBy: 'admin'
        }
      ]
    },
    {
      id: '2',
      title: 'Continuia Patient Terms of Service (TOS)',
      description: 'Terms of service for Continuia platform usage',
      createdAt: '2025-07-26T09:30:00Z',
      updatedAt: '2025-07-26T09:30:00Z',
      createdBy: 'admin',
      isActive: true,
      versions: [
        {
          id: 'v2',
          templateId: '2',
          version: 1,
          content: '# Continuia Patient Terms of Service\n\n**Effective Date:** July 26, 2025\n**Version:** 1.0\n\n## 📱 Platform Usage\n\nBy accessing and using the Continuia healthcare platform, you agree to be bound by these Terms of Service.\n\n## 🔒 Privacy & Security\n\nWe are committed to protecting your personal health information in accordance with HIPAA regulations.',
          notes: 'Initial TOS version',
          createdAt: '2025-07-26T09:30:00Z',
          createdBy: 'admin'
        }
      ]
    },
    {
      id: '3',
      title: 'Agent Compliance Checklist',
      description: 'Compliance checklist for AI agent operations',
      createdAt: '2025-07-26T09:00:00Z',
      updatedAt: '2025-07-26T09:00:00Z',
      createdBy: 'admin',
      isActive: true,
      versions: [
        {
          id: 'v3',
          templateId: '3',
          version: 1,
          content: '# Continuia Agent Compliance Checklist\n\n**Applies to:** Every second opinion case before patient delivery\n**Version 1.0 – July 2025**\n\n## 📂 Case Metadata\n\n| Item | Status |\n|------|--------|\n| Unique Case ID assigned | ☐ |\n| Patient consent recorded (timestamped) | ☐ |\n| Assigned physician license verified and active | ☐ |\n| Physician location jurisdiction matches patient region | ☐ |\n\n## 🔐 Consent & Disclaimers\n\n| Item | Status |\n|------|--------|\n| Patient agreed to Terms of Service | ☐ |\n| Consent included acknowledgment of AI involvement | ☐ |\n| Patient presented with AI disclaimer | ☐ |\n| No AI-generated draft shared directly with patient | ☐ |',
          notes: 'Initial compliance checklist',
          createdAt: '2025-07-26T09:00:00Z',
          createdBy: 'admin'
        }
      ]
    }
  ];

  useEffect(() => {
    loadDocuments();
  }, []);

  // Function to load individual document content
  const loadDocumentContent = async (documentId: string) => {
    try {
      // Loading content
      const response = await apiClient.getLegalTemplate(documentId);
      if (response && response.data && response.data.template) {
        const template = response.data.template;
        setSelectedDocument(template);
        
        if (template.versions && template.versions.length > 0) {
          setSelectedVersion(template.versions[0]);
        }
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error loading document content:', error);
    } finally {
      // Content loading complete
    }
  };

  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Try to load real documents from API first
      try {
        const response = await apiClient.getLegalTemplates();
        if (response && response.templates && Array.isArray(response.templates)) {
          setDocuments(response.templates);
          
          // Auto-select first document
          if (response.templates.length > 0) {
            setSelectedDocument(response.templates[0]);
            if (response.templates[0].versions && response.templates[0].versions.length > 0) {
              setSelectedVersion(response.templates[0].versions[0]);
            }
          }
          return;
        }
      } catch (apiError) {
        console.warn('API failed, falling back to mock data:', apiError);
      }
      
      // Fallback to mock data if API fails
      setDocuments(mockDocuments);
      
      // Auto-select first document
      if (mockDocuments.length > 0) {
        setSelectedDocument(mockDocuments[0]);
        if (mockDocuments[0].versions && mockDocuments[0].versions.length > 0) {
          setSelectedVersion(mockDocuments[0].versions[0]);
        }
      }
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error loading documents:', err);
      setError('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentSelect = (document: LegalDocument) => {
    // Load the full document content via API
    loadDocumentContent(document.id);
  };

  const handleEdit = (documentId: string) => {
    navigate(`/admin/legal-compliance/edit/${documentId}`);
  };

  const handleView = (documentId: string) => {
    navigate(`/admin/legal-compliance/view/${documentId}`);
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = !filterActive || doc.isActive;
    return matchesSearch && matchesFilter;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderMarkdown = (content: string) => {
    // Simple markdown rendering for preview
    return content
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mb-3 flex items-center"><span class="mr-2">📂</span>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-medium mb-2">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n/g, '<br>');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="h-full flex bg-gray-50">
      {/* Left Panel - Document List */}
      <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Legal & Compliance</h2>
            <div className="flex items-center space-x-2">
              {/* View Toggle */}
              <div className="flex items-center border border-gray-300 rounded-md">
                <button
                  onClick={() => setViewMode('list')}
                  className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-l-md ${
                    viewMode === 'list'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <List className="h-3 w-3 mr-1" />
                  List
                </button>
                <button
                  onClick={() => {
                    setViewMode('cards');
                    navigate('/admin/legal-compliance/cards');
                  }}
                  className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-r-md ${
                    viewMode === 'cards'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <LayoutGrid className="h-3 w-3 mr-1" />
                  Cards
                </button>
              </div>
              
              <button
                onClick={() => navigate('/admin/legal-compliance/create')}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-3 w-3 mr-1" />
                New
              </button>
            </div>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Document List */}
        <div className="flex-1 overflow-y-auto">
          {error && (
            <div className="p-4 text-red-600 text-sm">{error}</div>
          )}
          
          {filteredDocuments.map((document) => (
            <div
              key={document.id}
              onClick={() => handleDocumentSelect(document)}
              className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedDocument?.id === document.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-2 flex-1">
                  <FileText className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {document.title}
                    </p>
                    <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                      {document.description}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                      <span className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(document.updatedAt)}
                      </span>
                      <span className="flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        {document.createdBy}
                      </span>
                    </div>
                  </div>
                </div>
                
                {selectedDocument?.id === document.id && (
                  <ChevronRight className="h-4 w-4 text-blue-500 flex-shrink-0" />
                )}
              </div>
              
              {/* Quick Actions */}
              <div className="flex items-center space-x-1 mt-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleView(document.id);
                  }}
                  className="inline-flex items-center px-2 py-1 text-xs text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                >
                  <Eye className="h-3 w-3 mr-1" />
                  View
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit(document.id);
                  }}
                  className="inline-flex items-center px-2 py-1 text-xs text-gray-600 hover:text-green-600 hover:bg-green-50 rounded"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </button>
              </div>
            </div>
          ))}
          
          {filteredDocuments.length === 0 && !error && (
            <div className="p-8 text-center text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-sm">No documents found</p>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - Document Preview */}
      <div className="flex-1 flex flex-col bg-white">
        {selectedDocument ? (
          <>
            {/* Document Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-start justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {selectedDocument.title}
                  </h1>
                  <p className="text-gray-600 mb-4">
                    {selectedDocument.description}
                  </p>
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <span className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Updated {formatDate(selectedDocument.updatedAt)}
                    </span>
                    <span className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      Created by {selectedDocument.createdBy}
                    </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedDocument.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedDocument.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleView(selectedDocument.id)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Full View
                  </button>
                  <button
                    onClick={() => handleEdit(selectedDocument.id)}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                </div>
              </div>
            </div>

            {/* Document Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {selectedVersion && (
                <div className="prose max-w-none">
                  <div 
                    dangerouslySetInnerHTML={{ 
                      __html: renderMarkdown(selectedVersion.content) 
                    }}
                    className="text-gray-900 leading-relaxed"
                  />
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">Select a document to preview</p>
              <p className="text-sm mt-2">Choose a document from the list to view its content</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LegalComplianceList;
