import React, { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import { useNavigate } from 'react-router-dom'
import {
  Search,
  UserPlus,
  Edit,
  Trash2,
  Shield,
  Mail,
  Phone,
  UserCheck,
  UserX,
  Users,
  UserCog,
  AlertTriangle,
  ShieldCheck,
  ShieldX,
  CheckCircle,
  Award,
  Eye,
  XCircle,
  Clock,
  FileText,
  Calendar,
  User,
  Building
} from 'lucide-react'

interface Specialization {
  assignment_id: string
  specialization_id: string
  specialization_name: string
  specialization_code: string
  category_name: string
  is_primary: boolean
  board_certified: boolean
  certification_details?: string
  years_experience?: number
}

interface Doctor {
  id: string
  name: string
  email: string
  phone?: string
  role: 'doctor'
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  createdAt: string
  lastLogin?: string
  casesCount?: number
  specialization?: string // Keep for backward compatibility
  specializations?: Specialization[] // New multiple specializations
  licenseNumber?: string
  department?: string
  credentials?: Credential[]
}

interface Credential {
  id: string
  doctorId: string
  credentialType: string
  credentialNumber: string
  issuingAuthority: string
  issuedDate: string
  expirationDate?: string
  status: 'pending' | 'verified' | 'revoked' | 'expired'
  verificationMethod?: string
  verifiedBy?: string
  verifiedAt?: string
  rejectionReason?: string
  notes?: string
  createdAt: string
  updatedAt: string
  documents?: Array<{
    id: string
    title: string
    fileName: string
    fileSize: number
    uploadedAt: string
  }>
}

const statusConfig = {
  active: { label: 'Active', color: 'bg-green-100 text-green-800' },
  inactive: { label: 'Inactive', color: 'bg-gray-100 text-gray-800' },
  suspended: { label: 'Suspended', color: 'bg-red-100 text-red-800' },
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800' }
}

const credentialStatusConfig = {
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  verified: { label: 'Verified', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  revoked: { label: 'Rejected', color: 'bg-red-100 text-red-800', icon: XCircle },
  expired: { label: 'Expired', color: 'bg-gray-100 text-gray-800', icon: AlertTriangle }
}

const credentialTypeConfig = {
  medical_license: { label: 'Medical License', icon: Award },
  board_certification: { label: 'Board Certification', icon: Award },
  fellowship: { label: 'Fellowship', icon: Award },
  residency: { label: 'Residency', icon: Award },
  medical_degree: { label: 'Medical Degree', icon: Award },
  continuing_education: { label: 'Continuing Education', icon: Award },
  other: { label: 'Other', icon: FileText }
}


export function DoctorManagement() {
  const { user: _user } = useAuth()
  const [doctors, setDoctors] = useState<Doctor[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showCredentialsModal, setShowCredentialsModal] = useState(false)
  const [selectedCredential, setSelectedCredential] = useState<Credential | null>(null)
  const [showVerifyModal, setShowVerifyModal] = useState(false)
  const [showRejectModal, setShowRejectModal] = useState(false)
  const [verificationNotes, setVerificationNotes] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // Load doctors and their credentials from API
  useEffect(() => {
    const loadDoctors = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Get doctors from API using role filtering
        const roleParam = 'doctor'
        console.log('DoctorManagement: Role parameter:', roleParam)
        const usersResponse = await apiClient.getUsers({ role: roleParam })
        // Check if we have valid user data
        let doctorUsers = [];
        
        if (usersResponse && (usersResponse as any).data && Array.isArray((usersResponse as any).data)) {
          doctorUsers = (usersResponse as any).data
        } else if (Array.isArray(usersResponse)) {
          doctorUsers = usersResponse
        }
        
        // Get credentials for all doctors
        const credentialsResponse = await apiClient.getAdminCredentials({})
        let credentialsData = []
        
        if (credentialsResponse && credentialsResponse.credentials && Array.isArray(credentialsResponse.credentials)) {
          credentialsData = credentialsResponse.credentials
        } else if (credentialsResponse && credentialsResponse.data && Array.isArray(credentialsResponse.data)) {
          credentialsData = credentialsResponse.data
        } else if (Array.isArray(credentialsResponse)) {
          credentialsData = credentialsResponse
        }
        
        // Transform API user data to match expected Doctor interface
        const mappedDoctors: Doctor[] = doctorUsers.map((apiUser: any) => {
          // Find credentials for this doctor
          const doctorCredentials = credentialsData.filter((cred: any) => cred.doctorId === apiUser.id)
          
          return {
            id: apiUser.id,
            name: apiUser.firstName && apiUser.lastName ? `${apiUser.firstName} ${apiUser.lastName}` : apiUser.email,
            email: apiUser.email,
            phone: apiUser.phone || undefined,
            role: 'doctor' as const,
            status: apiUser.isActive ? 'active' : 'inactive',
            createdAt: apiUser.createdAt,
            lastLogin: apiUser.lastLoginAt || undefined,
            casesCount: apiUser.casesCount || 0,
            specialization: apiUser.profile?.bio || apiUser.specialization || 'General Medicine', // Backward compatibility
            specializations: apiUser.specializations || [], // New multiple specializations
            licenseNumber: apiUser.licenseNumber || undefined,
            department: apiUser.department || undefined,
            credentials: doctorCredentials || []
          }
        })
        
        setDoctors(mappedDoctors)
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load doctors:', error)
        setError('Failed to load doctors. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    loadDoctors()
  }, [])

  // Add defensive checks to handle potentially missing properties
  const filteredDoctors = doctors.filter(doctor => {
    if (!doctor) return false;
    
    // Check if name and email exist before using toLowerCase()
    const doctorName = doctor.name?.toLowerCase() || '';
    const doctorEmail = doctor.email?.toLowerCase() || '';
    const searchTermLower = searchTerm.toLowerCase();
    
    const matchesSearch = doctorName.includes(searchTermLower) || doctorEmail.includes(searchTermLower);
    const matchesStatus = statusFilter === 'all' || doctor.status === statusFilter;
    return matchesSearch && matchesStatus
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleStatusChange = async (doctorId: string, newStatus: Doctor['status']) => {
    setIsLoading(true)
    try {
      // API call to update doctor status
      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setDoctors(prev => prev.map(d => 
        d.id === doctorId ? { ...d, status: newStatus } : d
      ))
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error updating doctor status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteDoctor = async (doctorId: string) => {
    setIsLoading(true)
    try {
      // API call to delete doctor
      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setDoctors(prev => prev.filter(d => d.id !== doctorId))
      setShowDeleteConfirm(false)
      setSelectedDoctor(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error deleting doctor:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleViewCredentials = (doctor: Doctor) => {
    setSelectedDoctor(doctor)
    setShowCredentialsModal(true)
  }

  const handleVerifyCredential = async () => {
    if (!selectedCredential) return
    
    setIsProcessing(true)
    try {
      await apiClient.verifyCredential(selectedCredential.id, verificationNotes)
      
      // Update local state
      setDoctors(prev => prev.map(d => 
        d.id === selectedCredential.doctorId 
          ? {
              ...d,
              credentials: d.credentials?.map(c => 
                c.id === selectedCredential.id 
                  ? { ...c, status: 'verified' as const, verifiedAt: new Date().toISOString(), notes: verificationNotes }
                  : c
              )
            }
          : d
      ))
      
      setShowVerifyModal(false)
      setVerificationNotes('')
      setSelectedCredential(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error verifying credential:', error)
      setError('Failed to verify credential. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRejectCredential = async () => {
    if (!selectedCredential || !rejectionReason.trim()) return
    
    setIsProcessing(true)
    try {
      await apiClient.rejectCredential(selectedCredential.id, rejectionReason)
      
      // Update local state
      setDoctors(prev => prev.map(d =>
        d.id === selectedCredential.doctorId
          ? {
              ...d,
              credentials: d.credentials?.map(c =>
                c.id === selectedCredential.id
                  ? { ...c, status: 'revoked' as const, rejectionReason }
                  : c
              )
            }
          : d
      ))
      
      setShowRejectModal(false)
      setRejectionReason('')
      setSelectedCredential(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error rejecting credential:', error)
      setError('Failed to reject credential. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle doctor impersonation
  const handleImpersonate = async (doctorId: string) => {
    try {
      setIsLoading(true)
      const doctorToImpersonate = doctors.find(d => d.id === doctorId)
      
      if (!doctorToImpersonate) {
        // TODO: Replace with proper error reporting
  console.error('Doctor not found')
        setIsLoading(false)
        return
      }
      
      // Call the impersonation API
      const response = await apiClient.impersonateUser(doctorId)
      
      if (!response || !response.token) {
        // TODO: Replace with proper error reporting
  console.error('Failed to impersonate doctor: Invalid response format')
        setIsLoading(false)
        return
      }
      
      // Store original admin token for returning later
      localStorage.setItem('admin_token', localStorage.getItem('auth_token') || '')
      
      // Set the impersonation token
      apiClient.setToken(response.token)
      
      // Store impersonation info
      localStorage.setItem('impersonating', 'true')
      localStorage.setItem('impersonated_user_id', doctorId)
      localStorage.setItem('impersonated_user_name', doctorToImpersonate.name)
      localStorage.setItem('impersonated_user_role', 'doctor')
      
      // Store the token in localStorage first to ensure it's available
      localStorage.setItem('auth_token', response.token)
      
      // Navigate to doctor dashboard
      navigate('/doctor/cases')
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to impersonate doctor:', error)
      setIsLoading(false)
    }
  }

  const stats = {
    total: doctors.length,
    active: doctors.filter(d => d.status === 'active').length,
    pending: doctors.filter(d => d.status === 'pending').length,
    withCredentials: doctors.filter(d => d.credentials && d.credentials.length > 0).length,
    pendingCredentials: doctors.reduce((acc, d) => acc + (d.credentials?.filter(c => c.status === 'pending').length || 0), 0),
    eligibleForCases: doctors.filter(d => d.status === 'active').length, // All active doctors are eligible
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Doctor Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage doctors and their credentials
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Doctor
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShieldCheck className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Doctors</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.active}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.pending}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Award className="h-6 w-6 text-purple-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">With Credentials</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.withCredentials}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Eligible for Cases</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.eligibleForCases}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search doctors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
              <option value="pending">Pending</option>
            </select>
            
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Doctors Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
              <p className="mt-4 text-gray-500">Loading doctors...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <div className="text-red-500 mb-2">
                <AlertTriangle className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-700">{error}</p>
            </div>
          ) : filteredDoctors.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-400 mb-2">
                <Users className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-700">No doctors found</p>
              <p className="text-gray-500 text-sm mt-1">
                {searchTerm || statusFilter !== 'all' ? 
                  'Try adjusting your filters' : 'No doctors have been added yet'}
              </p>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Doctor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Specialization
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Credentials
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredDoctors.map((doctor) => {
                  const pendingCreds = doctor.credentials?.filter(c => c.status === 'pending').length || 0
                  const verifiedCreds = doctor.credentials?.filter(c => c.status === 'verified').length || 0
                  const totalCreds = doctor.credentials?.length || 0
                  
                  return (
                    <tr key={doctor.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {doctor.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{doctor.name}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <Mail className="h-3 w-3 mr-1" />
                              {doctor.email}
                            </div>
                            {doctor.phone && (
                              <div className="text-sm text-gray-500 flex items-center">
                                <Phone className="h-3 w-3 mr-1" />
                                {doctor.phone}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {doctor.specializations && doctor.specializations.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {doctor.specializations.map((spec: Specialization, index: number) => (
                              <span
                                key={spec.assignment_id || index}
                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  spec.is_primary
                                    ? 'bg-blue-100 text-blue-800 ring-1 ring-blue-600'
                                    : 'bg-gray-100 text-gray-800'
                                }`}
                              >
                                {spec.specialization_name}
                                {spec.is_primary && (
                                  <span className="ml-1 text-blue-600">★</span>
                                )}
                              </span>
                            ))}
                          </div>
                        ) : doctor.specialization ? (
                          // Fallback to bio field for backward compatibility
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {doctor.specialization}
                          </span>
                        ) : (
                          <span className="text-gray-400">Not specified</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[doctor.status].color}`}>
                          {statusConfig[doctor.status].label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {totalCreds > 0 ? (
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                <span className="text-green-600">{verifiedCreds} verified</span>
                                {pendingCreds > 0 && (
                                  <span className="text-yellow-600">{pendingCreds} pending</span>
                                )}
                              </div>
                              <div className="text-xs text-gray-500">Total: {totalCreds}</div>
                            </div>
                          ) : (
                            <span className="text-gray-500">No credentials</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {doctor.lastLogin ? formatDate(doctor.lastLogin) : 'Never'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewCredentials(doctor)}
                            className="text-purple-600 hover:text-purple-900"
                            title="View Credentials"
                          >
                            <Award className="h-4 w-4" />
                          </button>
                          
                          {doctor.status === 'active' ? (
                            <button
                              onClick={() => handleStatusChange(doctor.id, 'suspended')}
                              className="text-red-600 hover:text-red-900"
                              title="Suspend Doctor"
                            >
                              <UserX className="h-4 w-4" />
                            </button>
                          ) : doctor.status === 'suspended' ? (
                            <button
                              onClick={() => handleStatusChange(doctor.id, 'active')}
                              className="text-green-600 hover:text-green-900"
                              title="Activate Doctor"
                            >
                              <UserCheck className="h-4 w-4" />
                            </button>
                          ) : null}
                          
                          <button
                            onClick={() => {
                              setSelectedDoctor(doctor)
                              // setShowUserModal(true) // We'll implement this later if needed
                            }}
                            className="text-primary-600 hover:text-primary-900"
                            title="Edit Doctor"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          
                          <button
                            onClick={() => {
                              setSelectedDoctor(doctor)
                              setShowDeleteConfirm(true)
                            }}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Doctor"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                          
                          {/* Impersonate Doctor Button */}
                          {doctor.status === 'active' && (
                            <button
                              onClick={() => handleImpersonate(doctor.id)}
                              className="text-blue-600 hover:text-blue-900"
                              title="Impersonate Doctor"
                            >
                              <UserCog className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Credentials Modal */}
      {showCredentialsModal && selectedDoctor && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-full max-w-6xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Credentials for {selectedDoctor.name}
                </h3>
                <button
                  onClick={() => setShowCredentialsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircle className="h-6 w-6" />
                </button>
              </div>
              
              {selectedDoctor.credentials && selectedDoctor.credentials.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Credential
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Dates
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {selectedDoctor.credentials.map((credential) => {
                        const StatusIcon = credentialStatusConfig[credential.status].icon
                        const TypeIcon = credentialTypeConfig[credential.credentialType as keyof typeof credentialTypeConfig]?.icon || FileText
                        
                        return (
<tr key={credential.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <TypeIcon className="h-5 w-5 text-gray-400 mr-2" />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {credentialTypeConfig[credential.credentialType as keyof typeof credentialTypeConfig]?.label || credential.credentialType}
                                  </div>
                                  <div className="text-sm text-gray-500">{credential.credentialNumber}</div>
                                  <div className="text-sm text-gray-500 flex items-center">
                                    <Building className="h-3 w-3 mr-1" />
                                    {credential.issuingAuthority}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${credentialStatusConfig[credential.status].color}`}>
                                <StatusIcon className="h-3 w-3 mr-1" />
                                {credentialStatusConfig[credential.status].label}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                <div>
                                  <div>Issued: {formatDate(credential.issuedDate)}</div>
                                  {credential.expirationDate && (
                                    <div>Expires: {formatDate(credential.expirationDate)}</div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => {
                                    // View credential details - could open another modal
                                  }}
                                  className="text-primary-600 hover:text-primary-900"
                                  title="View Details"
                                >
                                  <Eye className="h-4 w-4" />
                                </button>
                                
                                {credential.status === 'pending' && (
                                  <>
                                    <button
                                      onClick={() => {
                                        setSelectedCredential(credential)
                                        setShowVerifyModal(true)
                                      }}
                                      className="text-green-600 hover:text-green-900"
                                      title="Verify Credential"
                                    >
                                      <CheckCircle className="h-4 w-4" />
                                    </button>
                                    
                                    <button
                                      onClick={() => {
                                        setSelectedCredential(credential)
                                        setShowRejectModal(true)
                                      }}
                                      className="text-red-600 hover:text-red-900"
                                      title="Reject Credential"
                                    >
                                      <XCircle className="h-4 w-4" />
                                    </button>
                                  </>
                                )}
                              </div>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="p-8 text-center">
                  <div className="text-gray-400 mb-2">
                    <Award className="h-12 w-12 mx-auto" />
                  </div>
                  <p className="text-gray-700">No credentials found</p>
                  <p className="text-gray-500 text-sm mt-1">
                    This doctor has not submitted any credentials yet.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Verify Modal */}
      {showVerifyModal && selectedCredential && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Verify Credential</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to verify this credential?
                </p>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Verification Notes (Optional)</label>
                  <textarea
                    value={verificationNotes}
                    onChange={(e) => setVerificationNotes(e.target.value)}
                    rows={3}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Add any notes about the verification..."
                  />
                </div>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={handleVerifyCredential}
                    disabled={isProcessing}
                    className="flex-1 px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-300 disabled:opacity-50"
                  >
                    {isProcessing ? 'Verifying...' : 'Verify'}
                  </button>
                  <button
                    onClick={() => setShowVerifyModal(false)}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && selectedCredential && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Reject Credential</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Please provide a reason for rejecting this credential.
                </p>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rejection Reason *</label>
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    rows={3}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Explain why this credential is being rejected..."
                    required
                  />
                </div>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={handleRejectCredential}
                    disabled={isProcessing || !rejectionReason.trim()}
                    className="flex-1 px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                  >
                    {isProcessing ? 'Rejecting...' : 'Reject'}
                  </button>
                  <button
                    onClick={() => setShowRejectModal(false)}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && selectedDoctor && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Doctor</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete {selectedDoctor.name}? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleDeleteDoctor(selectedDoctor.id)}
                    disabled={isLoading}
                    className="flex-1 px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                  >
                    {isLoading ? 'Deleting...' : 'Delete'}
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}