import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import { useNavigate } from 'react-router-dom'
import {
  Search,
  UserPlus,
  Edit,
  Trash2,
  Mail,
  Phone,
  UserCheck,
  UserX,
  Users,
  UserCog,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

interface Patient {
  id: string
  name: string
  email: string
  phone?: string
  role: 'patient'
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  createdAt: string
  lastLogin?: string
  casesCount?: number
}

const statusConfig = {
  active: { label: 'Active', color: 'bg-green-100 text-green-800' },
  inactive: { label: 'Inactive', color: 'bg-gray-100 text-gray-800' },
  suspended: { label: 'Suspended', color: 'bg-red-100 text-red-800' },
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800' }
}

export function PatientManagement() {
  const { user: _user } = useAuth()
  const [patients, setPatients] = useState<Patient[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Load patients from API using role filtering
  useEffect(() => {
    const loadPatients = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Get patients from API using role filtering
        const response = await apiClient.getUsers({ role: 'patient' })
        // Check if we have valid user data
        let patientUsers = [];
        
        if (response && (response as any).data && Array.isArray((response as any).data)) {
          patientUsers = (response as any).data
        } else if (Array.isArray(response)) {
          patientUsers = response
        }
        
        // Transform API user data to match expected Patient interface
        const mappedPatients: Patient[] = patientUsers.map((apiUser: any) => ({
          id: apiUser.id,
          name: apiUser.firstName && apiUser.lastName ? `${apiUser.firstName} ${apiUser.lastName}` : apiUser.email,
          email: apiUser.email,
          phone: apiUser.phone || undefined,
          role: 'patient' as const,
          status: apiUser.isActive ? 'active' : 'inactive',
          createdAt: apiUser.createdAt,
          lastLogin: apiUser.lastLoginAt || undefined,
          casesCount: apiUser.casesCount || 0
        }))
        
        setPatients(mappedPatients)
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load patients:', error)
        setError('Failed to load patients. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    loadPatients()
  }, [])

  // Add defensive checks to handle potentially missing properties
  const filteredPatients = patients.filter(patient => {
    if (!patient) return false;
    
    // Check if name and email exist before using toLowerCase()
    const patientName = patient.name?.toLowerCase() || '';
    const patientEmail = patient.email?.toLowerCase() || '';
    const searchTermLower = searchTerm.toLowerCase();
    
    const matchesSearch = patientName.includes(searchTermLower) || patientEmail.includes(searchTermLower);
    const matchesStatus = statusFilter === 'all' || patient.status === statusFilter;
    
    return matchesSearch && matchesStatus
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleStatusChange = async (patientId: string, newStatus: Patient['status']) => {
    setIsLoading(true)
    try {
      // API call to update patient status
      await apiClient.updateUserStatus(patientId, newStatus)
      
      setPatients(prev => prev.map(p => 
        p.id === patientId ? { ...p, status: newStatus } : p
      ))
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error updating patient status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeletePatient = async (patientId: string) => {
    setIsLoading(true)
    try {
      // API call to delete patient
      await apiClient.deleteUser(patientId)
      
      setPatients(prev => prev.filter(p => p.id !== patientId))
      setShowDeleteConfirm(false)
      setSelectedPatient(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error deleting patient:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle patient impersonation
  const handleImpersonate = async (patientId: string) => {
    try {
      setIsLoading(true)
      const patientToImpersonate = patients.find(p => p.id === patientId)
      
      if (!patientToImpersonate) {
        // TODO: Replace with proper error reporting
  console.error('Patient not found')
        setIsLoading(false)
        return
      }
      
      // Call the impersonation API
      const response = await apiClient.impersonateUser(patientId)
      
      if (!response || !response.token) {
        // TODO: Replace with proper error reporting
  console.error('Failed to impersonate patient: Invalid response format')
        setIsLoading(false)
        return
      }
      
      // Store original admin token for returning later
      localStorage.setItem('admin_token', localStorage.getItem('auth_token') || '')
      
      // Set the impersonation token
      apiClient.setToken(response.token)
      
      // Store impersonation info
      localStorage.setItem('impersonating', 'true')
      localStorage.setItem('impersonated_user_id', patientId)
      localStorage.setItem('impersonated_user_name', patientToImpersonate.name)
      localStorage.setItem('impersonated_user_role', 'patient')
      
      // Store the token in localStorage first to ensure it's available
      localStorage.setItem('auth_token', response.token)
      
      // Navigate to patient dashboard
      navigate('/patient/cases')
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to impersonate patient:', error)
      setIsLoading(false)
    }
  }

  const stats = {
    total: patients.length,
    active: patients.filter(p => p.status === 'active').length,
    pending: patients.filter(p => p.status === 'pending').length,
    suspended: patients.filter(p => p.status === 'suspended').length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Patient Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage patients and their accounts
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Patient
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Patients</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.active}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.pending}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserX className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Suspended</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.suspended}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search patients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Patients Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
              <p className="mt-4 text-gray-500">Loading patients...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <div className="text-red-500 mb-2">
                <AlertTriangle className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-700">{error}</p>
            </div>
          ) : filteredPatients.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-400 mb-2">
                <Users className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-700">No patients found</p>
              <p className="text-gray-500 text-sm mt-1">
                {searchTerm || statusFilter !== 'all' ? 
                  'Try adjusting your filters' : 'No patients have been added yet'}
              </p>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Patient
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cases
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredPatients.map((patient) => (
                  <tr key={patient.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {patient.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{patient.name}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {patient.email}
                          </div>
                          {patient.phone && (
                            <div className="text-sm text-gray-500 flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {patient.phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[patient.status].color}`}>
                        {statusConfig[patient.status].label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {patient.casesCount || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {patient.lastLogin ? formatDate(patient.lastLogin) : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {patient.status === 'active' ? (
                          <button
                            onClick={() => handleStatusChange(patient.id, 'suspended')}
                            className="text-red-600 hover:text-red-900"
                            title="Suspend Patient"
                          >
                            <UserX className="h-4 w-4" />
                          </button>
                        ) : patient.status === 'suspended' ? (
                          <button
                            onClick={() => handleStatusChange(patient.id, 'active')}
                            className="text-green-600 hover:text-green-900"
                            title="Activate Patient"
                          >
                            <UserCheck className="h-4 w-4" />
                          </button>
                        ) : null}
                        
                        <button
                          onClick={() => {
                            setSelectedPatient(patient)
                            // setShowPatientModal(true) // We'll implement this later if needed
                          }}
                          className="text-primary-600 hover:text-primary-900"
                          title="Edit Patient"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        
                        <button
                          onClick={() => {
                            setSelectedPatient(patient)
                            setShowDeleteConfirm(true)
                          }}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Patient"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                        
                        {/* Impersonate Patient Button */}
                        {patient.status === 'active' && (
                          <button
                            onClick={() => handleImpersonate(patient.id)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Impersonate Patient"
                          >
                            <UserCog className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && selectedPatient && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Patient</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete {selectedPatient.name}? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleDeletePatient(selectedPatient.id)}
                    disabled={isLoading}
                    className="flex-1 px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                  >
                    {isLoading ? 'Deleting...' : 'Delete'}
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="flex-1 px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}