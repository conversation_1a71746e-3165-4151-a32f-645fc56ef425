import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Search,
  UserCheck,
  Clock,
  AlertCircle,
  User,
  Stethoscope,
  Calendar,
  ArrowRight,
  X,
  Plus
} from 'lucide-react'

interface UnassignedCase {
  id: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  specialization: string
  submittedAt: string
  patientId: string
  patientName?: string
  patientAge?: number
  patientGender?: string
  status: string
  urgencyLevel: string
  specialtyRequired: string
  symptoms?: string
  medicalHistory?: string
  currentMedications?: string
  createdAt: string
  updatedAt: string
}

interface Doctor {
  id: string
  firstName: string
  lastName: string
  email: string
  role: string
  specialization?: string
  licenseNumber?: string
  yearsOfExperience?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// No mock data - using real API data

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-600', urgency: 1 },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-600', urgency: 2 },
  high: { label: 'High', color: 'bg-orange-100 text-orange-600', urgency: 3 },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-600', urgency: 4 }
}

// Removed complexityConfig as it's not used with real API data

export function CaseAssignment() {
  const { user: _user } = useAuth()
  const [cases, setCases] = useState<UnassignedCase[]>([])
  const [doctors, setDoctors] = useState<Doctor[]>([])
  const [selectedCase, setSelectedCase] = useState<UnassignedCase | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [specializationFilter, setSpecializationFilter] = useState<string>('all')
  const [isAssigning, setIsAssigning] = useState(false)
  const [showAssignModal, setShowAssignModal] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const casesResponse = await apiClient.getCases()
        const unassignedCases = casesResponse.data.filter((case_: any) => 
          case_.status === 'submitted' || case_.status === 'pending'
        )
        
        const transformedCases: UnassignedCase[] = unassignedCases.map((case_: any) => ({
          id: case_.id,
          title: case_.title,
          description: case_.description,
          priority: case_.urgencyLevel?.toLowerCase() || 'medium',
          specialization: case_.specialtyRequired || 'General',
          submittedAt: case_.createdAt,
          patientId: case_.patientId,
          patientName: case_.patientName || 'Unknown Patient',
          status: case_.status,
          urgencyLevel: case_.urgencyLevel,
          specialtyRequired: case_.specialtyRequired,
          symptoms: case_.symptoms,
          medicalHistory: case_.medicalHistory,
          currentMedications: case_.currentMedications,
          createdAt: case_.createdAt,
          updatedAt: case_.updatedAt
        }))
        
        setCases(transformedCases)
        
        const usersResponse = await apiClient.getUsers()
        const doctorUsers = ((usersResponse as any).data || usersResponse || []).filter((user: any) => 
          user.role === 'doctor' && user.isActive
        )
        
        setDoctors(doctorUsers)
        
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load data:', error)
        setError('Failed to load cases and doctors. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    loadData()
  }, [])

  const filteredCases = cases.filter(case_ => {
    const matchesSearch = case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (case_.patientName && case_.patientName.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesPriority = priorityFilter === 'all' || case_.priority === priorityFilter
    const matchesSpecialization = specializationFilter === 'all' || case_.specialization === specializationFilter
    
    return matchesSearch && matchesPriority && matchesSpecialization
  })

  const sortedCases = filteredCases.sort((a, b) => {
    const priorityA = priorityConfig[a.priority as keyof typeof priorityConfig]?.urgency || 1
    const priorityB = priorityConfig[b.priority as keyof typeof priorityConfig]?.urgency || 1
    if (priorityA !== priorityB) return priorityB - priorityA
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getDaysWaiting = (createdAt: string) => {
    const created = new Date(createdAt)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - created.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  const getWaitingDaysColor = (days: number) => {
    if (days >= 5) return 'text-red-600'
    if (days >= 3) return 'text-orange-600'
    return 'text-green-600'
  }

  const handleAssignCase = async (doctorId: string) => {
    if (!selectedCase) return
    
    setIsAssigning(true)
    try {
      await apiClient.assignCase(selectedCase.id, doctorId)
      
      setCases(prev => prev.filter(c => c.id !== selectedCase.id))
      setShowAssignModal(false)
      setSelectedCase(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to assign case:', error)
      setError('Failed to assign case. Please try again.')
    } finally {
      setIsAssigning(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Data</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const specializations = Array.from(new Set(cases.map(c => c.specialization)))

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Case Assignment</h1>
          <p className="mt-1 text-sm text-gray-500">
            Assign unassigned cases to appropriate specialists
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <div className="inline-flex items-center px-3 py-2 border border-orange-200 rounded-md bg-orange-50">
            <AlertCircle className="h-4 w-4 text-orange-500 mr-2" />
            <span className="text-sm text-orange-700">
              {cases.length} cases awaiting assignment
            </span>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search cases or patients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
            >
              <option value="all">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select
              value={specializationFilter}
              onChange={(e) => setSpecializationFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
            >
              <option value="all">All Specializations</option>
              {specializations.map(spec => (
                <option key={spec} value={spec}>{spec}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Cases List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {sortedCases.length === 0 ? (
          <div className="p-8 text-center">
            <UserCheck className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No unassigned cases</h3>
            <p className="mt-1 text-sm text-gray-500">
              All cases have been assigned to doctors.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {sortedCases.map((case_) => {
              const daysWaiting = getDaysWaiting(case_.createdAt)
              return (
                <div key={case_.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">
                          {case_.title}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityConfig[case_.priority as keyof typeof priorityConfig]?.color || 'bg-gray-100 text-gray-600'}`}>
                          {priorityConfig[case_.priority as keyof typeof priorityConfig]?.label || case_.priority}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-600">
                          {case_.specialization || 'General'}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {case_.description}
                      </p>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2" />
                          <span>{case_.patientName || 'Unknown Patient'}</span>
                        </div>
                        <div className="flex items-center">
                          <Stethoscope className="h-4 w-4 mr-2" />
                          <span>{case_.specialization}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>Submitted {formatDate(case_.createdAt)}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span className={getWaitingDaysColor(daysWaiting)}>
                            Waiting {daysWaiting} days
                          </span>
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-400">
                        Status: {case_.status}
                      </div>
                    </div>
                    
                    <div className="flex-shrink-0">
                      <button
                        onClick={() => {
                          setSelectedCase(case_)
                          setShowAssignModal(true)
                        }}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Assign Doctor
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Assignment Modal */}
      {showAssignModal && selectedCase && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Assign Case: {selectedCase.title}
              </h3>
              <button
                onClick={() => setShowAssignModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>Patient: {selectedCase.patientName || 'Unknown Patient'}</span>
                <span>Specialization: {selectedCase.specialization || 'General'}</span>
                <span className={`px-2 py-1 rounded ${priorityConfig[selectedCase.priority as keyof typeof priorityConfig]?.color || 'bg-gray-100 text-gray-600'}`}>
                  {priorityConfig[selectedCase.priority as keyof typeof priorityConfig]?.label || selectedCase.priority} Priority
                </span>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Available Doctors</h4>
              {doctors.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No doctors available</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {doctors.filter(doctor => doctor.isActive).map((doctor: Doctor) => (
                    <div key={doctor.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <h5 className="font-medium text-gray-900">
                              {doctor.firstName} {doctor.lastName}
                            </h5>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Available
                            </span>
                          </div>
                          <div className="mt-2 grid grid-cols-2 gap-4 text-sm text-gray-500">
                            <span>Email: {doctor.email}</span>
                            <span>Specialization: {doctor.specialization || 'General'}</span>
                            <span>Experience: {doctor.yearsOfExperience || 'N/A'} years</span>
                            <span>License: {doctor.licenseNumber || 'N/A'}</span>
                          </div>
                        </div>
                        <button
                          onClick={() => handleAssignCase(doctor.id)}
                          disabled={isAssigning}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isAssigning ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Assigning...
                            </>
                          ) : (
                            <>
                              <ArrowRight className="h-4 w-4 mr-2" />
                              Assign
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-6 w-6 text-orange-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Unassigned Cases</dt>
                  <dd className="text-lg font-medium text-gray-900">{cases.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertCircle className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Urgent Cases</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {cases.filter(c => c.priority === 'urgent').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Delayed (3+ days)</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {cases.filter(c => getDaysWaiting(c.createdAt) >= 3).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Available Doctors</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {doctors.filter(d => d.isActive).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
