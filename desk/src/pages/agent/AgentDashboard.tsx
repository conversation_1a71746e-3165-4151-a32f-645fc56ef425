import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import { Link } from 'react-router-dom'
import {
  MessageCircle,
  Clipboard,
  Clock,
  Star,
  Users,
  UserCheck,
  Settings
} from 'lucide-react'
import { DashboardCard } from '@/components/ui/dashboard-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { PageHeader } from '@/components/ui/page-header'
import { ErrorAlert } from '@/components/ui/error-alert'
import { layouts, commonClasses } from '@/constants/theme'

interface AgentStats {
  activeChats: number
  casesCoordinated: number
  pendingTasks: number
  satisfactionRating: number
  activeCases: number
}

export function AgentDashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<AgentStats>({
    activeChats: 0,
    casesCoordinated: 0,
    pendingTasks: 0,
    satisfactionRating: 4.9,
    activeCases: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Fetch cases to calculate stats
        const casesResponse = await apiClient.getCases()
        const cases = casesResponse.data || []
        
        // Calculate stats from available data
        const activeCases = cases.filter(c => ['submitted', 'assigned', 'in_review'].includes(c.status)).length
        const casesCoordinated = cases.filter(c => c.status === 'completed').length
        
        setStats({
          activeChats: 0, // This would come from a chat API
          casesCoordinated,
          pendingTasks: activeCases,
          satisfactionRating: 4.9, // This would come from a ratings API
          activeCases
        })
      } catch (err) {
        // TODO: Replace with proper error reporting
  console.error('Error fetching dashboard stats:', err)
        setError('Failed to load dashboard statistics')
      } finally {
        setLoading(false)
      }
    }
    
    fetchDashboardStats()
  }, [])

  if (loading) {
    return (
      <LoadingSpinner
        fullScreen
        text="Loading your agent dashboard..."
        size="xl"
      />
    )
  }

  return (
    <div className={layouts.page}>
      <div className={`${layouts.container} ${layouts.pageContent}`}>
        {/* Error Alert */}
        {error && (
          <ErrorAlert
            variant="error"
            title="Dashboard Error"
            message={error}
            dismissible
            onDismiss={() => setError(null)}
          />
        )}

        {/* Welcome Section */}
        <PageHeader
          title={`Welcome back, ${user?.firstName || 'Agent'}! 🎯`}
          description="Manage patient interactions and coordinate case workflows efficiently."
          action={
            <Link
              to="/admin/case-coordination"
              className={commonClasses.primaryButton}
            >
              <Settings className="mr-2 h-4 w-4" />
              Case Coordination
            </Link>
          }
        />

        {/* Stats Cards */}
        <div className={layouts.gridCols4}>
          <DashboardCard
            title="Active Chats"
            value={stats.activeChats}
            description="Ongoing patient conversations"
            icon={MessageCircle}
            variant="gradient"
            color="blue"
          />

          <DashboardCard
            title="Cases Coordinated"
            value={stats.casesCoordinated}
            description="Successfully managed cases"
            icon={Clipboard}
            variant="gradient"
            color="green"
          />

          <DashboardCard
            title="Pending Tasks"
            value={stats.pendingTasks}
            description="Cases requiring attention"
            icon={Clock}
            variant="gradient"
            color="yellow"
          />

          <DashboardCard
            title="Satisfaction Rating"
            value={`${stats.satisfactionRating}/5.0`}
            description="Patient satisfaction score"
            icon={Star}
            variant="gradient"
            color="purple"
          />
        </div>

        {/* Active Interactions Section */}
        <PageHeader
          title="💬 Active Patient Interactions"
          description="Manage ongoing patient conversations and support requests"
          centered={false}
          action={
            <Link
              to="/agent/interactions/active"
              className={commonClasses.secondaryButton}
            >
              <MessageCircle className="mr-2 h-4 w-4" />
              View All Chats
            </Link>
          }
        />

        {stats.activeChats === 0 ? (
          <div className={commonClasses.card}>
            <div className="text-center py-12">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <MessageCircle className="w-6 h-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No active interactions</h3>
              <p className="text-gray-500">
                Patient interactions will appear here when they need assistance.
              </p>
            </div>
          </div>
        ) : (
          <div className={commonClasses.card}>
            <div className="p-6">
              <p className="text-gray-600">
                You have {stats.activeChats} active chat{stats.activeChats !== 1 ? 's' : ''} with patients.
              </p>
              <Link
                to="/agent/interactions/active"
                className={`${commonClasses.primaryButton} mt-4 inline-flex`}
              >
                Manage Chats
              </Link>
            </div>
          </div>
        )}

        {/* Case Management Section */}
        <PageHeader
          title="📋 Case Management"
          description="Coordinate patient cases and doctor assignments"
          centered={false}
          action={
            <Link
              to="/cases"
              className={commonClasses.secondaryButton}
            >
              <Clipboard className="mr-2 h-4 w-4" />
              View All Cases
            </Link>
          }
        />

        {stats.pendingTasks === 0 ? (
          <div className={commonClasses.card}>
            <div className="text-center py-12">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Clipboard className="w-6 h-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No cases to manage</h3>
              <p className="text-gray-500">
                Cases requiring coordination will appear here.
              </p>
            </div>
          </div>
        ) : (
          <div className={commonClasses.card}>
            <div className="p-6">
              <p className="text-gray-600">
                You have {stats.pendingTasks} case{stats.pendingTasks !== 1 ? 's' : ''} requiring coordination.
              </p>
              <Link
                to="/cases"
                className={`${commonClasses.primaryButton} mt-4 inline-flex`}
              >
                Manage Cases
              </Link>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <PageHeader
          title="🚀 Quick Actions"
          description="Common tasks for efficient patient and case management"
          centered={true}
        />

        <div className={layouts.gridCols3}>
          <Link
            to="/cases"
            className="group bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border-2 border-blue-200 hover:border-blue-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <Clipboard className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-blue-900 mb-2">Manage Cases</h4>
            <p className="text-sm text-blue-700 leading-relaxed">Coordinate patient cases and doctor assignments</p>
          </Link>
          
          <Link
            to="/admin/users"
            className="group bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border-2 border-green-200 hover:border-green-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <Users className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-green-900 mb-2">User Management</h4>
            <p className="text-sm text-green-700 leading-relaxed">Manage patient and doctor accounts</p>
          </Link>
          
          <Link
            to="/agent/assignments"
            className="group bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border-2 border-purple-200 hover:border-purple-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-purple-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <UserCheck className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-purple-900 mb-2">Assignments</h4>
            <p className="text-sm text-purple-700 leading-relaxed">Assign doctors to patient cases</p>
          </Link>
        </div>
      </div>
    </div>
  )
}
