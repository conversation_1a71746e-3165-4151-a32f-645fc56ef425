import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Plus,
  Edit,
  Trash2,
  Upload,
  FileText,
  Calendar,
  Building,
  Award,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import {
  backgroundColors,
  buttonColors,
  textColors,
  typography,
  shadows,
  layouts,
  forms,
  spacing,
  sizes,
  animations,
  commonClasses
} from '@/constants/theme'
import { UniversalDocumentViewer } from '@/components/UniversalDocumentViewer'

interface Credential {
  id: string
  credentialType: string
  credentialNumber: string
  issuingAuthority: string
  issuedDate: string
  expirationDate?: string
  status: 'pending' | 'verified' | 'rejected' | 'expired'
  verificationMethod: string
  notes?: string
  metadata?: string
  createdAt: string
  updatedAt: string
}

interface CredentialDocument {
  id: string
  credentialId: string
  title: string
  description: string
  fileName: string
  originalFileName: string
  fileSize: number
  mimeType: string
  uploadedBy: string
  createdAt: string
}

const credentialTypes = [
  { value: 'medical_license', label: 'Medical License' },
  { value: 'board_certification', label: 'Board Certification' },
  { value: 'fellowship', label: 'Fellowship' },
  { value: 'residency', label: 'Residency' },
  { value: 'medical_degree', label: 'Medical Degree' },
  { value: 'continuing_education', label: 'Continuing Education' },
  { value: 'hospital_privileges', label: 'Hospital Privileges' },
  { value: 'malpractice_insurance', label: 'Malpractice Insurance' },
  { value: 'other', label: 'Other' }
]

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'verified':
      return <CheckCircle className="h-5 w-5 text-green-500" />
    case 'pending':
      return <Clock className="h-5 w-5 text-yellow-500" />
    case 'rejected':
      return <X className="h-5 w-5 text-red-500" />
    case 'expired':
      return <AlertCircle className="h-5 w-5 text-red-500" />
    default:
      return <Clock className="h-5 w-5 text-gray-500" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'verified':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    case 'expired':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

export function DoctorCredentials() {
  const { user } = useAuth()
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showInlineForm, setShowInlineForm] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDocumentsModal, setShowDocumentsModal] = useState(false)
  const [selectedCredential, setSelectedCredential] = useState<Credential | null>(null)
  const [credentialDocuments, setCredentialDocuments] = useState<CredentialDocument[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<CredentialDocument | null>(null)

  // Form state
  const [formData, setFormData] = useState({
    credentialType: '',
    credentialNumber: '',
    issuingAuthority: '',
    issuedDate: '',
    expirationDate: '',
    notes: '',
    metadata: ''
  })

  // Document upload state
  const [uploadFile, setUploadFile] = useState<File | null>(null)
  const [uploadTitle, setUploadTitle] = useState('')
  const [uploadDescription, setUploadDescription] = useState('')

  useEffect(() => {
    loadCredentials()
  }, [])

  const loadCredentials = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getDoctorCredentials()
      setCredentials(response.credentials || [])
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load credentials:', error)
      setError('Failed to load credentials. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCredential = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isSubmitting) return

    try {
      setIsSubmitting(true)
      setError(null)
      
      await apiClient.createDoctorCredential(formData)
      
      // Reset form and close modal
      setFormData({
        credentialType: '',
        credentialNumber: '',
        issuingAuthority: '',
        issuedDate: '',
        expirationDate: '',
        notes: '',
        metadata: ''
      })
      setShowInlineForm(false)
      
      // Reload credentials
      await loadCredentials()
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to create credential:', error)
      setError('Failed to create credential. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEditCredential = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isSubmitting || !selectedCredential) return

    try {
      setIsSubmitting(true)
      setError(null)
      
      await apiClient.updateDoctorCredential(selectedCredential.id, formData)
      
      setShowEditModal(false)
      setSelectedCredential(null)
      
      // Reload credentials
      await loadCredentials()
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to update credential:', error)
      setError('Failed to update credential. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const openEditModal = (credential: Credential) => {
    setSelectedCredential(credential)
    setFormData({
      credentialType: credential.credentialType,
      credentialNumber: credential.credentialNumber,
      issuingAuthority: credential.issuingAuthority,
      issuedDate: credential.issuedDate.split('T')[0], // Format for date input
      expirationDate: credential.expirationDate ? credential.expirationDate.split('T')[0] : '',
      notes: credential.notes || '',
      metadata: credential.metadata || ''
    })
    setShowEditModal(true)
  }

  const loadCredentialDocuments = async (credentialId: string) => {
    try {
      const response = await apiClient.getCredentialDocuments(credentialId)
      setCredentialDocuments(response.documents || [])
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load credential documents:', error)
      setError('Failed to load documents. Please try again.')
    }
  }

  const openDocumentsModal = async (credential: Credential) => {
    setSelectedCredential(credential)
    setShowDocumentsModal(true)
    await loadCredentialDocuments(credential.id)
  }

  const handleDocumentUpload = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!uploadFile || !uploadTitle || !selectedCredential || isSubmitting) return

    try {
      setIsSubmitting(true)
      setError(null)
      
      await apiClient.uploadCredentialDocument(selectedCredential.id, uploadFile, {
        title: uploadTitle,
        description: uploadDescription
      })
      
      // Reset upload form
      setUploadFile(null)
      setUploadTitle('')
      setUploadDescription('')
      
      // Reload documents
      await loadCredentialDocuments(selectedCredential.id)
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to upload document:', error)
      setError('Failed to upload document. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className={layouts.page}>
        <div className={layouts.container}>
          <div className={layouts.pageContent}>
            <div className={commonClasses.loading}>
              <div className={commonClasses.loadingSpinner}></div>
              <span className={`ml-4 ${typography.bodyLarge} ${textColors.secondary}`}>✨ Loading credentials...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={layouts.page}>
      <div className={layouts.container}>
        <div className={layouts.pageContent}>
          {/* Compact Header */}
          <div className={commonClasses.card}>
            <div className={spacing.cardPadding}>
              <div className="flex items-center justify-between">
                <div>
                  <h1 className={`${typography.h2} flex items-center`}>
                    🏆 Professional Credentials
                  </h1>
                  <p className={`${typography.body} ${textColors.secondary} mt-2`}>
                    Manage your medical licenses, certifications, and professional qualifications
                  </p>
                </div>
                <button
                  onClick={() => setShowInlineForm(!showInlineForm)}
                  className={`${commonClasses.primaryButton} ${animations.transitionAll}`}
                >
                  {showInlineForm ? (
                    <>
                      <ChevronUp className={`${sizes.iconSmall} mr-2`} />
                      Cancel
                    </>
                  ) : (
                    <>
                      <Plus className={`${sizes.iconSmall} mr-2`} />
                      Add Credential
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className={commonClasses.errorAlert}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <AlertCircle className={`${sizes.iconMedium} ${textColors.error} mt-1 flex-shrink-0`} />
                  <div>
                    <h3 className={`${typography.h5} ${textColors.error} mb-1`}>⚠️ Something went wrong</h3>
                    <p className={`${typography.body} ${textColors.error}`}>{error}</p>
                  </div>
                </div>
                <button
                  onClick={() => setError(null)}
                  className={`${textColors.error} hover:opacity-75`}
                >
                  <X className={sizes.iconMedium} />
                </button>
              </div>
            </div>
          )}

          {/* Credentials List */}
          {credentials.length > 0 ? (
            <div className={layouts.gridCols3}>
              {credentials.map((credential) => (
                <div key={credential.id} className={`${commonClasses.card} group ${animations.hoverShadow}`}>
                  <div className={spacing.cardPadding}>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <Award className={`${sizes.iconMedium} text-primary-600`} />
                        <h3 className={`${typography.h5} ${textColors.primary}`}>
                          {credentialTypes.find(t => t.value === credential.credentialType)?.label || credential.credentialType}
                        </h3>
                      </div>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(credential.status)}`}>
                        {getStatusIcon(credential.status)}
                        <span className="ml-1 capitalize">{credential.status}</span>
                      </span>
                    </div>
                    
                    <div className="space-y-3">
                      <div className={`flex items-center space-x-2 ${typography.caption} ${textColors.muted}`}>
                        <FileText className={sizes.iconSmall} />
                        <span>#{credential.credentialNumber}</span>
                      </div>
                      
                      <div className={`flex items-center space-x-2 ${typography.caption} ${textColors.muted}`}>
                        <Building className={sizes.iconSmall} />
                        <span>{credential.issuingAuthority}</span>
                      </div>
                      
                      <div className={`flex items-center space-x-2 ${typography.caption} ${textColors.muted}`}>
                        <Calendar className={sizes.iconSmall} />
                        <span>Issued: {formatDate(credential.issuedDate)}</span>
                      </div>
                      
                      {credential.expirationDate && (
                        <div className={`flex items-center space-x-2 ${typography.caption} ${textColors.muted}`}>
                          <Calendar className={sizes.iconSmall} />
                          <span>Expires: {formatDate(credential.expirationDate)}</span>
                        </div>
                      )}
                    </div>
                    
                    {credential.notes && (
                      <div className="mt-4 p-3 bg-primary-50 rounded-xl">
                        <p className={`${typography.caption} ${textColors.secondary}`}>{credential.notes}</p>
                      </div>
                    )}
                    
                    <div className="mt-6 flex items-center justify-between">
                      <button
                        onClick={() => openDocumentsModal(credential)}
                        className={`inline-flex items-center px-3 py-2 ${typography.caption} text-primary-600 hover:text-primary-900 border-2 border-primary-300 rounded-xl hover:bg-primary-50 ${animations.transitionAll}`}
                      >
                        <Eye className={`${sizes.iconSmall} mr-2`} />
                        Documents
                      </button>
                      
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => openEditModal(credential)}
                          className={`inline-flex items-center px-3 py-2 ${typography.caption} ${textColors.muted} hover:${textColors.primary} border-2 border-gray-300 rounded-xl hover:bg-gray-50 ${animations.transitionAll}`}
                        >
                          <Edit className={sizes.iconSmall} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : !showInlineForm ? (
            <div className={commonClasses.card}>
              <div className="text-center py-12 px-8">
                <div className="mb-6">
                  <Award className={`h-16 w-16 text-primary-300 mx-auto mb-4`} />
                </div>
                <h3 className={`${typography.h3} mb-4`}>
                  📋 No Credentials Yet
                </h3>
                <p className={`${typography.body} ${textColors.secondary} mb-6 max-w-md mx-auto`}>
                  Start building your professional profile by adding your medical licenses and certifications
                </p>
                <button
                  onClick={() => setShowInlineForm(true)}
                  className={`${commonClasses.primaryButton} px-6 py-3`}
                >
                  <Plus className={`${sizes.iconMedium} mr-2`} />
                  Add Your First Credential
                </button>
              </div>
            </div>
          ) : null}

          {/* Inline Add Credential Form */}
          {showInlineForm && (
            <div className={commonClasses.card}>
              <div className={spacing.cardPadding}>
                <div className="flex items-center justify-between mb-4">
                  <h2 className={`${typography.h3} flex items-center`}>
                    <Plus className={`${sizes.iconMedium} mr-2 text-primary-600`} />
                    Add New Credential
                  </h2>
                  <button
                    onClick={() => setShowInlineForm(false)}
                    className={`${textColors.muted} hover:${textColors.primary} ${animations.transitionAll}`}
                  >
                    <X className={sizes.iconMedium} />
                  </button>
                </div>
                
                <form onSubmit={handleCreateCredential} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={`block ${typography.label} mb-2`}>
                        Credential Type *
                      </label>
                      <select
                        value={formData.credentialType}
                        onChange={(e) => setFormData({ ...formData, credentialType: e.target.value })}
                        className={forms.select}
                        required
                      >
                        <option value="">Select credential type</option>
                        {credentialTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className={`block ${typography.label} mb-2`}>
                        Credential Number *
                      </label>
                      <input
                        type="text"
                        value={formData.credentialNumber}
                        onChange={(e) => setFormData({ ...formData, credentialNumber: e.target.value })}
                        className={forms.input}
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className={`block ${typography.label} mb-2`}>
                      Issuing Authority *
                    </label>
                    <input
                      type="text"
                      value={formData.issuingAuthority}
                      onChange={(e) => setFormData({ ...formData, issuingAuthority: e.target.value })}
                      className={forms.input}
                      required
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={`block ${typography.label} mb-2`}>
                        Issued Date *
                      </label>
                      <input
                        type="date"
                        value={formData.issuedDate}
                        onChange={(e) => setFormData({ ...formData, issuedDate: e.target.value })}
                        className={forms.input}
                        required
                      />
                    </div>
                    
                    <div>
                      <label className={`block ${typography.label} mb-2`}>
                        Expiration Date
                      </label>
                      <input
                        type="date"
                        value={formData.expirationDate}
                        onChange={(e) => setFormData({ ...formData, expirationDate: e.target.value })}
                        className={forms.input}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className={`block ${typography.label} mb-2`}>
                      Notes
                    </label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                      rows={3}
                      className={forms.input}
                    />
                  </div>
                  
                  <div className="flex items-center justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowInlineForm(false)}
                      className={commonClasses.secondaryButton}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`${commonClasses.primaryButton} ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {isSubmitting ? 'Creating...' : 'Create Credential'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Edit Credential Modal */}
      {showEditModal && selectedCredential && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Edit Credential</h2>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <form onSubmit={handleEditCredential} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Credential Type *
                  </label>
                  <select
                    value={formData.credentialType}
                    onChange={(e) => setFormData({ ...formData, credentialType: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  >
                    <option value="">Select credential type</option>
                    {credentialTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Credential Number *
                  </label>
                  <input
                    type="text"
                    value={formData.credentialNumber}
                    onChange={(e) => setFormData({ ...formData, credentialNumber: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Issuing Authority *
                  </label>
                  <input
                    type="text"
                    value={formData.issuingAuthority}
                    onChange={(e) => setFormData({ ...formData, issuingAuthority: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Issued Date *
                    </label>
                    <input
                      type="date"
                      value={formData.issuedDate}
                      onChange={(e) => setFormData({ ...formData, issuedDate: e.target.value })}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Expiration Date
                    </label>
                    <input
                      type="date"
                      value={formData.expirationDate}
                      onChange={(e) => setFormData({ ...formData, expirationDate: e.target.value })}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
                
                <div className="flex items-center justify-end space-x-4 pt-6">
                  <button
                    type="button"
                    onClick={() => setShowEditModal(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Updating...' : 'Update Credential'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Documents Modal */}
      {showDocumentsModal && selectedCredential && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  Documents for {credentialTypes.find(t => t.value === selectedCredential.credentialType)?.label}
                </h2>
                <button
                  onClick={() => setShowDocumentsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              {/* Upload Form */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload Document</h3>
                <form onSubmit={handleDocumentUpload} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      File *
                    </label>
                    <input
                      type="file"
                      onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                      accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title *
                    </label>
                    <input
                      type="text"
                      value={uploadTitle}
                      onChange={(e) => setUploadTitle(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={uploadDescription}
                      onChange={(e) => setUploadDescription(e.target.value)}
                      rows={2}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isSubmitting || !uploadFile || !uploadTitle}
                    className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {isSubmitting ? 'Uploading...' : 'Upload Document'}
                  </button>
                </form>
              </div>
              
              {/* Documents List */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Uploaded Documents</h3>
                {credentialDocuments.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {credentialDocuments.map((doc) => (
                      <div
                        key={doc.id}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer hover:border-primary-300"
                        onClick={() => {
                          // Open document in universal viewer
                          setSelectedDocument(doc)
                        }}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 flex items-center">
                              {doc.title}
                              <Eye className="h-4 w-4 ml-2 text-primary-500" />
                            </h4>
                            <p className="text-sm text-gray-600 mt-1">{doc.description}</p>
                            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                              <span>{doc.originalFileName}</span>
                              <span>{Math.round(doc.fileSize / 1024)} KB</span>
                              <span>{new Date(doc.createdAt).toLocaleDateString()}</span>
                            </div>
                            <div className="mt-2 text-xs text-primary-600 font-medium">
                              Click to view document
                            </div>
                          </div>
                          <FileText className="h-8 w-8 text-gray-400 flex-shrink-0" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">No documents uploaded yet</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Document Viewer Modal */}
      {selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-xl font-bold text-gray-900">
                {selectedDocument.title}
              </h2>
              <button
                onClick={() => setSelectedDocument(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="h-[calc(90vh-80px)]">
              <UniversalDocumentViewer
                document={{
                  id: selectedDocument.id,
                  title: selectedDocument.title,
                  originalFileName: selectedDocument.originalFileName,
                  mimeType: selectedDocument.mimeType,
                  fileSize: selectedDocument.fileSize
                }}
                streamUrl={apiClient.getDocumentStreamUrl(selectedDocument.id)}
                showControls={true}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}