import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Plus,
  Edit,
  Trash2,
  Globe,
  ExternalLink,
  AlertCircle,
  X,
  Save,
  Eye,
  EyeOff
} from 'lucide-react'

interface ProfileUrl {
  id: string
  urlType: string
  url: string
  displayName?: string
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

const urlTypes = [
  { value: 'website', label: 'Personal Website', icon: Globe },
  { value: 'linkedin', label: 'LinkedIn Profile', icon: ExternalLink },
  { value: 'research_gate', label: 'ResearchGate', icon: ExternalLink },
  { value: 'orcid', label: 'ORCID', icon: ExternalLink },
  { value: 'google_scholar', label: 'Google Scholar', icon: ExternalLink },
  { value: 'pubmed', label: 'PubMed', icon: ExternalLink },
  { value: 'hospital_profile', label: 'Hospital Profile', icon: ExternalLink },
  { value: 'practice_website', label: 'Practice Website', icon: Globe },
  { value: 'other', label: 'Other', icon: ExternalLink }
]

export function DoctorPublicSettings() {
  const { user } = useAuth()
  const [profileUrls, setProfileUrls] = useState<ProfileUrl[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedUrl, setSelectedUrl] = useState<ProfileUrl | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    urlType: '',
    url: '',
    displayName: '',
    sortOrder: 0
  })

  // Public profile visibility settings
  const [publicSettings, setPublicSettings] = useState({
    showCredentials: true,
    showExperience: true,
    showEducation: true,
    showPublications: true,
    showContactInfo: false,
    profileVisibility: 'public' as 'public' | 'private' | 'limited'
  })

  useEffect(() => {
    loadProfileUrls()
  }, [])

  const loadProfileUrls = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getDoctorProfileUrls()
      setProfileUrls(response.profileUrls || [])
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load profile URLs:', error)
      setError('Failed to load profile URLs. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateUrl = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isSubmitting) return

    try {
      setIsSubmitting(true)
      setError(null)
      
      await apiClient.createDoctorProfileUrl(formData)
      
      // Reset form and close modal
      setFormData({
        urlType: '',
        url: '',
        displayName: '',
        sortOrder: 0
      })
      setShowCreateModal(false)
      
      // Reload URLs
      await loadProfileUrls()
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to create profile URL:', error)
      setError('Failed to create profile URL. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEditUrl = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isSubmitting || !selectedUrl) return

    try {
      setIsSubmitting(true)
      setError(null)
      
      await apiClient.updateDoctorProfileUrl(selectedUrl.id, formData)
      
      setShowEditModal(false)
      setSelectedUrl(null)
      
      // Reload URLs
      await loadProfileUrls()
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to update profile URL:', error)
      setError('Failed to update profile URL. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteUrl = async (urlId: string) => {
    if (!confirm('Are you sure you want to delete this profile URL?')) {
      return
    }

    try {
      setError(null)
      await apiClient.deleteDoctorProfileUrl(urlId)
      await loadProfileUrls()
    } catch (error: any) {
      // TODO: Replace with proper error reporting
  console.error('Failed to delete profile URL:', error)
      setError('Failed to delete profile URL. Please try again.')
    }
  }

  const openEditModal = (url: ProfileUrl) => {
    setSelectedUrl(url)
    setFormData({
      urlType: url.urlType,
      url: url.url,
      displayName: url.displayName || '',
      sortOrder: url.sortOrder
    })
    setShowEditModal(true)
  }

  const getUrlTypeInfo = (urlType: string) => {
    return urlTypes.find(type => type.value === urlType) || urlTypes[urlTypes.length - 1]
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-600">Loading settings...</span>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-8 sm:p-8">
              <div className="text-center">
                <h1 className="text-3xl font-bold text-gray-900 mb-3">
                  🌐 Public Profile Settings
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed mb-6">
                  Manage your public profile visibility and professional links
                </p>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-lg font-semibold text-red-800 mb-1">⚠️ Error</h3>
                    <p className="text-red-700 text-base leading-relaxed">{error}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setError(null)} 
                  className="text-red-700 hover:text-red-900"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}

          {/* Profile Visibility Settings */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Profile Visibility</h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Profile Visibility Level
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="profileVisibility"
                        value="public"
                        checked={publicSettings.profileVisibility === 'public'}
                        onChange={(e) => setPublicSettings({ ...publicSettings, profileVisibility: e.target.value as any })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                      />
                      <span className="ml-3 text-sm text-gray-700">
                        <strong>Public</strong> - Visible to everyone
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="profileVisibility"
                        value="limited"
                        checked={publicSettings.profileVisibility === 'limited'}
                        onChange={(e) => setPublicSettings({ ...publicSettings, profileVisibility: e.target.value as any })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                      />
                      <span className="ml-3 text-sm text-gray-700">
                        <strong>Limited</strong> - Visible to verified healthcare professionals only
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="profileVisibility"
                        value="private"
                        checked={publicSettings.profileVisibility === 'private'}
                        onChange={(e) => setPublicSettings({ ...publicSettings, profileVisibility: e.target.value as any })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                      />
                      <span className="ml-3 text-sm text-gray-700">
                        <strong>Private</strong> - Not visible to others
                      </span>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Information to Display
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={publicSettings.showCredentials}
                        onChange={(e) => setPublicSettings({ ...publicSettings, showCredentials: e.target.checked })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show credentials and certifications</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={publicSettings.showExperience}
                        onChange={(e) => setPublicSettings({ ...publicSettings, showExperience: e.target.checked })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show professional experience</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={publicSettings.showEducation}
                        onChange={(e) => setPublicSettings({ ...publicSettings, showEducation: e.target.checked })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show education and training</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={publicSettings.showPublications}
                        onChange={(e) => setPublicSettings({ ...publicSettings, showPublications: e.target.checked })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show publications and research</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={publicSettings.showContactInfo}
                        onChange={(e) => setPublicSettings({ ...publicSettings, showContactInfo: e.target.checked })}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show contact information</span>
                    </label>
                  </div>
                </div>

                <div className="pt-4">
                  <button
                    onClick={() => {/* TODO: Save settings */}}
                    className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <Save className="h-5 w-5 mr-2" />
                    Save Settings
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Professional Links */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Professional Links</h2>
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Add Link
                </button>
              </div>

              {profileUrls.length > 0 ? (
                <div className="space-y-4">
                  {profileUrls.map((url) => {
                    const typeInfo = getUrlTypeInfo(url.urlType)
                    const IconComponent = typeInfo.icon
                    return (
                      <div key={url.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                        <div className="flex items-center space-x-4">
                          <IconComponent className="h-6 w-6 text-purple-600" />
                          <div>
                            <h3 className="font-medium text-gray-900">
                              {url.displayName || typeInfo.label}
                            </h3>
                            <a
                              href={url.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-purple-600 hover:text-purple-800 flex items-center"
                            >
                              {url.url}
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </a>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => openEditModal(url)}
                            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteUrl(url.id)}
                            className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Globe className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No professional links added</h3>
                  <p className="text-gray-500 mb-6">Add links to your professional profiles and websites</p>
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Add Your First Link
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Create URL Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Add Professional Link</h2>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <form onSubmit={handleCreateUrl} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Link Type *
                  </label>
                  <select
                    value={formData.urlType}
                    onChange={(e) => setFormData({ ...formData, urlType: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  >
                    <option value="">Select link type</option>
                    {urlTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    URL *
                  </label>
                  <input
                    type="url"
                    value={formData.url}
                    onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="https://example.com"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Display Name
                  </label>
                  <input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Optional custom name for this link"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort Order
                  </label>
                  <input
                    type="number"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) || 0 })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    min="0"
                  />
                  <p className="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                </div>
                
                <div className="flex items-center justify-end space-x-4 pt-6">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Adding...' : 'Add Link'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit URL Modal */}
      {showEditModal && selectedUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Edit Professional Link</h2>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <form onSubmit={handleEditUrl} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Link Type *
                  </label>
                  <select
                    value={formData.urlType}
                    onChange={(e) => setFormData({ ...formData, urlType: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  >
                    <option value="">Select link type</option>
                    {urlTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    URL *
                  </label>
                  <input
                    type="url"
                    value={formData.url}
                    onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="https://example.com"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Display Name
                  </label>
                  <input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Optional custom name for this link"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort Order
                  </label>
                  <input
                    type="number"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) || 0 })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    min="0"
                  />
                  <p className="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                </div>
                
                <div className="flex items-center justify-end space-x-4 pt-6">
                  <button
                    type="button"
                    onClick={() => setShowEditModal(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Updating...' : 'Update Link'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}