import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  ArrowLeft,
  Save,
  Send,
  FileText,
  Eye,
  X,
  AlertCircle,
  Stethoscope,
  Plus
} from 'lucide-react'

interface CaseForOpinion {
  id: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  specialization: string
  submittedAt: string
  status?: 'draft' | 'submitted' | 'in_review' | 'assigned' | 'completed' | 'cancelled'
  patient: {
    id: string
    name: string
    age: number
    gender: string
    medicalHistory: string
  }
  symptoms: string[]
  currentMedications: string[]
  allergies: string[]
  documents: {
    id: string
    name: string
    type: string
    url: string
    uploadedAt: string
  }[]
  previousOpinions?: {
    id: string
    doctor: string
    summary: string
    createdAt: string
  }[]
}

interface MedicalOpinion {
  diagnosis: string
  summary: string
  recommendations: string[]
  followUpRequired: boolean
  followUpInstructions: string
  urgencyLevel: 'routine' | 'urgent' | 'immediate'
  additionalTests: string[]
  referrals: string[]
  prognosis: string
  confidenceLevel: 'high' | 'medium' | 'low'
  differentialDiagnosis: string[]
}

// No mock data - using real API data

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-600' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-600' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-600' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-600' }
}

// const _urgencyConfig = {
//   routine: { label: 'Routine Follow-up', color: 'bg-green-100 text-green-800' },
//   urgent: { label: 'Urgent Follow-up', color: 'bg-orange-100 text-orange-800' },
//   immediate: { label: 'Immediate Attention', color: 'bg-red-100 text-red-800' }
// }

// const _confidenceConfig = {
//   high: { label: 'High Confidence', color: 'bg-green-100 text-green-800' },
//   medium: { label: 'Medium Confidence', color: 'bg-yellow-100 text-yellow-800' },
//   low: { label: 'Low Confidence', color: 'bg-red-100 text-red-800' }
// }

export function CreateOpinion() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { user: _user } = useAuth()
  const [caseData, setCaseData] = useState<CaseForOpinion | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load case data from API
  useEffect(() => {
    const loadCaseData = async () => {
      if (!id) return

      try {
        setLoading(true)
        setError(null)
        const response = await apiClient.getCase(id)
        setCaseData(response.data)
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load case:', error)
        setError('Failed to load case details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    loadCaseData()
  }, [id])
  const [opinion, setOpinion] = useState<MedicalOpinion>({
    diagnosis: '',
    summary: '',
    recommendations: [''],
    followUpRequired: false,
    followUpInstructions: '',
    urgencyLevel: 'routine',
    additionalTests: [''],
    referrals: [''],
    prognosis: '',
    confidenceLevel: 'high',
    differentialDiagnosis: ['']
  })
  const [activeSection, setActiveSection] = useState<'case' | 'opinion'>('case')
  const [isSaving, setIsSaving] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isClosing, setIsClosing] = useState(false)
  const [opinionId, setOpinionId] = useState<string | null>(null)



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500">Loading case details...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !caseData) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading case</h3>
            <p className="mt-1 text-sm text-gray-500">{error || 'Case not found'}</p>
            <div className="mt-6 space-x-3">
              <button
                onClick={() => navigate('/doctor/cases')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Cases
              </button>
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const addArrayItem = (field: keyof Pick<MedicalOpinion, 'recommendations' | 'additionalTests' | 'referrals' | 'differentialDiagnosis'>) => {
    setOpinion(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }))
  }

  const removeArrayItem = (field: keyof Pick<MedicalOpinion, 'recommendations' | 'additionalTests' | 'referrals' | 'differentialDiagnosis'>, index: number) => {
    setOpinion(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }))
  }

  const updateArrayItem = (field: keyof Pick<MedicalOpinion, 'recommendations' | 'additionalTests' | 'referrals' | 'differentialDiagnosis'>, index: number, value: string) => {
    setOpinion(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }))
  }

  const buildOpinionPayload = () => {
    const recs = (opinion.recommendations || []).filter(r => r && r.trim()).join('\n')
    const additionalTests = (opinion.additionalTests || []).filter(t => t && t.trim()).join('\n')
    const referralSpecialty = (opinion.referrals || []).filter(r => r && r.trim()).join(', ')

    const urgencyMap: Record<string, 'low' | 'medium' | 'high' | 'urgent'> = {
      routine: 'low',
      urgent: 'urgent',
      immediate: 'urgent',
    }
    const urgencyLevel = urgencyMap[opinion.urgencyLevel] || 'medium'
    const confidenceLevel = opinion.confidenceLevel

    return {
      diagnosis: opinion.diagnosis?.trim() || '',
      recommendations: recs || (opinion.summary?.trim() || ''),
      treatmentPlan: '',
      followUpInstructions: opinion.followUpInstructions?.trim() || '',
      urgencyLevel,
      confidenceLevel,
      additionalTests,
      referralSpecialty,
      notes: opinion.summary?.trim() || ''
    }
  }

  const handleSaveDraft = async () => {
    setIsSaving(true)
    try {
      if (!id) return

      const payload = buildOpinionPayload()

      if (!payload.diagnosis || payload.diagnosis.length < 10) {
        alert('Diagnosis must be at least 10 characters.')
        return
      }
      if (!payload.recommendations || payload.recommendations.length < 10) {
        alert('Recommendations must be at least 10 characters.')
        return
      }

      const resp = await apiClient.createMedicalOpinion(id, { caseId: id, ...payload })
      const created = (resp as any)?.opinion
      if (created?.id) {
        setOpinionId(created.id)
      }
      alert('Draft saved')
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error saving draft:', error)
      alert('Failed to save draft. Please ensure required fields are filled and you are assigned to this case.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleSubmitOpinion = async () => {
    setIsSubmitting(true)
    try {
      if (!id) return

      // const payload = buildOpinionPayload()
      // if (!payload.diagnosis || payload.diagnosis.length < 10) {
      //   alert('Diagnosis must be at least 10 characters.')
      //   return
      // }
      // if (!payload.recommendations || payload.recommendations.length < 10) {
      //   alert('Recommendations must be at least 10 characters.')
      //   return
      // }

      let currentOpinionId = opinionId
      if (!currentOpinionId) {
        const resp = await apiClient.createMedicalOpinion(id, { caseId: id, ...payload })
        const created = (resp as any)?.opinion
        if (created?.id) currentOpinionId = created.id
      } else {
        await apiClient.updateMedicalOpinion(id, currentOpinionId, payload)
      }

      if (!currentOpinionId) {
        alert('Failed to create opinion. Please try again.')
        return
      }

      await apiClient.submitMedicalOpinion(currentOpinionId)
      alert('Opinion submitted and case marked as completed.')
      navigate('/doctor/cases')
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error submitting opinion:', error)
      alert('Failed to submit opinion. Please ensure required fields are filled and you are assigned to this case.')
    } finally {
      setIsSubmitting(false)
    }
  }


  const handleCloseCase = async () => {
    if (!caseData?.id) return
    setIsClosing(true)
    try {
      await apiClient.updateCase(caseData.id, { status: 'completed' })
      alert('Case has been marked as completed. Medical Opinion remains visible to patient and admins.')
      // Refresh case data to reflect new status
      const refreshed = await apiClient.getCase(caseData.id)
      setCaseData(refreshed.data || caseData)
    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Failed to close case:', err)
      alert('Failed to close the case. Please try again or check your permissions.')
    } finally {
      setIsClosing(false)
    }
  }

  const isOpinionComplete = opinion.diagnosis && opinion.summary && opinion.recommendations.some(r => r.trim())

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/doctor/cases')}
            className="flex items-center text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft className="h-5 w-5 mr-1" />
            Back to Cases
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Medical Opinion</h1>
            <p className="text-sm text-gray-500">Case: {caseData.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleSaveDraft}
            disabled={isSaving}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Draft'}
          </button>
          <button
            onClick={handleSubmitOpinion}
            disabled={!isOpinionComplete || isSubmitting}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4 mr-2" />
            {isSubmitting ? 'Submitting...' : 'Submit Opinion'}
          </button>
          <button
            onClick={handleCloseCase}
            disabled={isClosing || caseData.status === 'completed' || caseData.status === 'cancelled'}
            className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              isClosing || caseData.status === 'completed' || caseData.status === 'cancelled'
                ? 'text-gray-400 border-gray-200 bg-white cursor-not-allowed'
                : 'text-gray-700 border-gray-300 bg-white hover:bg-gray-50 focus:ring-red-500'
            }`}
            title={caseData.status === 'completed' ? 'Case already completed' : caseData.status === 'cancelled' ? 'Case is cancelled' : 'Mark case as completed'}
          >
            <X className="h-4 w-4 mr-2" />
            {isClosing ? 'Closing…' : caseData.status === 'completed' ? 'Completed' : 'Close Case'}
          </button>
        </div>
      </div>

      {/* Section Toggle */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'case', label: 'Case Review', icon: FileText },
              { id: 'opinion', label: 'Medical Opinion', icon: Stethoscope }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveSection(tab.id as any)}
                  className={`${
                    activeSection === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeSection === 'case' && (
            <div className="space-y-6">
              {/* Patient Information */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Patient Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Name</span>
                    <p className="text-sm text-gray-900">{caseData.patient.name}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Age</span>
                    <p className="text-sm text-gray-900">{caseData.patient.age} years</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Gender</span>
                    <p className="text-sm text-gray-900">{caseData.patient.gender}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Priority</span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityConfig[caseData.priority].color}`}>
                      {priorityConfig[caseData.priority].label}
                    </span>
                  </div>
                </div>
              </div>

              {/* Case Details */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-6">
                  {/* Symptoms */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Presenting Symptoms</h4>
                    <div className="space-y-2">
                      {caseData.symptoms.map((symptom, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <div className="h-2 w-2 bg-red-400 rounded-full mr-3"></div>
                          {symptom}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Current Medications */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Current Medications</h4>
                    <div className="space-y-2">
                      {caseData.currentMedications.map((medication, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <div className="h-2 w-2 bg-blue-400 rounded-full mr-3"></div>
                          {medication}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Allergies */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Known Allergies</h4>
                    <div className="flex flex-wrap gap-2">
                      {caseData.allergies.map((allergy, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800"
                        >
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {allergy}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Medical History */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Medical History</h4>
                    <div className="bg-white border border-gray-200 rounded-lg p-4">
                      <p className="text-sm text-gray-600 leading-relaxed">{caseData.patient.medicalHistory}</p>
                    </div>
                  </div>

                  {/* Documents */}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Medical Documents</h4>
                    <div className="space-y-3">
                      {caseData.documents.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                              <p className="text-xs text-gray-500">Uploaded {formatDate(doc.uploadedAt)}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button className="text-primary-600 hover:text-primary-900">
                              <Eye className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'opinion' && (
            <div className="space-y-6">
              {/* Primary Diagnosis */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Diagnosis *
                </label>
                <textarea
                  value={opinion.diagnosis}
                  onChange={(e) => setOpinion(prev => ({ ...prev, diagnosis: e.target.value }))}
                  rows={3}
                  className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter your primary diagnosis..."
                />
              </div>

              {/* Clinical Summary */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Clinical Summary *
                </label>
                <textarea
                  value={opinion.summary}
                  onChange={(e) => setOpinion(prev => ({ ...prev, summary: e.target.value }))}
                  rows={5}
                  className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Provide a comprehensive clinical summary of your findings and analysis..."
                />
              </div>

              {/* Confidence Level and Urgency */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Confidence Level
                  </label>
                  <select
                    value={opinion.confidenceLevel}
                    onChange={(e) => setOpinion(prev => ({ ...prev, confidenceLevel: e.target.value as any }))}
                    className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="high">High Confidence</option>
                    <option value="medium">Medium Confidence</option>
                    <option value="low">Low Confidence</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Urgency Level
                  </label>
                  <select
                    value={opinion.urgencyLevel}
                    onChange={(e) => setOpinion(prev => ({ ...prev, urgencyLevel: e.target.value as any }))}
                    className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="routine">Routine Follow-up</option>
                    <option value="urgent">Urgent Follow-up</option>
                    <option value="immediate">Immediate Attention</option>
                  </select>
                </div>
              </div>

              {/* Treatment Recommendations */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Treatment Recommendations *
                  </label>
                  <button
                    onClick={() => addArrayItem('recommendations')}
                    className="inline-flex items-center text-sm text-primary-600 hover:text-primary-900"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Recommendation
                  </button>
                </div>
                <div className="space-y-3">
                  {opinion.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="flex-1">
                        <textarea
                          value={rec}
                          onChange={(e) => updateArrayItem('recommendations', index, e.target.value)}
                          rows={2}
                          className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                          placeholder={`Recommendation ${index + 1}...`}
                        />
                      </div>
                      {opinion.recommendations.length > 1 && (
                        <button
                          onClick={() => removeArrayItem('recommendations', index)}
                          className="text-red-600 hover:text-red-900 mt-2"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Differential Diagnosis */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Differential Diagnosis
                  </label>
                  <button
                    onClick={() => addArrayItem('differentialDiagnosis')}
                    className="inline-flex items-center text-sm text-primary-600 hover:text-primary-900"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Alternative
                  </button>
                </div>
                <div className="space-y-3">
                  {opinion.differentialDiagnosis.map((diagnosis, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="flex-1">
                        <input
                          type="text"
                          value={diagnosis}
                          onChange={(e) => updateArrayItem('differentialDiagnosis', index, e.target.value)}
                          className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                          placeholder={`Alternative diagnosis ${index + 1}...`}
                        />
                      </div>
                      {opinion.differentialDiagnosis.length > 1 && (
                        <button
                          onClick={() => removeArrayItem('differentialDiagnosis', index)}
                          className="text-red-600 hover:text-red-900 mt-2"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Additional Tests */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Recommended Additional Tests
                  </label>
                  <button
                    onClick={() => addArrayItem('additionalTests')}
                    className="inline-flex items-center text-sm text-primary-600 hover:text-primary-900"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Test
                  </button>
                </div>
                <div className="space-y-3">
                  {opinion.additionalTests.map((test, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="flex-1">
                        <input
                          type="text"
                          value={test}
                          onChange={(e) => updateArrayItem('additionalTests', index, e.target.value)}
                          className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                          placeholder={`Additional test ${index + 1}...`}
                        />
                      </div>
                      {opinion.additionalTests.length > 1 && (
                        <button
                          onClick={() => removeArrayItem('additionalTests', index)}
                          className="text-red-600 hover:text-red-900 mt-2"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Referrals */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Specialist Referrals
                  </label>
                  <button
                    onClick={() => addArrayItem('referrals')}
                    className="inline-flex items-center text-sm text-primary-600 hover:text-primary-900"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Referral
                  </button>
                </div>
                <div className="space-y-3">
                  {opinion.referrals.map((referral, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="flex-1">
                        <input
                          type="text"
                          value={referral}
                          onChange={(e) => updateArrayItem('referrals', index, e.target.value)}
                          className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                          placeholder={`Specialist referral ${index + 1}...`}
                        />
                      </div>
                      {opinion.referrals.length > 1 && (
                        <button
                          onClick={() => removeArrayItem('referrals', index)}
                          className="text-red-600 hover:text-red-900 mt-2"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Prognosis */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Prognosis
                </label>
                <textarea
                  value={opinion.prognosis}
                  onChange={(e) => setOpinion(prev => ({ ...prev, prognosis: e.target.value }))}
                  rows={3}
                  className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Provide your assessment of the patient's likely outcome and recovery..."
                />
              </div>

              {/* Follow-up */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="followUpRequired"
                    type="checkbox"
                    checked={opinion.followUpRequired}
                    onChange={(e) => setOpinion(prev => ({ ...prev, followUpRequired: e.target.checked }))}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="followUpRequired" className="ml-2 block text-sm text-gray-900">
                    Follow-up appointment required
                  </label>
                </div>
                {opinion.followUpRequired && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Follow-up Instructions
                    </label>
                    <textarea
                      value={opinion.followUpInstructions}
                      onChange={(e) => setOpinion(prev => ({ ...prev, followUpInstructions: e.target.value }))}
                      rows={3}
                      className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Specify follow-up timeline and instructions..."
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
