import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Clock, Eye, Edit, Filter, Search } from 'lucide-react'
import { PageHeader } from '@/components/ui/page-header'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { apiClient } from '@/services/api'

interface PendingOpinion {
  id: string
  caseId: string
  patientName: string
  specialty: string
  urgency: 'low' | 'medium' | 'high'
  submittedDate: string
  dueDate: string
  status: 'pending' | 'in_progress' | 'draft'
  description: string
}

export function PendingOpinions() {
  const navigate = useNavigate()
  const [opinions, setOpinions] = useState<PendingOpinion[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [urgencyFilter, setUrgencyFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    fetchPendingOpinions()
  }, [])

  const fetchPendingOpinions = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Get cases that specifically need medical opinions
      // These are cases where the doctor has been requested to provide expert opinions
      const casesResponse = await apiClient.getCases({
        status: 'pending_opinion' // Only cases specifically needing medical opinions
      })
      const cases = casesResponse.data || []
      
      // Transform cases into pending opinions format
      const pendingOpinions: PendingOpinion[] = []
      
      for (const caseItem of cases) {
        try {
          // Check if this case already has medical opinions from this doctor
          const existingOpinions = await apiClient.getMedicalOpinions(caseItem.id)
          const doctorHasOpinion = Array.isArray(existingOpinions) &&
            existingOpinions.some(opinion => opinion.doctorId === caseItem.assignedDoctorId)
          
          // Only include if doctor hasn't provided opinion yet
          if (!doctorHasOpinion) {
            pendingOpinions.push({
              id: caseItem.id,
              caseId: caseItem.id,
              patientName: caseItem.patient?.name || caseItem.patientName || 'Unknown Patient',
              specialty: caseItem.specialty || 'General Medicine',
              urgency: caseItem.priority?.toLowerCase() || 'medium',
              submittedDate: caseItem.createdAt || new Date().toISOString(),
              dueDate: caseItem.opinionDueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
              status: 'pending',
              description: caseItem.opinionRequest || caseItem.description || caseItem.chiefComplaint || 'Medical opinion requested'
            })
          }
        } catch (opinionErr) {
          // If we can't check opinions, include the case anyway
          pendingOpinions.push({
            id: caseItem.id,
            caseId: caseItem.id,
            patientName: caseItem.patient?.name || caseItem.patientName || 'Unknown Patient',
            specialty: caseItem.specialty || 'General Medicine',
            urgency: caseItem.priority?.toLowerCase() || 'medium',
            submittedDate: caseItem.createdAt || new Date().toISOString(),
            dueDate: caseItem.opinionDueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            status: 'pending',
            description: caseItem.opinionRequest || caseItem.description || caseItem.chiefComplaint || 'Medical opinion requested'
          })
        }
      }
      
      setOpinions(pendingOpinions)
    } catch (err: any) {
      // TODO: Replace with proper error reporting
  console.error('Error fetching pending opinions:', err)
      setError(err.message || 'Failed to fetch pending opinions')
    } finally {
      setLoading(false)
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'in_progress': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'draft': return 'text-gray-600 bg-gray-50 border-gray-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const filteredOpinions = opinions.filter(opinion => {
    const matchesSearch = opinion.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opinion.specialty.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opinion.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesUrgency = urgencyFilter === 'all' || opinion.urgency === urgencyFilter
    const matchesStatus = statusFilter === 'all' || opinion.status === statusFilter
    
    return matchesSearch && matchesUrgency && matchesStatus
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Pending Opinions"
        description="Review and complete medical opinions awaiting your expertise"
      />

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search by patient, specialty, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Urgency Filter */}
          <div className="sm:w-48">
            <select
              value={urgencyFilter}
              onChange={(e) => setUrgencyFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">All Urgencies</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>

          {/* Status Filter */}
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="draft">Draft</option>
            </select>
          </div>
        </div>
      </div>

      {/* Opinions List */}
      <div className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {filteredOpinions.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No pending opinions</h3>
            <p className="text-gray-500">
              {searchTerm || urgencyFilter !== 'all' || statusFilter !== 'all'
                ? 'No opinions match your current filters.'
                : 'You have no pending medical opinions at this time.'}
            </p>
          </div>
        ) : (
          filteredOpinions.map((opinion) => (
            <div key={opinion.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {opinion.patientName}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(opinion.urgency)}`}>
                      {opinion.urgency.charAt(0).toUpperCase() + opinion.urgency.slice(1)} Priority
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(opinion.status)}`}>
                      {opinion.status.replace('_', ' ').charAt(0).toUpperCase() + opinion.status.replace('_', ' ').slice(1)}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-500">Specialty</p>
                      <p className="font-medium text-gray-900">{opinion.specialty}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Submitted</p>
                      <p className="font-medium text-gray-900">
                        {new Date(opinion.submittedDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Due Date</p>
                      <p className="font-medium text-gray-900">
                        {new Date(opinion.dueDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4">{opinion.description}</p>
                </div>

                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => navigate(`/cases/${opinion.caseId}`)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Case
                  </button>
                  <button
                    onClick={() => navigate(`/doctor/opinions/create/${opinion.caseId}`)}
                    className="inline-flex items-center px-3 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Start Opinion
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}