import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Link } from 'react-router-dom'
import { apiClient } from '@/services/api'
import {
  Calendar,
  Clock,
  CheckCircle,
  Star,
  Clipboard,
  FileText
} from 'lucide-react'
import { DashboardCard } from '@/components/ui/dashboard-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { PageHeader } from '@/components/ui/page-header'
import { ErrorAlert } from '@/components/ui/error-alert'
import { layouts, commonClasses } from '@/constants/theme'

interface DoctorStats {
  pendingReviews: number
  completedReviews: number
  averageRating: number
  totalCases: number
}

export function DoctorDashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DoctorStats>({
    pendingReviews: 0,
    completedReviews: 0,
    averageRating: 4.8,
    totalCases: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch doctor's assigned cases using apiClient
      const casesResponse = await apiClient.getDoctorCases()
      const cases = casesResponse || []
      
      // Calculate stats from cases data
      const pendingReviews = cases.filter(c => c.status === 'assigned').length
      const completedReviews = cases.filter(c => c.status === 'completed').length
      
      setStats({
        pendingReviews,
        completedReviews,
        averageRating: 4.8, // This would come from a ratings API
        totalCases: cases.length
      })

    } catch (err) {
      // TODO: Replace with proper error reporting
  console.error('Error fetching dashboard data:', err)
      setError('Failed to load dashboard data. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <LoadingSpinner
        fullScreen
        text="Loading your doctor dashboard..."
        size="xl"
      />
    )
  }

  return (
    <div className={layouts.page}>
      <div className={`${layouts.container} ${layouts.pageContent}`}>
        {/* Error Alert */}
        {error && (
          <ErrorAlert
            variant="error"
            title="Dashboard Error"
            message={error}
            dismissible
            onDismiss={() => setError(null)}
          />
        )}

        {/* Welcome Section */}
        <PageHeader
          title={`Welcome back, ${user?.firstName || 'Doctor'}! 👨‍⚕️`}
          description="Review patient cases and provide expert medical opinions."
          action={
            <Link
              to="/doctor/appointments"
              className={commonClasses.primaryButton}
            >
              <Calendar className="mr-2 h-4 w-4" />
              View Appointments
            </Link>
          }
        />

        {/* Stats Cards */}
        <div className={layouts.gridCols4}>
          <DashboardCard
            title="Pending Reviews"
            value={stats.pendingReviews}
            description="Cases awaiting your review"
            icon={Clock}
            variant="gradient"
            color="blue"
          />

          <DashboardCard
            title="Completed Reviews"
            value={stats.completedReviews}
            description="Cases you've reviewed"
            icon={CheckCircle}
            variant="gradient"
            color="green"
          />

          <DashboardCard
            title="Average Rating"
            value={`${stats.averageRating}/5.0`}
            description="Patient satisfaction score"
            icon={Star}
            variant="gradient"
            color="yellow"
          />

          <DashboardCard
            title="Total Cases"
            value={stats.totalCases}
            description="All assigned cases"
            icon={FileText}
            variant="gradient"
            color="purple"
          />
        </div>

        {/* Case Queue Section */}
        <PageHeader
          title="📋 Case Queue"
          description="Review and manage your assigned patient cases"
          centered={false}
          action={
            <Link
              to="/cases"
              className={commonClasses.secondaryButton}
            >
              <Clipboard className="mr-2 h-4 w-4" />
              View All Cases
            </Link>
          }
        />

        {stats.pendingReviews === 0 ? (
          <div className={commonClasses.card}>
            <div className="text-center py-12">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Clipboard className="w-6 h-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No cases assigned</h3>
              <p className="text-gray-500">
                New cases will appear here when they match your specialization.
              </p>
            </div>
          </div>
        ) : (
          <div className={commonClasses.card}>
            <div className="p-6">
              <p className="text-gray-600">
                You have {stats.pendingReviews} case{stats.pendingReviews !== 1 ? 's' : ''} waiting for your review.
              </p>
              <Link
                to="/cases"
                className={`${commonClasses.primaryButton} mt-4 inline-flex`}
              >
                Review Cases
              </Link>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <PageHeader
          title="🚀 Quick Actions"
          description="Common tasks for efficient case management"
          centered={true}
        />

        <div className={layouts.gridCols3}>
          <Link
            to="/cases"
            className="group bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border-2 border-blue-200 hover:border-blue-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <Clipboard className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-blue-900 mb-2">Review Cases</h4>
            <p className="text-sm text-blue-700 leading-relaxed">Review assigned patient cases and provide medical opinions</p>
          </Link>
          
          <Link
            to="/doctor/appointments"
            className="group bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border-2 border-green-200 hover:border-green-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-green-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <Calendar className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-green-900 mb-2">My Schedule</h4>
            <p className="text-sm text-green-700 leading-relaxed">View and manage your upcoming appointments</p>
          </Link>
          
          <Link
            to="/crm/organizations"
            className="group bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border-2 border-purple-200 hover:border-purple-300 rounded-xl p-6 text-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-purple-500 rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg">
              <FileText className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-bold text-purple-900 mb-2">CRM</h4>
            <p className="text-sm text-purple-700 leading-relaxed">Manage organizations and professional contacts</p>
          </Link>
        </div>
      </div>
    </div>
  )
}
