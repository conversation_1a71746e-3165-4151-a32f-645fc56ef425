import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Clock, Eye, Edit, Search, Timer, Play, Pause, User, Users } from 'lucide-react'
import { PageHeader } from '@/components/ui/page-header'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { apiClient } from '@/services/api'

interface WorkloadItem {
  id: string
  caseId: string
  title: string
  patientSex: string
  patientAge: number
  patientRace: string
  specialty: string
  urgency: 'low' | 'medium' | 'high'
  acceptedDate: string
  dueDate: string
  status: 'active' | 'paused' | 'completed'
  description: string
  timeSpent: number // in minutes
  elapsedTime: number // minutes since acceptance
}

export function CurrentWorkload() {
  const navigate = useNavigate()
  const [workloadItems, setWorkloadItems] = useState<WorkloadItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [urgencyFilter, setUrgencyFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    fetchCurrentWorkload()
    
    // Update elapsed time every minute
    const interval = setInterval(() => {
      setWorkloadItems(prev => prev.map(item => {
        const now = Date.now()
        const acceptedTime = new Date(item.acceptedDate).getTime()
        const elapsedMinutes = Math.floor((now - acceptedTime) / (1000 * 60))
        return {
          ...item,
          elapsedTime: elapsedMinutes
        }
      }))
    }, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [])

  const fetchCurrentWorkload = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Get doctor's assigned cases and filter for only 'in_review' status
      const allCases = await apiClient.getDoctorCases()
      const cases = (allCases || []).filter(case_ => case_.status === 'in_review')
      
      // Transform cases into workload items
      const workloadItems: WorkloadItem[] = cases
        .map(caseItem => {
          const acceptedDate = caseItem.acceptedAt || caseItem.createdAt || new Date().toISOString()
          const now = Date.now()
          const acceptedTime = new Date(acceptedDate).getTime()
          const elapsedMinutes = Math.floor((now - acceptedTime) / (1000 * 60))
          
          return {
            id: caseItem.id,
            caseId: caseItem.id,
            title: caseItem.title || 'Medical Case Review',
            patientSex: caseItem.patientGender || 'Not specified',
            patientAge: caseItem.patientAge || 0,
            patientRace: caseItem.patientRace || 'Not specified',
            specialty: caseItem.specialtyRequired || 'General Medicine',
            urgency: caseItem.urgencyLevel?.toLowerCase() || 'medium',
            acceptedDate: acceptedDate,
            dueDate: caseItem.dueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            status: 'active',
            description: caseItem.description || 'Active case in workload',
            timeSpent: caseItem.timeSpent || 0, // Time in minutes
            elapsedTime: elapsedMinutes
          }
        })
      
      setWorkloadItems(workloadItems)
    } catch (err: any) {
      // TODO: Replace with proper error reporting
  console.error('Error fetching current workload:', err)
      setError(err.message || 'Failed to fetch current workload')
    } finally {
      setLoading(false)
    }
  }

  // Helper function to format elapsed time
  const formatElapsedTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    const days = Math.floor(hours / 24)
    const remainingHours = hours % 24
    
    if (days > 0) {
      return `${days}d ${remainingHours}h ${mins}m`
    } else if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50 border-green-200'
      case 'paused': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'completed': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  // No filtering for doctors - show all their in_review cases
  const filteredItems = workloadItems

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Current Workload"
        description="Track and manage your active cases with time monitoring"
      />

      {/* Simple header for Current Workload */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Active Cases</h3>
            <p className="text-sm text-gray-600">Cases you are currently reviewing</p>
          </div>
          <div className="text-sm text-gray-500">
            {filteredItems.length} case{filteredItems.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Workload Items */}
      <div className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {filteredItems.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <Timer className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No active workload</h3>
            <p className="text-gray-500">
              You have no active cases in your workload at this time.
            </p>
          </div>
        ) : (
          filteredItems.map((item) => (
            <div key={item.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {item.title}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(item.urgency)}`}>
                      {item.urgency.charAt(0).toUpperCase() + item.urgency.slice(1)} Priority
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(item.status)}`}>
                      {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div className="flex items-center gap-2">
                        {item.patientSex && item.patientSex !== 'Not specified' && (
                          <>
                            {item.patientSex.toLowerCase() === 'male' || item.patientSex.toLowerCase() === 'm' ? (
                              <User className="h-4 w-4 text-blue-600" />
                            ) : item.patientSex.toLowerCase() === 'female' || item.patientSex.toLowerCase() === 'f' ? (
                              <Users className="h-4 w-4 text-pink-600" />
                            ) : (
                              <User className="h-4 w-4 text-gray-600" />
                            )}
                            <span className="font-medium text-gray-900">
                              {item.patientSex.charAt(0).toUpperCase()}{item.patientAge > 0 ? `, ${item.patientAge}` : ''}{item.patientRace && item.patientRace !== 'Not specified' ? `, ${item.patientRace}` : ''}
                            </span>
                          </>
                        )}
                        {(!item.patientSex || item.patientSex === 'Not specified') && (
                          <span className="text-sm text-gray-500">Demographics not available</span>
                        )}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Accepted</p>
                      <p className="font-medium text-gray-900">
                        {new Date(item.acceptedDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Time Spent</p>
                      <p className="font-medium text-gray-900">
                        {formatTime(item.timeSpent)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Elapsed Time</p>
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-gray-900">
                          {formatElapsedTime(item.elapsedTime)}
                        </p>
                        <div className="flex items-center text-blue-600">
                          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse mr-1"></div>
                          <span className="text-xs">Active</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600">{item.specialty}</p>
                  </div>

                  <p className="text-gray-600 mb-4">{item.description}</p>
                </div>

                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => navigate(`/cases/${item.caseId}`)}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Review Case
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}