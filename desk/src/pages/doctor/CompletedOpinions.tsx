import React, { useState, useEffect } from 'react'
import { CheckCircle, Eye, Download, Filter, Search, Calendar } from 'lucide-react'
import { PageHeader } from '@/components/ui/page-header'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { apiClient } from '@/services/api'

interface CompletedOpinion {
  id: string
  caseId: string
  patientName: string
  specialty: string
  urgency: 'low' | 'medium' | 'high'
  submittedDate: string
  completedDate: string
  status: 'completed' | 'delivered' | 'reviewed'
  description: string
  opinionSummary: string
  rating?: number
}

export function CompletedOpinions() {
  const [opinions, setOpinions] = useState<CompletedOpinion[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [specialtyFilter, setSpecialtyFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateRange, setDateRange] = useState<string>('all')

  useEffect(() => {
    fetchCompletedOpinions()
  }, [])

  const fetchCompletedOpinions = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Get doctor's assigned cases
      const casesResponse = await apiClient.getDoctorCases()
      const cases = Array.isArray(casesResponse) ? casesResponse : []
      
      // Get completed cases and their medical opinions
      const completedCases = cases.filter(caseItem =>
        caseItem.status === 'completed' ||
        caseItem.status === 'closed' ||
        caseItem.status === 'delivered'
      )
      
      // For each completed case, get the medical opinions
      const completedOpinions: CompletedOpinion[] = []
      
      for (const caseItem of completedCases) {
        try {
          const opinionsResponse = await apiClient.getMedicalOpinions(caseItem.id)
          const opinions = Array.isArray(opinionsResponse) ? opinionsResponse : []
          
          // Transform each opinion into the expected format
          opinions.forEach(opinion => {
            completedOpinions.push({
              id: opinion.id,
              caseId: caseItem.id,
              patientName: caseItem.patient?.name || caseItem.patientName || 'Unknown Patient',
              specialty: caseItem.specialty || opinion.specialty || 'General Medicine',
              urgency: caseItem.priority?.toLowerCase() || 'medium',
              submittedDate: caseItem.createdAt || caseItem.assignedAt || new Date().toISOString(),
              completedDate: opinion.completedAt || opinion.updatedAt || new Date().toISOString(),
              status: opinion.status === 'approved' ? 'delivered' :
                     opinion.status === 'reviewed' ? 'reviewed' : 'completed',
              description: caseItem.description || caseItem.chiefComplaint || 'Medical opinion completed',
              opinionSummary: opinion.summary || opinion.content || 'Opinion provided',
              rating: opinion.rating || undefined
            })
          })
        } catch (opinionErr) {
          // If we can't get opinions for a case, still show the case as completed
          completedOpinions.push({
            id: caseItem.id,
            caseId: caseItem.id,
            patientName: caseItem.patient?.name || caseItem.patientName || 'Unknown Patient',
            specialty: caseItem.specialty || 'General Medicine',
            urgency: caseItem.priority?.toLowerCase() || 'medium',
            submittedDate: caseItem.createdAt || caseItem.assignedAt || new Date().toISOString(),
            completedDate: caseItem.completedAt || caseItem.updatedAt || new Date().toISOString(),
            status: 'completed',
            description: caseItem.description || caseItem.chiefComplaint || 'Case completed',
            opinionSummary: 'Opinion completed - details may be available in case notes'
          })
        }
      }
      
      setOpinions(completedOpinions)
    } catch (err: any) {
      // TODO: Replace with proper error reporting
  console.error('Error fetching completed opinions:', err)
      setError(err.message || 'Failed to fetch completed opinions')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'delivered': return 'text-green-600 bg-green-50 border-green-200'
      case 'reviewed': return 'text-purple-600 bg-purple-50 border-purple-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-sm ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ))
  }

  const filteredOpinions = opinions.filter(opinion => {
    const matchesSearch = opinion.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opinion.specialty.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opinion.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSpecialty = specialtyFilter === 'all' || opinion.specialty.toLowerCase() === specialtyFilter.toLowerCase()
    const matchesStatus = statusFilter === 'all' || opinion.status === statusFilter
    
    let matchesDate = true
    if (dateRange !== 'all') {
      const completedDate = new Date(opinion.completedDate)
      const now = new Date()
      const daysAgo = parseInt(dateRange)
      const cutoffDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000)
      matchesDate = completedDate >= cutoffDate
    }
    
    return matchesSearch && matchesSpecialty && matchesStatus && matchesDate
  })

  const uniqueSpecialties = Array.from(new Set(opinions.map(o => o.specialty)))

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Completed Opinions"
        description="Review your completed medical opinions and patient feedback"
      />

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search by patient, specialty, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Specialty Filter */}
          <div>
            <select
              value={specialtyFilter}
              onChange={(e) => setSpecialtyFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">All Specialties</option>
              {uniqueSpecialties.map(specialty => (
                <option key={specialty} value={specialty}>{specialty}</option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="delivered">Delivered</option>
              <option value="reviewed">Reviewed</option>
            </select>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex items-center gap-4">
            <Calendar className="h-4 w-4 text-gray-400" />
            <div className="flex gap-2">
              {['all', '7', '30', '90'].map(range => (
                <button
                  key={range}
                  onClick={() => setDateRange(range)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    dateRange === range
                      ? 'bg-primary-100 text-primary-700 border border-primary-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {range === 'all' ? 'All Time' : `Last ${range} days`}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Completed</p>
              <p className="text-2xl font-bold text-gray-900">{opinions.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-bold">★</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Rating</p>
              <p className="text-2xl font-bold text-gray-900">
                {opinions.filter(o => o.rating).length > 0 
                  ? (opinions.filter(o => o.rating).reduce((sum, o) => sum + (o.rating || 0), 0) / opinions.filter(o => o.rating).length).toFixed(1)
                  : 'N/A'
                }
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">This Month</p>
              <p className="text-2xl font-bold text-gray-900">
                {opinions.filter(o => {
                  const completedDate = new Date(o.completedDate)
                  const now = new Date()
                  return completedDate.getMonth() === now.getMonth() && completedDate.getFullYear() === now.getFullYear()
                }).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Opinions List */}
      <div className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {filteredOpinions.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <CheckCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No completed opinions</h3>
            <p className="text-gray-500">
              {searchTerm || specialtyFilter !== 'all' || statusFilter !== 'all' || dateRange !== 'all'
                ? 'No opinions match your current filters.'
                : 'You have no completed medical opinions yet.'}
            </p>
          </div>
        ) : (
          filteredOpinions.map((opinion) => (
            <div key={opinion.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {opinion.patientName}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(opinion.urgency)}`}>
                      {opinion.urgency.charAt(0).toUpperCase() + opinion.urgency.slice(1)} Priority
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(opinion.status)}`}>
                      {opinion.status.charAt(0).toUpperCase() + opinion.status.slice(1)}
                    </span>
                    {opinion.rating && (
                      <div className="flex items-center gap-1">
                        {renderStars(opinion.rating)}
                        <span className="text-sm text-gray-500 ml-1">({opinion.rating}/5)</span>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-500">Specialty</p>
                      <p className="font-medium text-gray-900">{opinion.specialty}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Submitted</p>
                      <p className="font-medium text-gray-900">
                        {new Date(opinion.submittedDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Completed</p>
                      <p className="font-medium text-gray-900">
                        {new Date(opinion.completedDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-1">Case Description</p>
                    <p className="text-gray-600 mb-3">{opinion.description}</p>
                    
                    <p className="text-sm text-gray-500 mb-1">Opinion Summary</p>
                    <p className="text-gray-900 font-medium">{opinion.opinionSummary}</p>
                  </div>
                </div>

                <div className="flex gap-2 ml-4">
                  <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                  <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}