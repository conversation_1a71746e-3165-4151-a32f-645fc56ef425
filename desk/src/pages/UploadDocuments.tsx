import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Upload,
  X,
  FileText,
  Image,
  File,
  AlertCircle,
  CheckCircle,
  ArrowLeft
} from 'lucide-react'

interface UploadFile {
  file: File
  documentType: string
  description: string
}

const documentTypes = [
  { value: 'lab_report', label: 'Lab Report' },
  { value: 'imaging', label: 'Imaging (X-ray, MRI, CT)' },
  { value: 'prescription', label: 'Prescription' },
  { value: 'medical_history', label: 'Medical History' },
  { value: 'insurance', label: 'Insurance Document' },
  { value: 'other', label: 'Other' }
]

const getFileIcon = (file: File) => {
  if (file.type.startsWith('image/')) return Image
  if (file.type === 'application/pdf') return FileText
  return File
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function UploadDocuments() {
  const navigate = useNavigate()
  const { user: _user } = useAuth()
  const [files, setFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({})
  const [uploadResults, setUploadResults] = useState<{ success: string[], failed: string[] }>({ success: [], failed: [] })

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    const newFiles = selectedFiles.map(file => ({
      file,
      documentType: 'other',
      description: ''
    }))
    setFiles(prev => [...prev, ...newFiles])
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const updateFileInfo = (index: number, field: 'documentType' | 'description', value: string) => {
    setFiles(prev => prev.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    ))
  }

  const handleUpload = async () => {
    if (files.length === 0) return

    setIsUploading(true)
    setUploadResults({ success: [], failed: [] })
    
    const results = { success: [] as string[], failed: [] as string[] }

    for (let i = 0; i < files.length; i++) {
      const { file, documentType, description } = files[i]
      
      try {
        setUploadProgress(prev => ({ ...prev, [file.name]: 0 }))
        
        const metadata = {
          title: file.name.split('.')[0], // Use filename without extension as title
          description: description || `Uploaded ${new Date().toLocaleDateString()}`,
          documentType: documentType
        }
        
        // Use the unified upload method - no caseId for general document uploads
        await apiClient.uploadDocument(null, file, metadata)
        
        setUploadProgress(prev => ({ ...prev, [file.name]: 100 }))
        results.success.push(file.name)
      } catch (error: any) {
        // TODO: Replace with proper error reporting
  console.error(`Failed to upload ${file.name}:`, error)
        results.failed.push(file.name)
      }
    }

    setUploadResults(results)
    setIsUploading(false)
    
    if (results.success.length > 0) {
      // Clear successfully uploaded files
      setFiles(prev => prev.filter(item => !results.success.includes(item.file.name)))
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/documents')}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Documents
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Upload Documents</h1>
          <p className="mt-2 text-sm text-gray-600">
            Upload medical documents to your account for easy access and sharing with healthcare providers
          </p>
        </div>

        {/* Upload Area */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="p-6">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="mt-4">
                <label htmlFor="file-upload" className="cursor-pointer">
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    Click to upload files or drag and drop
                  </span>
                  <span className="mt-1 block text-sm text-gray-500">
                    PDF, JPG, PNG up to 10MB each
                  </span>
                </label>
                <input
                  id="file-upload"
                  name="file-upload"
                  type="file"
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={handleFileSelect}
                  className="sr-only"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Files List */}
        {files.length > 0 && (
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Files to Upload ({files.length})
              </h3>
              <div className="space-y-4">
                {files.map((item, index) => {
                  const FileIcon = getFileIcon(item.file)
                  const progress = uploadProgress[item.file.name] || 0
                  
                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center flex-1 min-w-0">
                          <FileIcon className="h-8 w-8 text-gray-400 mr-3 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {item.file.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {formatFileSize(item.file.size)}
                            </div>
                            {isUploading && progress > 0 && (
                              <div className="mt-2">
                                <div className="bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${progress}%` }}
                                  />
                                </div>
                                <div className="text-xs text-gray-500 mt-1">{progress}%</div>
                              </div>
                            )}
                          </div>
                        </div>
                        {!isUploading && (
                          <button
                            onClick={() => removeFile(index)}
                            className="ml-4 text-gray-400 hover:text-red-500"
                          >
                            <X className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                      
                      <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Document Type
                          </label>
                          <select
                            value={item.documentType}
                            onChange={(e) => updateFileInfo(index, 'documentType', e.target.value)}
                            disabled={isUploading}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
                          >
                            {documentTypes.map(type => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Description (Optional)
                          </label>
                          <input
                            type="text"
                            value={item.description}
                            onChange={(e) => updateFileInfo(index, 'description', e.target.value)}
                            disabled={isUploading}
                            placeholder="Brief description"
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
                          />
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        )}

        {/* Upload Results */}
        {(uploadResults.success.length > 0 || uploadResults.failed.length > 0) && (
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Results</h3>
              
              {uploadResults.success.length > 0 && (
                <div className="mb-4">
                  <div className="flex items-center text-green-600 mb-2">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    <span className="font-medium">Successfully uploaded ({uploadResults.success.length})</span>
                  </div>
                  <ul className="text-sm text-gray-600 ml-7">
                    {uploadResults.success.map(filename => (
                      <li key={filename}>{filename}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {uploadResults.failed.length > 0 && (
                <div>
                  <div className="flex items-center text-red-600 mb-2">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    <span className="font-medium">Failed to upload ({uploadResults.failed.length})</span>
                  </div>
                  <ul className="text-sm text-gray-600 ml-7">
                    {uploadResults.failed.map(filename => (
                      <li key={filename}>{filename}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Upload Button */}
        {files.length > 0 && (
          <div className="flex justify-end">
            <button
              onClick={handleUpload}
              disabled={isUploading}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload {files.length} File{files.length > 1 ? 's' : ''}
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
