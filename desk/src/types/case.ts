/**
 * Shared type definitions for case-related data
 */

export interface CaseData {
  id: string;
  title: string;
  patientId: string;
  patient?: {
    id: string;
    name: string;
  };
  description: string;
  status: CaseStatus;
  urgencyLevel: 'low' | 'medium' | 'high' | 'urgent';
  symptoms: string;
  medicalHistory: string;
  currentMedications: string;
  createdAt: string;
  updatedAt: string;
  // Patient demographic information
  patientGender?: string;
  patientDateOfBirth?: string;
  patientCountry?: string;
  assignedDoctors?: {
    id: string;
    name: string;
    specialization: string;
    assignedAt?: string;
  }[];
  documents?: {
    id: string;
    title: string;
    fileName: string;
    createdAt: string;
  }[];
  structuredContent?: any; // For case metadata
}

// Unified case status type that includes all possible statuses
export type CaseStatus = 
  | 'draft' 
  | 'submitted' 
  | 'in_review' 
  | 'assigned' 
  | 'in_progress' 
  | 'completed' 
  | 'archived'
  | 'cancelled';
