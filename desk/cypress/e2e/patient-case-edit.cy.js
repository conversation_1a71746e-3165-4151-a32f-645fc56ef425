describe('Patient Case Edit Permissions', () => {
  let testCase

  before(() => {
    // Set up test data
    testCase = {
      title: 'Test Case for Patient Editing',
      description: 'This is a test case to validate patient editing permissions'
    }
  })

  describe('1. Authentication Flow', () => {
    it('should display login page with patient quick access button', () => {
      cy.visit('/')
      
      // Verify login page loads
      cy.url().should('include', '/')
      cy.get('body').should('be.visible')
      
      // Check for patient login button
      cy.get('[data-testid="patient-login-button"]', { timeout: 10000 })
        .should('be.visible')
        .should('contain', 'Patient')
    })

    it('should successfully login as patient using quick access', () => {
      cy.loginAsPatient()
      
      // Verify successful login and redirect to patient dashboard
      cy.url().should('include', '/patient')
      cy.get('[data-testid="patient-dashboard"]', { timeout: 10000 })
        .should('be.visible')
      
      // Verify patient authentication token is stored
      cy.window().its('localStorage')
        .invoke('getItem', 'token')
        .should('exist')
      
      // Verify user data is stored
      cy.window().its('localStorage')
        .invoke('getItem', 'user')
        .should('exist')
        .then((userStr) => {
          const user = JSON.parse(userStr)
          expect(user.role).to.equal('patient')
          expect(user.email).to.equal('<EMAIL>')
        })
    })

    it('should maintain patient session across page refreshes', () => {
      // Login as patient
      cy.get('[data-testid="patient-login-button"]').click()
      cy.url().should('include', '/patient')
      
      // Refresh the page
      cy.reload()
      
      // Verify still logged in
      cy.url().should('include', '/patient')
      cy.window().its('localStorage')
        .invoke('getItem', 'token')
        .should('exist')
    })
  })

  describe('Patient Edit Permissions', () => {
    it('should allow patient to see edit button on their own case', () => {
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
      cy.checkEditButtonVisibility(true)
    })

    it('should allow patient to edit allowed fields', () => {
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
      
      // Enter edit mode
      cy.enterEditMode()
      
      // Edit allowed fields
      cy.get('[data-testid="case-title-input"]').clear().type('Updated Case Title by Patient')
      cy.get('[data-testid="case-description-textarea"]').clear().type('Updated description by patient')
      cy.get('[data-testid="case-symptoms-textarea"]').clear().type('Updated symptoms by patient')
      cy.get('[data-testid="case-medical-history-textarea"]').clear().type('Updated medical history by patient')
      cy.get('[data-testid="case-medications-textarea"]').clear().type('Updated medications by patient')
      
      // Save changes
      cy.saveChanges()
      
      // Verify changes were saved
      cy.contains('Updated Case Title by Patient').should('be.visible')
      cy.contains('Updated description by patient').should('be.visible')
    })

    it('should not show status and urgency dropdowns for patients in edit mode', () => {
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
      
      // Enter edit mode
      cy.enterEditMode()
      
      // Status and urgency dropdowns should not be visible for patients
      cy.get('[data-testid="case-status-select"]').should('not.exist')
      cy.get('[data-testid="case-urgency-select"]').should('not.exist')
      
      // But status badges should still be visible (read-only)
      cy.get('[data-testid="case-status-badge"]').should('be.visible')
      cy.get('[data-testid="case-urgency-badge"]').should('be.visible')
    })

    it('should show appropriate edit controls for patient-editable fields', () => {
      cy.enterEditMode()
      
      // Verify patient can see input fields for allowed fields
      cy.get('[data-testid="case-title-input"]').should('be.visible').should('not.be.disabled')
      cy.get('[data-testid="case-description-textarea"]').should('be.visible').should('not.be.disabled')
      cy.get('[data-testid="case-symptoms-textarea"]').should('be.visible').should('not.be.disabled')
      cy.get('[data-testid="case-medical-history-textarea"]').should('be.visible').should('not.be.disabled')
      cy.get('[data-testid="case-medications-textarea"]').should('be.visible').should('not.be.disabled')
      
      // Verify restricted fields are not editable (no input controls)
      cy.get('[data-testid="case-status-select"]').should('not.exist')
      cy.get('[data-testid="case-urgency-select"]').should('not.exist')
      cy.get('[data-testid="case-assigned-doctor-select"]').should('not.exist')
    })
  })

  describe('4. Patient Edit Workflow', () => {
    beforeEach(() => {
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
    })

    it('should successfully edit and save patient-allowed fields', () => {
      cy.enterEditMode()
      
      // Edit allowed fields
      cy.get('[data-testid="case-title-input"]').clear().type('Updated Case Title by Patient')
      cy.get('[data-testid="case-description-textarea"]').clear().type('Updated description by patient')
      cy.get('[data-testid="case-symptoms-textarea"]').clear().type('Updated symptoms by patient')
      
      // Save changes
      cy.saveChanges()
      
      // Verify changes were saved and are visible
      cy.get('[data-testid="case-title"]').should('contain', 'Updated Case Title by Patient')
      cy.get('[data-testid="case-description"]').should('contain', 'Updated description by patient')
      cy.get('[data-testid="case-symptoms"]').should('contain', 'Updated symptoms by patient')
    })

    it('should allow canceling edit without saving changes', () => {
      // Remember original title
      cy.get('[data-testid="case-title"]').invoke('text').as('originalTitle')
      
      cy.enterEditMode()
      
      // Make changes
      cy.get('[data-testid="case-title-input"]').clear().type('Temporary Title Change')
      
      // Cancel edit
      cy.cancelEdit()
      
      // Verify original title is restored
      cy.get('@originalTitle').then((originalTitle) => {
        cy.get('[data-testid="case-title"]').should('contain', originalTitle)
      })
    })

    it('should show save and cancel buttons in edit mode', () => {
      cy.enterEditMode()
      
      // Verify edit mode buttons are visible
      cy.get('[data-testid="case-save-button"]').should('be.visible').should('contain', 'Save')
      cy.get('[data-testid="case-cancel-button"]').should('be.visible').should('contain', 'Cancel')
      
      // Verify edit button is hidden in edit mode
      cy.get('[data-testid="case-edit-button"]').should('not.exist')
    })
  })

  describe('5. Patient UI Validation', () => {
    it('should use permission-based checks for patient role', () => {
      // Test that the UI uses the permission system correctly for patients
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
      
      // Check that edit button appears based on patient permissions
      cy.window().then((win) => {
        // Verify the permission hook is being used
        cy.get('[data-testid="case-edit-button"]').should('be.visible')
        
        // The button should be labeled simply as "Edit" (not role-specific text)
        cy.get('[data-testid="case-edit-button"]').should('contain', 'Edit')
        cy.get('[data-testid="case-edit-button"]').should('not.contain', 'Update My Case')
      })
    })

    it('should handle permission checks gracefully when case data is loading', () => {
      cy.loginAsPatient()
      
      // Intercept the case API call to simulate loading state
      cy.intercept('GET', '/api/cases/*', { delay: 1000 }).as('getCaseDetails')
      
      cy.navigateToCase(testCase.title)
      
      // Edit button should not appear during loading
      cy.get('[data-testid="case-edit-button"]').should('not.exist')
      
      // Wait for API call to complete
      cy.wait('@getCaseDetails')
      
      // Edit button should appear after loading
      cy.get('[data-testid="case-edit-button"]').should('be.visible')
    })
  })

  describe('Patient Error Handling and Edge Cases', () => {
    it('should handle API errors gracefully during patient case updates', () => {
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
      
      // Intercept update API to simulate error
      cy.intercept('PUT', '/api/cases/*', { statusCode: 500, body: { error: 'Server error' } }).as('updateCaseError')
      
      cy.enterEditMode()
      cy.get('[data-testid="case-title-input"]').clear().type('This will fail')
      cy.get('[data-testid="case-save-button"]').click()
      
      // Should show error message
      cy.get('[data-testid="error-message"]').should('be.visible')
      cy.get('[data-testid="error-message"]').should('contain', 'Failed to update case')
    })

    it('should prevent concurrent edits and show appropriate feedback for patients', () => {
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
      
      cy.enterEditMode()
      
      // Simulate saving state
      cy.get('[data-testid="case-save-button"]').click()
      
      // Save button should be disabled during save
      cy.get('[data-testid="case-save-button"]').should('be.disabled')
      cy.get('[data-testid="case-save-button"]').should('contain', 'Saving...')
    })

    it('should validate patient can only access their own cases', () => {
      cy.loginAsPatient()
      
      // Try to access a case that doesn't belong to the patient
      cy.visit('/cases/invalid-case-id', { failOnStatusCode: false })
      
      // Should show appropriate error or redirect
      cy.url().should('not.contain', '/cases/invalid-case-id')
      // Should either redirect to cases list or show 404/403 error
      cy.get('body').should('contain.oneOf', ['Not Found', 'Access Denied', 'Cases'])
    })

    it('should maintain patient session throughout case editing workflow', () => {
      cy.loginAsPatient()
      cy.navigateToCase(testCase.title)
      
      // Verify patient is still logged in after navigation
      cy.window().its('localStorage').invoke('getItem', 'auth').should('exist')
      
      // Enter edit mode
      cy.enterEditMode()
      
      // Make changes
      cy.get('[data-testid="case-title-input"]').clear().type('Session Test Title')
      
      // Save changes
      cy.saveChanges()
      
      // Verify patient session is still valid after save
      cy.window().its('localStorage').invoke('getItem', 'auth').should('exist')
      cy.get('[data-testid="case-edit-button"]').should('be.visible')
    })
  })
})
