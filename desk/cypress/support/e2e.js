// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Custom commands for Continuia app
Cypress.Commands.add('loginAsPatient', () => {
  cy.visit('/')
  cy.get('[data-testid="patient-login-button"]').click()
  cy.url().should('include', '/dashboard')
})

Cypress.Commands.add('loginAsDoctor', () => {
  cy.visit('/')
  cy.get('[data-testid="doctor-login-button"]').click()
  cy.url().should('include', '/dashboard')
})

Cypress.Commands.add('loginAsAdmin', () => {
  cy.visit('/')
  cy.get('[data-testid="admin-login-button"]').click()
  cy.url().should('include', '/dashboard')
})

Cypress.Commands.add('navigateToCase', (caseTitle) => {
  cy.get('[data-testid="cases-nav"]').click()
  cy.contains(caseTitle).click()
})

Cypress.Commands.add('checkEditButtonVisibility', (shouldBeVisible) => {
  if (shouldBeVisible) {
    cy.get('[data-testid="case-edit-button"]').should('be.visible')
  } else {
    cy.get('[data-testid="case-edit-button"]').should('not.exist')
  }
})

Cypress.Commands.add('enterEditMode', () => {
  cy.get('[data-testid="case-edit-button"]').click()
  cy.get('[data-testid="case-save-button"]').should('be.visible')
})

Cypress.Commands.add('saveChanges', () => {
  cy.get('[data-testid="case-save-button"]').click()
  cy.get('[data-testid="case-edit-button"]').should('be.visible')
})
