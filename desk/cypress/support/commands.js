// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

// Custom commands for UI interactions
Cypress.Commands.add('loginAsPatient', () => {
  cy.visit('/')
  cy.get('[data-testid="patient-login-button"]', { timeout: 10000 })
    .should('be.visible')
    .click()
  
  // Wait for redirect to patient dashboard
  cy.url().should('include', '/patient', { timeout: 10000 })
  cy.get('[data-testid="patient-dashboard"]', { timeout: 10000 })
    .should('be.visible')
})

Cypress.Commands.add('loginAsDoctor', () => {
  cy.visit('/')
  cy.get('[data-testid="doctor-login-button"]', { timeout: 10000 })
    .should('be.visible')
    .click()
  
  // Wait for redirect to doctor dashboard
  cy.url().should('include', '/doctor', { timeout: 10000 })
})

Cypress.Commands.add('loginAsAdmin', () => {
  cy.visit('/')
  cy.get('[data-testid="admin-login-button"]', { timeout: 10000 })
    .should('be.visible')
    .click()
  
  // Wait for redirect to admin dashboard
  cy.url().should('include', '/admin', { timeout: 10000 })
})

Cypress.Commands.add('navigateToCase', (caseTitle) => {
  // Navigate to cases from patient dashboard
  cy.get('[data-testid="nav-cases"]', { timeout: 10000 })
    .should('be.visible')
    .click()
  
  // Find and click on the specific case
  cy.get('[data-testid="case-item"]', { timeout: 10000 })
    .contains(caseTitle)
    .should('be.visible')
    .click()
  
  // Wait for case detail page to load
  cy.get('[data-testid="case-detail"]', { timeout: 10000 })
    .should('be.visible')
})

Cypress.Commands.add('enterEditMode', () => {
  cy.get('[data-testid="case-edit-button"]')
    .should('be.visible')
    .click()
  
  // Verify edit mode is active
  cy.get('[data-testid="case-save-button"]')
    .should('be.visible')
  cy.get('[data-testid="case-cancel-button"]')
    .should('be.visible')
})

Cypress.Commands.add('saveChanges', () => {
  cy.get('[data-testid="case-save-button"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click()
  
  // Wait for save to complete
  cy.get('[data-testid="case-edit-button"]', { timeout: 10000 })
    .should('be.visible')
})

Cypress.Commands.add('cancelEdit', () => {
  cy.get('[data-testid="case-cancel-button"]')
    .should('be.visible')
    .click()
  
  // Verify edit mode is exited
  cy.get('[data-testid="case-edit-button"]')
    .should('be.visible')
})
