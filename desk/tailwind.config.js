/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Continuia Brand Colors - Updated to match primary website
        primary: {
          50: '#f5f0ff',   // Very light purple background
          100: '#ede4ff',  // Light purple background
          200: '#ddd0ff',  // Lighter purple
          300: '#c3a9f7',  // Light purple accent
          400: '#a277ff',  // Medium purple (website buttons)
          500: '#8b5cf6',  // Main purple (website primary color)
          600: '#7c3aed',  // Darker purple (hover states)
          700: '#6d28d9',  // Deep purple
          800: '#5b21b6',  // Very deep purple
          900: '#4c1d95',  // Darkest purple
        },
        accent: {
          50: '#fff0f5',
          100: '#ffe4ed',
          200: '#ffd0e0',
          300: '#ffb9d1',   // Light Pink (Feature Cards)
          400: '#ffa0c2',
          500: '#ff84b0',   // Pink (Accent Color)
          600: '#f0658a',
          700: '#e84a7b',
          800: '#d03a6c',
          900: '#b22a5c',
        },
        background: {
          DEFAULT: '#ffffff', // White background like Continuia website
          secondary: '#f5f0ff', // Soft Lavender Tint for secondary areas (matches website)
          tertiary: '#f8fafc',
          gradient: {
            start: '#f5f0ff', // Light purple gradient start
            middle: '#ffffff', // White middle
            end: '#ede4ff'    // Light purple gradient end
          },
          feature: {
            purple: '#f5f0ff', // Light purple for feature cards (matches website)
            pink: '#fff0f5',   // Light pink for feature cards
            blue: '#f0f5ff',   // Light blue for feature cards
            green: '#f0fff4',  // Light green for feature cards
            orange: '#fff8f0'  // Light orange for feature cards
          },
        },
        // Healthcare specific colors
        success: {
          50: '#f0fdf4',
          500: '#22c55e',
          600: '#16a34a',
        },
        warning: {
          50: '#fffbeb',
          500: '#f59e0b',
          600: '#d97706',
        },
        error: {
          50: '#fef2f2',
          500: '#ef4444',
          600: '#dc2626',
        },
        // Neutral grays
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      fontSize: {
        xs: ['0.75rem', { lineHeight: '1rem' }],
        sm: ['0.875rem', { lineHeight: '1.25rem' }],
        base: ['1rem', { lineHeight: '1.5rem' }],
        lg: ['1.125rem', { lineHeight: '1.75rem' }],
        xl: ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'large': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
