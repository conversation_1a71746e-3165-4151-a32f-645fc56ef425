# My.Continuia Frontend

## Overview
This is the frontend application for My.Continuia, a healthcare platform built with React, TypeScript, and a custom API client.

## Architecture

### Technology Stack
- **React 19**: UI library with TypeScript
- **Vite**: Build tool and development server
- **TypeScript**: Programming language for type safety
- **Shadcn UI**: Primary UI component library
- **Radix UI**: Primitive UI components
- **Tailwind CSS**: Utility-first CSS framework

### Data Management
- **Custom API Client**: Located in `@/services/api.ts` for all backend connectivity
- **React Query**: Used for some data fetching operations
- **React Hook Form**: Form management

### Authentication
- **JWT-based Authentication**: Using the application's authentication system
- **Token Storage**: JWT tokens stored in localStorage as 'auth-token'

### Component Structure
- `components/ui`: Atomic UI components
- `components/refine-ui`: Refine-specific components (legacy, to be refactored)

## Recent Architecture Changes

### 2025-07-23: Removed Refine Dependencies
- Refactored admin components to use custom API client instead of Refine
- Affected files:
  - `src/pages/admin/ConsentFormView.tsx`
  - `src/pages/admin/ConsentForms.tsx`
  - `src/pages/admin/ConsentFormEdit.tsx`
- Implementation now follows the pattern in `UserManagement.tsx` using React state/effect hooks and direct API client calls

## Development

### Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Build
```bash
npm run build
```

## Deployment
- Deployed to Netlify using the existing configuration
