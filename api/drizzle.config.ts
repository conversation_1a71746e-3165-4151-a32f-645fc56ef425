import type { Config } from 'drizzle-kit';
import * as dotenv from 'dotenv';

dotenv.config();

// Use DATABASE_URL for consistency with the rest of the application
const databaseUrl = process.env.DATABASE_URL || '********************************************/continuia';

export default {
  schema: './src/db/schema/*',
  out: './drizzle',
  driver: 'pg',
  dbCredentials: {
    connectionString: databaseUrl,
  },
  verbose: true,
  strict: true,
} satisfies Config;
