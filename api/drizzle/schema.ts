import { pgTable, foreignKey, pgEnum, uuid, timestamp, integer, text, varchar, unique, boolean, jsonb, serial, index, date } from "drizzle-orm/pg-core"
  import { sql } from "drizzle-orm"

export const appointmentStatus = pgEnum("appointment_status", ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])
export const appointmentType = pgEnum("appointment_type", ['consultation', 'follow_up', 'review', 'emergency'])
export const caseStatus = pgEnum("case_status", ['draft', 'submitted', 'in_review', 'assigned', 'completed', 'cancelled'])
export const documentType = pgEnum("document_type", ['lab_report', 'imaging', 'prescription', 'medical_history', 'insurance', 'other'])
export const urgencyLevel = pgEnum("urgency_level", ['low', 'medium', 'high', 'urgent'])
export const confidenceLevel = pgEnum("confidence_level", ['low', 'medium', 'high'])
export const opinionStatus = pgEnum("opinion_status", ['draft', 'submitted', 'reviewed', 'approved'])
export const contactStatus = pgEnum("contact_status", ['lead', 'prospect', 'customer', 'inactive', 'Active', 'Inactive', 'Lead', 'Customer', 'Partner'])
export const organizationType = pgEnum("organization_type", ['hospital', 'clinic', 'private_practice', 'insurance', 'pharmacy', 'other', 'Hospital', 'Clinic', 'Insurance', 'Pharmacy', 'Other'])
export const touchpointType = pgEnum("touchpoint_type", ['email', 'call', 'meeting', 'referral', 'social', 'other', 'Call', 'Email', 'Meeting', 'Demo', 'Followup', 'Other'])
export const doctorRole = pgEnum("doctor_role", ['primary', 'consulting', 'second_opinion', 'reviewing', 'collaborating'])
export const actionStatus = pgEnum("action_status", ['Pending', 'Completed', 'Blocked'])
export const activityType = pgEnum("activity_type", ['Call', 'Email', 'Meeting', 'InternalNote', 'Other'])
export const communicationStepStatus = pgEnum("communication_step_status", ['Pending', 'Completed', 'Skipped', 'Failed'])
export const communicationStepType = pgEnum("communication_step_type", ['Email', 'Call', 'Meeting', 'Task', 'Other'])
export const documentStatus = pgEnum("document_status", ['Draft', 'Sent', 'Signed'])
export const leadSource = pgEnum("lead_source", ['Inbound', 'Outbound', 'Referral', 'Campaign', 'Other'])
export const leadStage = pgEnum("lead_stage", ['Lead', 'Deal', 'ClosedWon', 'ClosedLost'])
export const leadStatus = pgEnum("lead_status", ['New', 'Contacted', 'Qualified', 'Engaged', 'ProposalSent', 'Signed', 'Onboarded', 'ClosedLost'])
export const leadType = pgEnum("lead_type", ['Hospital', 'Clinic', 'Doctor', 'Referral', 'Other'])
export const ownerType = pgEnum("owner_type", ['User', 'Team'])
export const doctorAcceptanceStatus = pgEnum("doctor_acceptance_status", ['pending', 'accepted', 'declined', 'closed'])
export const credentialStatus = pgEnum("credential_status", ['pending', 'verified', 'expired', 'revoked', 'under_review'])
export const credentialType = pgEnum("credential_type", ['medical_license', 'board_certification', 'dea_registration', 'npi_number', 'hospital_privileges', 'malpractice_insurance', 'continuing_education', 'fellowship', 'residency', 'medical_degree', 'other'])
export const verificationMethod = pgEnum("verification_method", ['manual', 'automated', 'third_party', 'document_upload'])


export const appointments = pgTable("appointments", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	patientId: uuid("patient_id").notNull(),
	doctorId: uuid("doctor_id").notNull().references(() => users.id, { onDelete: "cascade" } ),
	caseId: uuid("case_id").references(() => cases.id, { onDelete: "set null" } ),
	appointmentType: appointmentType("appointment_type").default('consultation').notNull(),
	scheduledAt: timestamp("scheduled_at", { mode: 'string' }).notNull(),
	duration: integer("duration").default(30).notNull(),
	status: appointmentStatus("status").default('scheduled').notNull(),
	notes: text("notes"),
	meetingLink: varchar("meeting_link", { length: 500 }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const users = pgTable("users", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	email: varchar("email", { length: 255 }).notNull(),
	passwordHash: varchar("password_hash", { length: 255 }).notNull(),
	firstName: varchar("first_name", { length: 100 }).notNull(),
	lastName: varchar("last_name", { length: 100 }).notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	isEmailVerified: boolean("is_email_verified").default(false).notNull(),
	emailVerificationToken: varchar("email_verification_token", { length: 255 }),
	passwordResetToken: varchar("password_reset_token", { length: 255 }),
	passwordResetExpires: timestamp("password_reset_expires", { mode: 'string' }),
	lastLoginAt: timestamp("last_login_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	role: varchar("role"),
},
(table) => {
	return {
		usersEmailUnique: unique("users_email_unique").on(table.email),
	}
});

export const medicalDocuments = pgTable("medical_documents", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	caseId: uuid("case_id"),
	uploadedBy: uuid("uploaded_by").notNull().references(() => users.id, { onDelete: "cascade" } ),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	documentType: documentType("document_type").default('other').notNull(),
	fileName: varchar("file_name", { length: 255 }).notNull(),
	originalFileName: varchar("original_file_name", { length: 255 }).notNull(),
	filePath: varchar("file_path", { length: 500 }).notNull(),
	fileSize: integer("file_size").notNull(),
	mimeType: varchar("mime_type", { length: 100 }).notNull(),
	isDeleted: boolean("is_deleted").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const medicalOpinions = pgTable("medical_opinions", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	caseId: uuid("case_id").notNull(),
	doctorId: uuid("doctor_id").notNull().references(() => users.id, { onDelete: "cascade" } ),
	diagnosis: text("diagnosis").notNull(),
	recommendations: text("recommendations").notNull(),
	treatmentPlan: text("treatment_plan"),
	followUpInstructions: text("follow_up_instructions"),
	urgencyLevel: urgencyLevel("urgency_level").default('medium').notNull(),
	confidenceLevel: confidenceLevel("confidence_level").default('medium').notNull(),
	additionalTests: text("additional_tests"),
	referralSpecialty: varchar("referral_specialty", { length: 100 }),
	notes: text("notes"),
	status: opinionStatus("status").default('draft').notNull(),
	submittedAt: timestamp("submitted_at", { mode: 'string' }),
	approvedBy: uuid("approved_by").references(() => users.id, { onDelete: "set null" } ),
	approvedAt: timestamp("approved_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const userProfiles = pgTable("user_profiles", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	phoneNumber: varchar("phone_number", { length: 20 }),
	dateOfBirth: timestamp("date_of_birth", { mode: 'string' }),
	gender: varchar("gender", { length: 20 }),
	address: text("address"),
	city: varchar("city", { length: 100 }),
	state: varchar("state", { length: 100 }),
	country: varchar("country", { length: 100 }),
	zipCode: varchar("zip_code", { length: 20 }),
	emergencyContactName: varchar("emergency_contact_name", { length: 200 }),
	emergencyContactPhone: varchar("emergency_contact_phone", { length: 20 }),
	profilePictureUrl: varchar("profile_picture_url", { length: 500 }),
	bio: text("bio"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	bloodType: varchar("blood_type", { length: 10 }),
	insuranceProvider: varchar("insurance_provider", { length: 200 }),
	insurancePolicyNumber: varchar("insurance_policy_number", { length: 100 }),
	allergies: text("allergies"),
	medications: text("medications"),
	medicalConditions: text("medical_conditions"),
	emergencyContactRelationship: varchar("emergency_contact_relationship", { length: 100 }),
	notificationEmail: boolean("notification_email").default(true),
	notificationSms: boolean("notification_sms").default(false),
	notificationPush: boolean("notification_push").default(true),
	privacyShareDataForResearch: boolean("privacy_share_data_for_research").default(false),
	privacyAllowMarketingCommunications: boolean("privacy_allow_marketing_communications").default(false),
});

export const auditLogs = pgTable("audit_logs", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id"),
	action: varchar("action", { length: 100 }).notNull(),
	resource: varchar("resource", { length: 100 }).notNull(),
	resourceId: varchar("resource_id", { length: 255 }),
	ipAddress: varchar("ip_address", { length: 45 }),
	userAgent: text("user_agent"),
	metadata: text("metadata"),
	timestamp: timestamp("timestamp", { mode: 'string' }).defaultNow().notNull(),
});

export const cases = pgTable("cases", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	patientId: uuid("patient_id").notNull(),
	assignedDoctorId: uuid("assigned_doctor_id"),
	title: varchar("title", { length: 255 }).notNull(),
	urgencyLevel: urgencyLevel("urgency_level").default('medium').notNull(),
	specialtyRequired: varchar("specialty_required", { length: 100 }),
	status: caseStatus("status").default('draft').notNull(),
	submittedAt: timestamp("submitted_at", { mode: 'string' }),
	assignedAt: timestamp("assigned_at", { mode: 'string' }),
	completedAt: timestamp("completed_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const permissions = pgTable("permissions", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 100 }).notNull(),
	resource: varchar("resource", { length: 50 }).notNull(),
	action: varchar("action", { length: 50 }).notNull(),
	description: text("description"),
	scopeFilter: jsonb("scope_filter"),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmContacts = pgTable("crm_contacts", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	firstName: varchar("first_name", { length: 100 }).notNull(),
	lastName: varchar("last_name", { length: 100 }).notNull(),
	email: varchar("email", { length: 255 }),
	phone: varchar("phone", { length: 50 }),
	position: varchar("position", { length: 100 }),
	organizationId: uuid("organization_id"),
	status: contactStatus("status").default('lead').notNull(),
	source: varchar("source", { length: 100 }),
	assignedToId: uuid("assigned_to_id"),
	tags: text("tags"),
	notes: text("notes"),
	lastContactedAt: timestamp("last_contacted_at", { mode: 'string' }),
	createdBy: uuid("created_by"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmContactPlanAssignments = pgTable("crm_contact_plan_assignments", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	contactId: uuid("contact_id").notNull(),
	planId: uuid("plan_id").notNull(),
	startDate: timestamp("start_date", { mode: 'string' }).defaultNow().notNull(),
	endDate: timestamp("end_date", { mode: 'string' }),
	isActive: boolean("is_active").default(true).notNull(),
	progress: integer("progress").default(0).notNull(),
	assignedBy: uuid("assigned_by").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmCommunicationPlans = pgTable("crm_communication_plans", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	targetAudience: varchar("target_audience", { length: 100 }),
	duration: integer("duration"),
	isActive: boolean("is_active").default(true).notNull(),
	createdBy: uuid("created_by").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmOrganizations = pgTable("crm_organizations", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	type: organizationType("type").notNull(),
	address: text("address"),
	city: varchar("city", { length: 100 }),
	state: varchar("state", { length: 100 }),
	zipCode: varchar("zip_code", { length: 20 }),
	country: varchar("country", { length: 100 }),
	phone: varchar("phone", { length: 50 }),
	email: varchar("email", { length: 255 }),
	website: varchar("website", { length: 255 }),
	size: varchar("size", { length: 50 }),
	specialties: text("specialties"),
	notes: text("notes"),
	createdBy: uuid("created_by").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmReferrals = pgTable("crm_referrals", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	contactId: uuid("contact_id"),
	organizationId: uuid("organization_id"),
	patientId: uuid("patient_id"),
	caseId: uuid("case_id"),
	referralDate: timestamp("referral_date", { mode: 'string' }).defaultNow().notNull(),
	status: varchar("status", { length: 50 }).default('pending'::character varying).notNull(),
	notes: text("notes"),
	value: integer("value"),
	createdBy: uuid("created_by").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmCommunicationPlanSteps = pgTable("crm_communication_plan_steps", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	planId: uuid("plan_id").notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	type: touchpointType("type").default('email').notNull(),
	dayOffset: integer("day_offset").notNull(),
	template: text("template"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmTouchpoints = pgTable("crm_touchpoints", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	contactId: uuid("contact_id"),
	organizationId: uuid("organization_id"),
	type: touchpointType("type").default('other').notNull(),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	date: timestamp("date", { mode: 'string' }).notNull(),
	outcome: text("outcome"),
	followUpRequired: boolean("follow_up_required").default(false).notNull(),
	followUpDate: timestamp("follow_up_date", { mode: 'string' }),
	createdBy: uuid("created_by").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const userRoles = pgTable("user_roles", {
	userId: uuid("user_id").notNull(),
	roleId: uuid("role_id").notNull(),
	assignedAt: timestamp("assigned_at", { mode: 'string' }).defaultNow().notNull(),
	assignedBy: uuid("assigned_by"),
	isActive: boolean("is_active").default(true).notNull(),
});

export const caseNotes = pgTable("case_notes", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	caseId: uuid("case_id").notNull(),
	doctorId: uuid("doctor_id").notNull(),
	structuredContent: jsonb("structured_content").default({}).notNull(),
	rawContent: text("raw_content"),
	version: integer("version").default(1),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	noteTypeId: uuid("note_type_id").notNull(),
});

export const caseDiscussions = pgTable("case_discussions", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	caseId: uuid("case_id").notNull(),
	authorId: uuid("author_id").notNull(),
	content: text("content").notNull(),
	hasAttachments: boolean("has_attachments").default(false).notNull(),
	isRead: boolean("is_read").default(false).notNull(),
	isDeleted: boolean("is_deleted").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	isVisibleToPatient: boolean("is_visible_to_patient").default(true).notNull(),
});

export const roles = pgTable("roles", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 50 }).notNull(),
	displayName: varchar("display_name", { length: 100 }).notNull(),
	description: text("description"),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const rolePermissions = pgTable("role_permissions", {
	roleId: uuid("role_id").notNull(),
	permissionId: uuid("permission_id").notNull(),
	grantedAt: timestamp("granted_at", { mode: 'string' }).defaultNow().notNull(),
	grantedBy: uuid("granted_by"),
});

export const noteTypes = pgTable("note_types", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	key: varchar("key", { length: 50 }).notNull(),
	name: varchar("name", { length: 100 }).notNull(),
	description: text("description"),
	icon: varchar("icon", { length: 50 }).default('FileText'::character varying),
	color: varchar("color", { length: 50 }).default('blue'::character varying),
	category: varchar("category", { length: 50 }).default('clinical'::character varying),
	allowedRoles: jsonb("allowed_roles").default(["doctor","admin"]).notNull(),
	requiresDoctor: boolean("requires_doctor").default(false),
	requiresPermission: varchar("requires_permission", { length: 100 }),
	autoSave: boolean("auto_save").default(true),
	autoSaveDelay: integer("auto_save_delay").default(2000),
	richText: boolean("rich_text").default(true),
	showDoctorInfo: boolean("show_doctor_info").default(true),
	showInSidebar: boolean("show_in_sidebar").default(false),
	placeholder: text("placeholder"),
	template: jsonb("template"),
	aiEnabled: boolean("ai_enabled").default(false),
	aiModel: varchar("ai_model", { length: 100 }),
	aiPrompt: text("ai_prompt"),
	sortOrder: integer("sort_order").default(0),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	createdBy: uuid("created_by"),
});

export const discussionAttachments = pgTable("discussion_attachments", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	discussionId: uuid("discussion_id").notNull(),
	documentId: uuid("document_id").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
});

export const caseConsentRequirements = pgTable("case_consent_requirements", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	caseId: uuid("case_id").notNull(),
	ruleId: uuid("rule_id").notNull(),
	templateId: uuid("template_id").notNull(),
	requiredFromUserId: uuid("required_from_user_id"),
	requiredFromRole: varchar("required_from_role", { length: 50 }),
	status: varchar("status", { length: 20 }).default('pending'::character varying).notNull(),
	isBlocking: boolean("is_blocking").default(true).notNull(),
	assignedAt: timestamp("assigned_at", { mode: 'string' }).defaultNow().notNull(),
	dueDate: timestamp("due_date", { mode: 'string' }),
	resolvedAt: timestamp("resolved_at", { mode: 'string' }),
	agreementId: uuid("agreement_id"),
});

export const caseDoctors = pgTable("case_doctors", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	caseId: uuid("case_id").notNull(),
	doctorId: uuid("doctor_id").notNull(),
	assignedBy: uuid("assigned_by").notNull(),
	assignedAt: timestamp("assigned_at", { mode: 'string' }).defaultNow().notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	acceptanceStatus: doctorAcceptanceStatus("acceptance_status").default('pending').notNull(),
	acceptedAt: timestamp("accepted_at", { mode: 'string' }),
	timeSpentMinutes: integer("time_spent_minutes").default(0).notNull(),
	lastActivityAt: timestamp("last_activity_at", { mode: 'string' }),
	notes: text("notes"),
});

export const legalComplianceTemplates = pgTable("legal_compliance_templates", {
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	createdBy: uuid("created_by"),
	isActive: boolean("is_active").default(true).notNull(),
	id: uuid("id").primaryKey().notNull(),
});

export const legalComplianceVersions = pgTable("legal_compliance_versions", {
	version: integer("version").notNull(),
	content: text("content").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	createdBy: uuid("created_by"),
	isActive: boolean("is_active").default(true).notNull(),
	notes: text("notes"),
	id: uuid("id").primaryKey().notNull(),
	templateId: uuid("template_id").notNull(),
});

export const userLegalAgreements = pgTable("user_legal_agreements", {
	id: serial("id").primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	formVersionId: integer("form_version_id").notNull(),
	consentedAt: timestamp("consented_at", { mode: 'string' }).defaultNow().notNull(),
	ipAddress: varchar("ip_address", { length: 45 }),
	userAgent: text("user_agent"),
	metadata: text("metadata"),
	documentVersionIdUuid: uuid("document_version_id_uuid"),
	documentVersionId: uuid("document_version_id"),
});

export const consentAssignmentRules = pgTable("consent_assignment_rules", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	templateId: uuid("template_id").notNull(),
	triggerType: varchar("trigger_type", { length: 50 }).notNull(),
	conditions: jsonb("conditions"),
	isRequired: boolean("is_required").default(true).notNull(),
	isRecurring: boolean("is_recurring").default(false).notNull(),
	priority: varchar("priority", { length: 20 }).default('medium'::character varying).notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	createdBy: uuid("created_by"),
});

export const userConsentRequirements = pgTable("user_consent_requirements", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	ruleId: uuid("rule_id").notNull(),
	templateId: uuid("template_id").notNull(),
	status: varchar("status", { length: 20 }).default('pending'::character varying).notNull(),
	isBlocking: boolean("is_blocking").default(true).notNull(),
	triggerContext: jsonb("trigger_context"),
	assignedAt: timestamp("assigned_at", { mode: 'string' }).defaultNow().notNull(),
	dueDate: timestamp("due_date", { mode: 'string' }),
	resolvedAt: timestamp("resolved_at", { mode: 'string' }),
	resolvedBy: uuid("resolved_by"),
	agreementId: uuid("agreement_id"),
});

export const crmActivityLog = pgTable("crm_activity_log", {
	activityId: uuid("activity_id").defaultRandom().primaryKey().notNull(),
	leadId: uuid("lead_id").notNull(),
	type: activityType("type").notNull(),
	date: timestamp("date", { mode: 'string' }).notNull(),
	summary: text("summary"),
	createdBy: uuid("created_by").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmDocuments = pgTable("crm_documents", {
	documentId: uuid("document_id").defaultRandom().primaryKey().notNull(),
	leadId: uuid("lead_id").notNull(),
	type: documentType("type").notNull(),
	status: documentStatus("status").default('Draft').notNull(),
	url: varchar("url", { length: 500 }).notNull(),
	uploadedBy: uuid("uploaded_by").notNull(),
	uploadedAt: timestamp("uploaded_at", { mode: 'string' }).defaultNow().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmLeadActions = pgTable("crm_lead_actions", {
	actionId: uuid("action_id").defaultRandom().primaryKey().notNull(),
	leadId: uuid("lead_id").notNull(),
	title: varchar("title", { length: 255 }).notNull(),
	sequence: varchar("sequence", { length: 50 }),
	status: actionStatus("status").default('Pending').notNull(),
	dueDate: timestamp("due_date", { mode: 'string' }),
	ownerId: uuid("owner_id"),
	ownerType: ownerType("owner_type"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	completedAt: timestamp("completed_at", { mode: 'string' }),
});

export const crmLeads = pgTable("crm_leads", {
	leadId: uuid("lead_id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	type: leadType("type").notNull(),
	source: leadSource("source").notNull(),
	status: leadStatus("status").default('New').notNull(),
	stage: leadStage("stage").default('Lead').notNull(),
	assignedToId: uuid("assigned_to_id"),
	assignedToType: ownerType("assigned_to_type"),
	orgAffiliation: varchar("org_affiliation", { length: 255 }),
	notes: text("notes"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const crmTeams = pgTable("crm_teams", {
	teamId: uuid("team_id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const permissionAuditLog = pgTable("permission_audit_log", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	permissionName: varchar("permission_name", { length: 100 }).notNull(),
	resource: varchar("resource", { length: 50 }).notNull(),
	action: varchar("action", { length: 50 }).notNull(),
	endpoint: varchar("endpoint", { length: 200 }),
	method: varchar("method", { length: 10 }),
	ipAddress: varchar("ip_address", { length: 45 }),
	userAgent: text("user_agent"),
	granted: boolean("granted").notNull(),
	reason: text("reason"),
	filterApplied: jsonb("filter_applied"),
	timestamp: timestamp("timestamp", { mode: 'string' }).defaultNow().notNull(),
});

export const doctorProfileUrls = pgTable("doctor_profile_urls", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	doctorId: uuid("doctor_id").notNull().references(() => users.id, { onDelete: "cascade" } ),
	urlType: varchar("url_type", { length: 50 }).notNull(),
	url: varchar("url", { length: 500 }).notNull(),
	displayName: varchar("display_name", { length: 100 }),
	isVerified: boolean("is_verified").default(false).notNull(),
	verifiedBy: uuid("verified_by").references(() => users.id),
	verifiedAt: timestamp("verified_at", { mode: 'string' }),
	isActive: boolean("is_active").default(true).notNull(),
	sortOrder: integer("sort_order").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const doctorCredentials = pgTable("doctor_credentials", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	doctorId: uuid("doctor_id").notNull().references(() => users.id, { onDelete: "cascade" } ),
	credentialType: credentialType("credential_type").notNull(),
	credentialNumber: varchar("credential_number", { length: 100 }).notNull(),
	issuingAuthority: varchar("issuing_authority", { length: 255 }).notNull(),
	issuedDate: timestamp("issued_date", { mode: 'string' }).notNull(),
	expirationDate: timestamp("expiration_date", { mode: 'string' }),
	status: credentialStatus("status").default('pending').notNull(),
	verificationMethod: verificationMethod("verification_method").default('manual').notNull(),
	verifiedBy: uuid("verified_by").references(() => users.id),
	verifiedAt: timestamp("verified_at", { mode: 'string' }),
	notes: text("notes"),
	metadata: text("metadata"),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const credentialDocuments = pgTable("credential_documents", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	credentialId: uuid("credential_id").notNull().references(() => doctorCredentials.id, { onDelete: "cascade" } ),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	fileName: varchar("file_name", { length: 255 }).notNull(),
	originalFileName: varchar("original_file_name", { length: 255 }).notNull(),
	filePath: varchar("file_path", { length: 500 }).notNull(),
	fileSize: integer("file_size").notNull(),
	mimeType: varchar("mime_type", { length: 100 }).notNull(),
	uploadedBy: uuid("uploaded_by").notNull().references(() => users.id),
	isVerified: boolean("is_verified").default(false).notNull(),
	verifiedBy: uuid("verified_by").references(() => users.id),
	verifiedAt: timestamp("verified_at", { mode: 'string' }),
	isDeleted: boolean("is_deleted").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
});

export const specializationCategories = pgTable("specialization_categories", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 100 }).notNull(),
	description: text("description"),
	parentCategoryId: uuid("parent_category_id"),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxSpecializationCategoriesParent: index("idx_specialization_categories_parent").on(table.parentCategoryId),
		idxSpecializationCategoriesActive: index("idx_specialization_categories_active").on(table.isActive),
		specializationCategoriesParentCategoryIdFkey: foreignKey({
			columns: [table.parentCategoryId],
			foreignColumns: [table.id],
			name: "specialization_categories_parent_category_id_fkey"
		}).onDelete("set null"),
		specializationCategoriesNameKey: unique("specialization_categories_name_key").on(table.name),
	}
});

export const doctorSpecializations = pgTable("doctor_specializations", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	name: varchar("name", { length: 150 }).notNull(),
	code: varchar("code", { length: 20 }),
	description: text("description"),
	categoryId: uuid("category_id").references(() => specializationCategories.id, { onDelete: "set null" } ),
	requiresBoardCertification: boolean("requires_board_certification").default(false),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxDoctorSpecializationsCategory: index("idx_doctor_specializations_category").on(table.categoryId),
		idxDoctorSpecializationsActive: index("idx_doctor_specializations_active").on(table.isActive),
		doctorSpecializationsNameKey: unique("doctor_specializations_name_key").on(table.name),
		doctorSpecializationsCodeKey: unique("doctor_specializations_code_key").on(table.code),
	}
});

export const doctorSpecializationAssignments = pgTable("doctor_specialization_assignments", {
	id: uuid("id").defaultRandom().primaryKey().notNull(),
	doctorId: uuid("doctor_id").notNull().references(() => users.id, { onDelete: "cascade" } ),
	specializationId: uuid("specialization_id").notNull().references(() => doctorSpecializations.id, { onDelete: "cascade" } ),
	isPrimary: boolean("is_primary").default(false),
	boardCertified: boolean("board_certified").default(false),
	certificationDate: date("certification_date"),
	certificationBody: varchar("certification_body", { length: 200 }),
	yearsExperience: integer("years_experience").default(0),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
},
(table) => {
	return {
		idxDoctorSpecializationAssignmentsDoctor: index("idx_doctor_specialization_assignments_doctor").on(table.doctorId),
		idxDoctorSpecializationAssignmentsSpecialization: index("idx_doctor_specialization_assignments_specialization").on(table.specializationId),
		idxDoctorSpecializationAssignmentsPrimary: index("idx_doctor_specialization_assignments_primary").on(table.doctorId, table.isPrimary),
		idxDoctorSpecializationAssignmentsActive: index("idx_doctor_specialization_assignments_active").on(table.isActive),
		doctorSpecializationAssignmenDoctorIdSpecializationIdKey: unique("doctor_specialization_assignmen_doctor_id_specialization_id_key").on(table.doctorId, table.specializationId),
	}
});