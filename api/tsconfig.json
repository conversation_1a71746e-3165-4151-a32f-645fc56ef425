{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleDetection": "force", "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "allowImportingTsExtensions": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/db/*": ["src/db/*"], "@/routes/*": ["src/routes/*"], "@/middleware/*": ["src/middleware/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/services/*": ["src/services/*"], "@/shared/*": ["src/shared/*"]}}, "include": ["src/**/*", "src/shared/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true}}