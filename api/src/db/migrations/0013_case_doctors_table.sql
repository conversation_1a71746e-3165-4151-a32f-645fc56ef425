-- Create case_doctors junction table for multi-doctor assignment
CREATE TABLE IF NOT EXISTS "case_doctors" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "case_id" UUID NOT NULL REFERENCES "cases"("id") ON DELETE CASCADE,
  "doctor_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "assigned_by" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "assigned_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS "case_doctors_case_id_idx" ON "case_doctors" ("case_id");
CREATE INDEX IF NOT EXISTS "case_doctors_doctor_id_idx" ON "case_doctors" ("doctor_id");
CREATE INDEX IF NOT EXISTS "case_doctors_is_active_idx" ON "case_doctors" ("is_active");

-- Migrate existing doctor assignments from cases table
INSERT INTO "case_doctors" ("case_id", "doctor_id", "assigned_by", "assigned_at", "is_active")
SELECT 
  "id" AS "case_id",
  "assigned_doctor_id" AS "doctor_id",
  -- Use the patient_id as the assigned_by since we don't know who actually assigned it
  "patient_id" AS "assigned_by",
  COALESCE("assigned_at", "created_at") AS "assigned_at",
  TRUE AS "is_active"
FROM "cases"
WHERE "assigned_doctor_id" IS NOT NULL;

-- Add comment to document the table
COMMENT ON TABLE "case_doctors" IS 'Junction table for multi-doctor assignment to cases';
