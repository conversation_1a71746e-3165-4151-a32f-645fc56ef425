import { sql } from 'drizzle-orm';
import { pgTable, uuid, text, timestamp, boolean } from 'drizzle-orm/pg-core';

export async function up(db: any) {
  // Create case_discussions table
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS case_discussions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      case_id UUID NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
      author_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      content TEXT NOT NULL,
      has_attachments BOOLEAN NOT NULL DEFAULT FALSE,
      is_read BOOLEAN NOT NULL DEFAULT FALSE,
      is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create discussion_attachments table
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS discussion_attachments (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      discussion_id UUID NOT NULL REFERENCES case_discussions(id) ON DELETE CASCADE,
      document_id UUID NOT NULL REFERENCES medical_documents(id) ON DELETE CASCADE,
      created_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create indexes for better performance
  await db.execute(sql`CREATE INDEX idx_case_discussions_case_id ON case_discussions(case_id);`);
  await db.execute(sql`CREATE INDEX idx_case_discussions_author_id ON case_discussions(author_id);`);
  await db.execute(sql`CREATE INDEX idx_discussion_attachments_discussion_id ON discussion_attachments(discussion_id);`);
  await db.execute(sql`CREATE INDEX idx_discussion_attachments_document_id ON discussion_attachments(document_id);`);
}

export async function down(db: any) {
  // Drop indexes
  await db.execute(sql`DROP INDEX IF EXISTS idx_discussion_attachments_document_id;`);
  await db.execute(sql`DROP INDEX IF EXISTS idx_discussion_attachments_discussion_id;`);
  await db.execute(sql`DROP INDEX IF EXISTS idx_case_discussions_author_id;`);
  await db.execute(sql`DROP INDEX IF EXISTS idx_case_discussions_case_id;`);

  // Drop tables
  await db.execute(sql`DROP TABLE IF EXISTS discussion_attachments;`);
  await db.execute(sql`DROP TABLE IF EXISTS case_discussions;`);
}
