{"id": "9a835efd-db42-4ebf-a74b-64e11054b4f4", "prevId": "4abf68e1-e8e7-4e84-9b34-dcb3eaa4d108", "version": "5", "dialect": "pg", "tables": {"appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "appointment_type": {"name": "appointment_type", "type": "appointment_type", "primaryKey": false, "notNull": true, "default": "'consultation'"}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "status": {"name": "status", "type": "appointment_status", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "meeting_link": {"name": "meeting_link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"appointments_patient_id_users_id_fk": {"name": "appointments_patient_id_users_id_fk", "tableFrom": "appointments", "tableTo": "users", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointments_doctor_id_users_id_fk": {"name": "appointments_doctor_id_users_id_fk", "tableFrom": "appointments", "tableTo": "users", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointments_case_id_cases_id_fk": {"name": "appointments_case_id_cases_id_fk", "tableFrom": "appointments", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "cases": {"name": "cases", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_doctor_id": {"name": "assigned_doctor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "symptoms": {"name": "symptoms", "type": "text", "primaryKey": false, "notNull": true}, "medical_history": {"name": "medical_history", "type": "text", "primaryKey": false, "notNull": false}, "current_medications": {"name": "current_medications", "type": "text", "primaryKey": false, "notNull": false}, "urgency_level": {"name": "urgency_level", "type": "urgency_level", "primaryKey": false, "notNull": true, "default": "'medium'"}, "specialty_required": {"name": "specialty_required", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "case_status", "primaryKey": false, "notNull": true, "default": "'draft'"}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"cases_patient_id_users_id_fk": {"name": "cases_patient_id_users_id_fk", "tableFrom": "cases", "tableTo": "users", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "cases_assigned_doctor_id_users_id_fk": {"name": "cases_assigned_doctor_id_users_id_fk", "tableFrom": "cases", "tableTo": "users", "columnsFrom": ["assigned_doctor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "medical_documents": {"name": "medical_documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "document_type": {"name": "document_type", "type": "document_type", "primaryKey": false, "notNull": true, "default": "'other'"}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "original_file_name": {"name": "original_file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_path": {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"medical_documents_case_id_cases_id_fk": {"name": "medical_documents_case_id_cases_id_fk", "tableFrom": "medical_documents", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_documents_uploaded_by_users_id_fk": {"name": "medical_documents_uploaded_by_users_id_fk", "tableFrom": "medical_documents", "tableTo": "users", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "medical_opinions": {"name": "medical_opinions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "diagnosis": {"name": "diagnosis", "type": "text", "primaryKey": false, "notNull": true}, "recommendations": {"name": "recommendations", "type": "text", "primaryKey": false, "notNull": true}, "treatment_plan": {"name": "treatment_plan", "type": "text", "primaryKey": false, "notNull": false}, "follow_up_instructions": {"name": "follow_up_instructions", "type": "text", "primaryKey": false, "notNull": false}, "urgency_level": {"name": "urgency_level", "type": "urgency_level", "primaryKey": false, "notNull": true, "default": "'medium'"}, "confidence_level": {"name": "confidence_level", "type": "confidence_level", "primaryKey": false, "notNull": true, "default": "'medium'"}, "additional_tests": {"name": "additional_tests", "type": "text", "primaryKey": false, "notNull": false}, "referral_specialty": {"name": "referral_specialty", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "opinion_status", "primaryKey": false, "notNull": true, "default": "'draft'"}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "approved_by": {"name": "approved_by", "type": "uuid", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"medical_opinions_case_id_cases_id_fk": {"name": "medical_opinions_case_id_cases_id_fk", "tableFrom": "medical_opinions", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_opinions_doctor_id_users_id_fk": {"name": "medical_opinions_doctor_id_users_id_fk", "tableFrom": "medical_opinions", "tableTo": "users", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_opinions_approved_by_users_id_fk": {"name": "medical_opinions_approved_by_users_id_fk", "tableFrom": "medical_opinions", "tableTo": "users", "columnsFrom": ["approved_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audit_logs_user_id_users_id_fk": {"name": "audit_logs_user_id_users_id_fk", "tableFrom": "audit_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "emergency_contact_name": {"name": "emergency_contact_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "emergency_contact_phone": {"name": "emergency_contact_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "profile_picture_url": {"name": "profile_picture_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "blood_type": {"name": "blood_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "insurance_provider": {"name": "insurance_provider", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "insurance_policy_number": {"name": "insurance_policy_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "allergies": {"name": "allergies", "type": "text", "primaryKey": false, "notNull": false}, "medications": {"name": "medications", "type": "text", "primaryKey": false, "notNull": false}, "medical_conditions": {"name": "medical_conditions", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_profiles_user_id_users_id_fk": {"name": "user_profiles_user_id_users_id_fk", "tableFrom": "user_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "user_role", "primaryKey": false, "notNull": true, "default": "'patient'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_token": {"name": "password_reset_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}}, "enums": {"appointment_status": {"name": "appointment_status", "values": {"scheduled": "scheduled", "confirmed": "confirmed", "in_progress": "in_progress", "completed": "completed", "cancelled": "cancelled", "no_show": "no_show"}}, "appointment_type": {"name": "appointment_type", "values": {"consultation": "consultation", "follow_up": "follow_up", "review": "review", "emergency": "emergency"}}, "case_status": {"name": "case_status", "values": {"draft": "draft", "submitted": "submitted", "in_review": "in_review", "assigned": "assigned", "completed": "completed", "cancelled": "cancelled"}}, "document_type": {"name": "document_type", "values": {"lab_report": "lab_report", "imaging": "imaging", "prescription": "prescription", "medical_history": "medical_history", "insurance": "insurance", "other": "other"}}, "urgency_level": {"name": "urgency_level", "values": {"low": "low", "medium": "medium", "high": "high", "urgent": "urgent"}}, "confidence_level": {"name": "confidence_level", "values": {"low": "low", "medium": "medium", "high": "high"}}, "opinion_status": {"name": "opinion_status", "values": {"draft": "draft", "submitted": "submitted", "reviewed": "reviewed", "approved": "approved"}}, "user_role": {"name": "user_role", "values": {"patient": "patient", "doctor": "doctor", "agent": "agent", "admin": "admin"}}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}