{"id": "17a383f8-e572-4b82-84b3-b316752419f1", "prevId": "9a835efd-db42-4ebf-a74b-64e11054b4f4", "version": "5", "dialect": "pg", "tables": {"appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "appointment_type": {"name": "appointment_type", "type": "appointment_type", "primaryKey": false, "notNull": true, "default": "'consultation'"}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "status": {"name": "status", "type": "appointment_status", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "meeting_link": {"name": "meeting_link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"appointments_patient_id_users_id_fk": {"name": "appointments_patient_id_users_id_fk", "tableFrom": "appointments", "tableTo": "users", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointments_doctor_id_users_id_fk": {"name": "appointments_doctor_id_users_id_fk", "tableFrom": "appointments", "tableTo": "users", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointments_case_id_cases_id_fk": {"name": "appointments_case_id_cases_id_fk", "tableFrom": "appointments", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "cases": {"name": "cases", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_doctor_id": {"name": "assigned_doctor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "symptoms": {"name": "symptoms", "type": "text", "primaryKey": false, "notNull": true}, "medical_history": {"name": "medical_history", "type": "text", "primaryKey": false, "notNull": false}, "current_medications": {"name": "current_medications", "type": "text", "primaryKey": false, "notNull": false}, "urgency_level": {"name": "urgency_level", "type": "urgency_level", "primaryKey": false, "notNull": true, "default": "'medium'"}, "specialty_required": {"name": "specialty_required", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "case_status", "primaryKey": false, "notNull": true, "default": "'draft'"}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"cases_patient_id_users_id_fk": {"name": "cases_patient_id_users_id_fk", "tableFrom": "cases", "tableTo": "users", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "cases_assigned_doctor_id_users_id_fk": {"name": "cases_assigned_doctor_id_users_id_fk", "tableFrom": "cases", "tableTo": "users", "columnsFrom": ["assigned_doctor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "medical_documents": {"name": "medical_documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "document_type": {"name": "document_type", "type": "document_type", "primaryKey": false, "notNull": true, "default": "'other'"}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "original_file_name": {"name": "original_file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_path": {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"medical_documents_case_id_cases_id_fk": {"name": "medical_documents_case_id_cases_id_fk", "tableFrom": "medical_documents", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_documents_uploaded_by_users_id_fk": {"name": "medical_documents_uploaded_by_users_id_fk", "tableFrom": "medical_documents", "tableTo": "users", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_communication_plan_steps": {"name": "crm_communication_plan_steps", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "touchpoint_type", "primaryKey": false, "notNull": true, "default": "'email'"}, "day_offset": {"name": "day_offset", "type": "integer", "primaryKey": false, "notNull": true}, "template": {"name": "template", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_communication_plan_steps_plan_id_crm_communication_plans_id_fk": {"name": "crm_communication_plan_steps_plan_id_crm_communication_plans_id_fk", "tableFrom": "crm_communication_plan_steps", "tableTo": "crm_communication_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_communication_plans": {"name": "crm_communication_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "target_audience": {"name": "target_audience", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_communication_plans_created_by_users_id_fk": {"name": "crm_communication_plans_created_by_users_id_fk", "tableFrom": "crm_communication_plans", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_contact_plan_assignments": {"name": "crm_contact_plan_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "contact_id": {"name": "contact_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "assigned_by": {"name": "assigned_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_contact_plan_assignments_contact_id_crm_contacts_id_fk": {"name": "crm_contact_plan_assignments_contact_id_crm_contacts_id_fk", "tableFrom": "crm_contact_plan_assignments", "tableTo": "crm_contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "crm_contact_plan_assignments_plan_id_crm_communication_plans_id_fk": {"name": "crm_contact_plan_assignments_plan_id_crm_communication_plans_id_fk", "tableFrom": "crm_contact_plan_assignments", "tableTo": "crm_communication_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "crm_contact_plan_assignments_assigned_by_users_id_fk": {"name": "crm_contact_plan_assignments_assigned_by_users_id_fk", "tableFrom": "crm_contact_plan_assignments", "tableTo": "users", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_contacts": {"name": "crm_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "contact_status", "primaryKey": false, "notNull": true, "default": "'lead'"}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "assigned_to_id": {"name": "assigned_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "last_contacted_at": {"name": "last_contacted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_contacts_organization_id_crm_organizations_id_fk": {"name": "crm_contacts_organization_id_crm_organizations_id_fk", "tableFrom": "crm_contacts", "tableTo": "crm_organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_contacts_assigned_to_id_users_id_fk": {"name": "crm_contacts_assigned_to_id_users_id_fk", "tableFrom": "crm_contacts", "tableTo": "users", "columnsFrom": ["assigned_to_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_contacts_created_by_users_id_fk": {"name": "crm_contacts_created_by_users_id_fk", "tableFrom": "crm_contacts", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_organizations": {"name": "crm_organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "organization_type", "primaryKey": false, "notNull": true, "default": "'other'"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "size": {"name": "size", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_organizations_created_by_users_id_fk": {"name": "crm_organizations_created_by_users_id_fk", "tableFrom": "crm_organizations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_referrals": {"name": "crm_referrals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "contact_id": {"name": "contact_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "referral_date": {"name": "referral_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'new'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_referrals_contact_id_crm_contacts_id_fk": {"name": "crm_referrals_contact_id_crm_contacts_id_fk", "tableFrom": "crm_referrals", "tableTo": "crm_contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_referrals_organization_id_crm_organizations_id_fk": {"name": "crm_referrals_organization_id_crm_organizations_id_fk", "tableFrom": "crm_referrals", "tableTo": "crm_organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_referrals_patient_id_users_id_fk": {"name": "crm_referrals_patient_id_users_id_fk", "tableFrom": "crm_referrals", "tableTo": "users", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_referrals_created_by_users_id_fk": {"name": "crm_referrals_created_by_users_id_fk", "tableFrom": "crm_referrals", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_touchpoints": {"name": "crm_touchpoints", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "contact_id": {"name": "contact_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "touchpoint_type", "primaryKey": false, "notNull": true, "default": "'other'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "outcome": {"name": "outcome", "type": "text", "primaryKey": false, "notNull": false}, "follow_up_required": {"name": "follow_up_required", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "follow_up_date": {"name": "follow_up_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_touchpoints_contact_id_crm_contacts_id_fk": {"name": "crm_touchpoints_contact_id_crm_contacts_id_fk", "tableFrom": "crm_touchpoints", "tableTo": "crm_contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "crm_touchpoints_organization_id_crm_organizations_id_fk": {"name": "crm_touchpoints_organization_id_crm_organizations_id_fk", "tableFrom": "crm_touchpoints", "tableTo": "crm_organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_touchpoints_created_by_users_id_fk": {"name": "crm_touchpoints_created_by_users_id_fk", "tableFrom": "crm_touchpoints", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audit_logs_user_id_users_id_fk": {"name": "audit_logs_user_id_users_id_fk", "tableFrom": "audit_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "emergency_contact_name": {"name": "emergency_contact_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "emergency_contact_phone": {"name": "emergency_contact_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "emergency_contact_relationship": {"name": "emergency_contact_relationship", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "profile_picture_url": {"name": "profile_picture_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "notification_email": {"name": "notification_email", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "notification_sms": {"name": "notification_sms", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "notification_push": {"name": "notification_push", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "privacy_share_data_for_research": {"name": "privacy_share_data_for_research", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "privacy_allow_marketing_communications": {"name": "privacy_allow_marketing_communications", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "blood_type": {"name": "blood_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "insurance_provider": {"name": "insurance_provider", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "insurance_policy_number": {"name": "insurance_policy_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "allergies": {"name": "allergies", "type": "text", "primaryKey": false, "notNull": false}, "medications": {"name": "medications", "type": "text", "primaryKey": false, "notNull": false}, "medical_conditions": {"name": "medical_conditions", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_profiles_user_id_users_id_fk": {"name": "user_profiles_user_id_users_id_fk", "tableFrom": "user_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "user_role", "primaryKey": false, "notNull": true, "default": "'patient'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_token": {"name": "password_reset_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}, "medical_opinions": {"name": "medical_opinions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "diagnosis": {"name": "diagnosis", "type": "text", "primaryKey": false, "notNull": true}, "recommendations": {"name": "recommendations", "type": "text", "primaryKey": false, "notNull": true}, "treatment_plan": {"name": "treatment_plan", "type": "text", "primaryKey": false, "notNull": false}, "follow_up_instructions": {"name": "follow_up_instructions", "type": "text", "primaryKey": false, "notNull": false}, "urgency_level": {"name": "urgency_level", "type": "urgency_level", "primaryKey": false, "notNull": true, "default": "'medium'"}, "confidence_level": {"name": "confidence_level", "type": "confidence_level", "primaryKey": false, "notNull": true, "default": "'medium'"}, "additional_tests": {"name": "additional_tests", "type": "text", "primaryKey": false, "notNull": false}, "referral_specialty": {"name": "referral_specialty", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "opinion_status", "primaryKey": false, "notNull": true, "default": "'draft'"}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "approved_by": {"name": "approved_by", "type": "uuid", "primaryKey": false, "notNull": false}, "approved_at": {"name": "approved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"medical_opinions_case_id_cases_id_fk": {"name": "medical_opinions_case_id_cases_id_fk", "tableFrom": "medical_opinions", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_opinions_doctor_id_users_id_fk": {"name": "medical_opinions_doctor_id_users_id_fk", "tableFrom": "medical_opinions", "tableTo": "users", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_opinions_approved_by_users_id_fk": {"name": "medical_opinions_approved_by_users_id_fk", "tableFrom": "medical_opinions", "tableTo": "users", "columnsFrom": ["approved_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"appointment_status": {"name": "appointment_status", "values": {"scheduled": "scheduled", "confirmed": "confirmed", "in_progress": "in_progress", "completed": "completed", "cancelled": "cancelled", "no_show": "no_show"}}, "appointment_type": {"name": "appointment_type", "values": {"consultation": "consultation", "follow_up": "follow_up", "review": "review", "emergency": "emergency"}}, "case_status": {"name": "case_status", "values": {"draft": "draft", "submitted": "submitted", "in_review": "in_review", "assigned": "assigned", "completed": "completed", "cancelled": "cancelled"}}, "document_type": {"name": "document_type", "values": {"lab_report": "lab_report", "imaging": "imaging", "prescription": "prescription", "medical_history": "medical_history", "insurance": "insurance", "other": "other"}}, "urgency_level": {"name": "urgency_level", "values": {"low": "low", "medium": "medium", "high": "high", "urgent": "urgent"}}, "contact_status": {"name": "contact_status", "values": {"lead": "lead", "prospect": "prospect", "customer": "customer", "inactive": "inactive"}}, "organization_type": {"name": "organization_type", "values": {"hospital": "hospital", "clinic": "clinic", "private_practice": "private_practice", "insurance": "insurance", "pharmacy": "pharmacy", "other": "other"}}, "touchpoint_type": {"name": "touchpoint_type", "values": {"email": "email", "call": "call", "meeting": "meeting", "referral": "referral", "social": "social", "other": "other"}}, "user_role": {"name": "user_role", "values": {"patient": "patient", "doctor": "doctor", "agent": "agent", "admin": "admin"}}, "confidence_level": {"name": "confidence_level", "values": {"low": "low", "medium": "medium", "high": "high"}}, "opinion_status": {"name": "opinion_status", "values": {"draft": "draft", "submitted": "submitted", "reviewed": "reviewed", "approved": "approved"}}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}