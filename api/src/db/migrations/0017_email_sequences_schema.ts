import { sql } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
export async function up(db: any) {
  // Create sequence_status enum
  await db.execute(sql`
    CREATE TYPE sequence_status AS ENUM ('Draft', 'Active', 'Paused', 'Completed', 'Archived');
  `);

  // Create sequence_step_type enum
  await db.execute(sql`
    CREATE TYPE sequence_step_type AS ENUM ('Email', 'Wait', 'Condition', 'Action');
  `);

  // Create sequence_step_status enum
  await db.execute(sql`
    CREATE TYPE sequence_step_status AS ENUM ('Pending', 'Scheduled', 'Sent', 'Failed', 'Skipped', 'Completed');
  `);

  // Create email sequences table
  await db.execute(sql`
    CREATE TABLE crm_email_sequences (
      sequence_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) NOT NULL,
      description TEXT,
      status sequence_status NOT NULL DEFAULT 'Draft',
      trigger_campaign_id UUID REFERENCES crm_campaigns(campaign_id) ON DELETE CASCADE,
      trigger_event VARCHAR(255) NOT NULL,
      max_subscribers INTEGER,
      respect_unsubscribe BOOLEAN NOT NULL DEFAULT true,
      respect_business_hours BOOLEAN NOT NULL DEFAULT false,
      business_hours_start VARCHAR(5) DEFAULT '09:00',
      business_hours_end VARCHAR(5) DEFAULT '17:00',
      business_days_only BOOLEAN NOT NULL DEFAULT false,
      timezone VARCHAR(50) DEFAULT 'UTC',
      total_subscribers INTEGER NOT NULL DEFAULT 0,
      active_subscribers INTEGER NOT NULL DEFAULT 0,
      completed_subscribers INTEGER NOT NULL DEFAULT 0,
      tags JSONB DEFAULT '[]'::jsonb,
      metadata JSONB DEFAULT '{}'::jsonb,
      created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create email sequence steps table
  await db.execute(sql`
    CREATE TABLE crm_email_sequence_steps (
      step_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      sequence_id UUID NOT NULL REFERENCES crm_email_sequences(sequence_id) ON DELETE CASCADE,
      step_order INTEGER NOT NULL,
      step_type sequence_step_type NOT NULL,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      delay_amount INTEGER NOT NULL DEFAULT 0,
      delay_unit VARCHAR(20) NOT NULL DEFAULT 'hours',
      template_id UUID REFERENCES crm_email_templates(template_id) ON DELETE SET NULL,
      subject_override VARCHAR(500),
      wait_until_time VARCHAR(5),
      wait_until_day VARCHAR(10),
      condition_rules JSONB,
      action_config JSONB,
      is_active BOOLEAN NOT NULL DEFAULT true,
      skip_weekends BOOLEAN NOT NULL DEFAULT false,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create email sequence subscribers table
  await db.execute(sql`
    CREATE TABLE crm_email_sequence_subscribers (
      subscriber_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      sequence_id UUID NOT NULL REFERENCES crm_email_sequences(sequence_id) ON DELETE CASCADE,
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      email VARCHAR(255) NOT NULL,
      name VARCHAR(255),
      current_step_id UUID REFERENCES crm_email_sequence_steps(step_id) ON DELETE SET NULL,
      current_step_order INTEGER NOT NULL DEFAULT 1,
      next_execution_at TIMESTAMP,
      status VARCHAR(50) NOT NULL DEFAULT 'active',
      started_at TIMESTAMP NOT NULL DEFAULT NOW(),
      completed_at TIMESTAMP,
      paused_at TIMESTAMP,
      unsubscribed_at TIMESTAMP,
      context_data JSONB DEFAULT '{}'::jsonb,
      emails_sent INTEGER NOT NULL DEFAULT 0,
      emails_opened INTEGER NOT NULL DEFAULT 0,
      emails_clicked INTEGER NOT NULL DEFAULT 0,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create email sequence executions table
  await db.execute(sql`
    CREATE TABLE crm_email_sequence_executions (
      execution_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      subscriber_id UUID NOT NULL REFERENCES crm_email_sequence_subscribers(subscriber_id) ON DELETE CASCADE,
      step_id UUID NOT NULL REFERENCES crm_email_sequence_steps(step_id) ON DELETE CASCADE,
      status sequence_step_status NOT NULL DEFAULT 'Pending',
      scheduled_at TIMESTAMP NOT NULL,
      executed_at TIMESTAMP,
      campaign_execution_id UUID,
      email_sent BOOLEAN NOT NULL DEFAULT false,
      email_delivered BOOLEAN NOT NULL DEFAULT false,
      email_opened BOOLEAN NOT NULL DEFAULT false,
      email_clicked BOOLEAN NOT NULL DEFAULT false,
      email_bounced BOOLEAN NOT NULL DEFAULT false,
      error_message TEXT,
      retry_count INTEGER NOT NULL DEFAULT 0,
      max_retries INTEGER NOT NULL DEFAULT 3,
      result_data JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create email sequence analytics table
  await db.execute(sql`
    CREATE TABLE crm_email_sequence_analytics (
      analytics_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      sequence_id UUID NOT NULL REFERENCES crm_email_sequences(sequence_id) ON DELETE CASCADE,
      step_id UUID REFERENCES crm_email_sequence_steps(step_id) ON DELETE CASCADE,
      period_start TIMESTAMP NOT NULL,
      period_end TIMESTAMP NOT NULL,
      period_type VARCHAR(20) NOT NULL,
      new_subscribers INTEGER NOT NULL DEFAULT 0,
      active_subscribers INTEGER NOT NULL DEFAULT 0,
      completed_subscribers INTEGER NOT NULL DEFAULT 0,
      unsubscribed_subscribers INTEGER NOT NULL DEFAULT 0,
      emails_sent INTEGER NOT NULL DEFAULT 0,
      emails_delivered INTEGER NOT NULL DEFAULT 0,
      emails_opened INTEGER NOT NULL DEFAULT 0,
      emails_clicked INTEGER NOT NULL DEFAULT 0,
      emails_bounced INTEGER NOT NULL DEFAULT 0,
      completion_rate INTEGER NOT NULL DEFAULT 0,
      unsubscribe_rate INTEGER NOT NULL DEFAULT 0,
      delivery_rate INTEGER NOT NULL DEFAULT 0,
      open_rate INTEGER NOT NULL DEFAULT 0,
      click_rate INTEGER NOT NULL DEFAULT 0,
      bounce_rate INTEGER NOT NULL DEFAULT 0,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create indexes for performance
  await db.execute(sql`
    CREATE INDEX idx_crm_email_sequences_status ON crm_email_sequences(status);
    CREATE INDEX idx_crm_email_sequences_trigger_event ON crm_email_sequences(trigger_event);
    CREATE INDEX idx_crm_email_sequences_created_by ON crm_email_sequences(created_by);
    
    CREATE INDEX idx_crm_email_sequence_steps_sequence_id ON crm_email_sequence_steps(sequence_id);
    CREATE INDEX idx_crm_email_sequence_steps_step_order ON crm_email_sequence_steps(step_order);
    CREATE INDEX idx_crm_email_sequence_steps_step_type ON crm_email_sequence_steps(step_type);
    CREATE INDEX idx_crm_email_sequence_steps_is_active ON crm_email_sequence_steps(is_active);
    
    CREATE INDEX idx_crm_email_sequence_subscribers_sequence_id ON crm_email_sequence_subscribers(sequence_id);
    CREATE INDEX idx_crm_email_sequence_subscribers_user_id ON crm_email_sequence_subscribers(user_id);
    CREATE INDEX idx_crm_email_sequence_subscribers_email ON crm_email_sequence_subscribers(email);
    CREATE INDEX idx_crm_email_sequence_subscribers_status ON crm_email_sequence_subscribers(status);
    CREATE INDEX idx_crm_email_sequence_subscribers_next_execution ON crm_email_sequence_subscribers(next_execution_at);
    
    CREATE INDEX idx_crm_email_sequence_executions_subscriber_id ON crm_email_sequence_executions(subscriber_id);
    CREATE INDEX idx_crm_email_sequence_executions_step_id ON crm_email_sequence_executions(step_id);
    CREATE INDEX idx_crm_email_sequence_executions_status ON crm_email_sequence_executions(status);
    CREATE INDEX idx_crm_email_sequence_executions_scheduled_at ON crm_email_sequence_executions(scheduled_at);
    
    CREATE INDEX idx_crm_email_sequence_analytics_sequence_id ON crm_email_sequence_analytics(sequence_id);
    CREATE INDEX idx_crm_email_sequence_analytics_step_id ON crm_email_sequence_analytics(step_id);
    CREATE INDEX idx_crm_email_sequence_analytics_period ON crm_email_sequence_analytics(period_start, period_end);
  `);

  logger.debug('✅ Created email sequences schema tables and indexes');
}

export async function down(db: any) {
  // Drop tables in reverse order to handle dependencies
  await db.execute(sql`DROP TABLE IF EXISTS crm_email_sequence_analytics CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS crm_email_sequence_executions CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS crm_email_sequence_subscribers CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS crm_email_sequence_steps CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS crm_email_sequences CASCADE;`);
  
  // Drop enums
  await db.execute(sql`DROP TYPE IF EXISTS sequence_step_status CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS sequence_step_type CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS sequence_status CASCADE;`);
  
  logger.debug('✅ Dropped email sequences schema tables and types');
}