import { sql } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
export async function up(db: any) {
  // Add patient visibility column to case_discussions table
  await db.execute(sql`
    ALTER TABLE case_discussions 
    ADD COLUMN is_visible_to_patient BOOLEAN NOT NULL DEFAULT true;
  `);
  
  // Create index for better performance when filtering by patient visibility
  await db.execute(sql`
    CREATE INDEX idx_case_discussions_patient_visibility 
    ON case_discussions(case_id, is_visible_to_patient, created_at);
  `);
  
  logger.debug('Added patient visibility column to case_discussions table');
}

export async function down(db: any) {
  // Drop index
  await db.execute(sql`DROP INDEX IF EXISTS idx_case_discussions_patient_visibility;`);
  
  // Remove patient visibility column
  await db.execute(sql`ALTER TABLE case_discussions DROP COLUMN IF EXISTS is_visible_to_patient;`);
  
  logger.debug('Removed patient visibility column from case_discussions table');
}