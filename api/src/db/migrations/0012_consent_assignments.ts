import { sql } from 'drizzle-orm';
import { pgTable, text, timestamp, varchar, boolean, uuid, jsonb } from "drizzle-orm/pg-core";

import { logger } from '../utils/structuredLogger';
export async function up(db: any) {
  // Create consent assignment rules table
  await db.execute(sql`
    CREATE TABLE consent_assignment_rules (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      template_id UUID NOT NULL REFERENCES legal_compliance_templates(id),
      trigger_type VARCHAR(50) NOT NULL,
      conditions JSONB,
      is_required BOOLEAN NOT NULL DEFAULT true,
      is_recurring BOOLEAN NOT NULL DEFAULT false,
      priority VARCHAR(20) NOT NULL DEFAULT 'medium',
      name VARCHAR(255) NOT NULL,
      description TEXT,
      is_active BOOLEAN NOT NULL DEFAULT true,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
      created_by UUID
    );
  `);

  // Create user consent requirements table
  await db.execute(sql`
    CREATE TABLE user_consent_requirements (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL,
      rule_id UUID NOT NULL REFERENCES consent_assignment_rules(id),
      template_id UUID NOT NULL REFERENCES legal_compliance_templates(id),
      status VARCHAR(20) NOT NULL DEFAULT 'pending',
      is_blocking BOOLEAN NOT NULL DEFAULT true,
      trigger_context JSONB,
      assigned_at TIMESTAMP NOT NULL DEFAULT NOW(),
      due_date TIMESTAMP,
      resolved_at TIMESTAMP,
      resolved_by UUID,
      agreement_id UUID
    );
  `);

  // Create case consent requirements table
  await db.execute(sql`
    CREATE TABLE case_consent_requirements (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      case_id UUID NOT NULL,
      rule_id UUID NOT NULL REFERENCES consent_assignment_rules(id),
      template_id UUID NOT NULL REFERENCES legal_compliance_templates(id),
      required_from_user_id UUID,
      required_from_role VARCHAR(50),
      status VARCHAR(20) NOT NULL DEFAULT 'pending',
      is_blocking BOOLEAN NOT NULL DEFAULT true,
      assigned_at TIMESTAMP NOT NULL DEFAULT NOW(),
      due_date TIMESTAMP,
      resolved_at TIMESTAMP,
      agreement_id UUID
    );
  `);

  // Create indexes for performance
  await db.execute(sql`
    CREATE INDEX idx_consent_assignment_rules_trigger_type ON consent_assignment_rules(trigger_type);
    CREATE INDEX idx_consent_assignment_rules_template_id ON consent_assignment_rules(template_id);
    CREATE INDEX idx_user_consent_requirements_user_id ON user_consent_requirements(user_id);
    CREATE INDEX idx_user_consent_requirements_status ON user_consent_requirements(status);
    CREATE INDEX idx_case_consent_requirements_case_id ON case_consent_requirements(case_id);
    CREATE INDEX idx_case_consent_requirements_status ON case_consent_requirements(status);
  `);

  logger.debug('✅ Created consent assignment tables and indexes');
}

export async function down(db: any) {
  await db.execute(sql`DROP TABLE IF EXISTS case_consent_requirements CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS user_consent_requirements CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS consent_assignment_rules CASCADE;`);
  
  logger.debug('✅ Dropped consent assignment tables');
}
