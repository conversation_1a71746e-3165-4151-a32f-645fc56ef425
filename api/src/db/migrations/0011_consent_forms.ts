import { sql } from "drizzle-orm";
import { pgTable, serial, text, timestamp, varchar, integer, boolean } from "drizzle-orm/pg-core";

export async function up(db) {
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS consent_form_templates (
      id SERIAL PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      created_at TIMESTAMP DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
      created_by INTEGER REFERENCES users(id),
      is_active BOOLEAN DEFAULT TRUE NOT NULL
    );

    CREATE TABLE IF NOT EXISTS consent_form_versions (
      id SERIAL PRIMARY KEY,
      template_id INTEGER REFERENCES consent_form_templates(id) NOT NULL,
      version INTEGER NOT NULL,
      content TEXT NOT NULL,
      created_at TIMESTAMP DEFAULT NOW() NOT NULL,
      created_by INTEGER REFERENCES users(id),
      is_active BOOLEAN DEFAULT TRUE NOT NULL,
      notes TEXT
    );

    CREATE TABLE IF NOT EXISTS user_consents (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) NOT NULL,
      form_version_id INTEGER REFERENCES consent_form_versions(id) NOT NULL,
      consented_at TIMESTAMP DEFAULT NOW() NOT NULL,
      ip_address VARCHAR(45),
      user_agent TEXT,
      metadata TEXT
    );

    -- Create indexes for better query performance
    CREATE INDEX idx_consent_form_versions_template_id ON consent_form_versions(template_id);
    CREATE INDEX idx_consent_form_versions_is_active ON consent_form_versions(is_active);
    CREATE INDEX idx_user_consents_user_id ON user_consents(user_id);
    CREATE INDEX idx_user_consents_form_version_id ON user_consents(form_version_id);
  `);
}

export async function down(db) {
  await db.execute(sql`
    DROP INDEX IF EXISTS idx_user_consents_form_version_id;
    DROP INDEX IF EXISTS idx_user_consents_user_id;
    DROP INDEX IF EXISTS idx_consent_form_versions_is_active;
    DROP INDEX IF EXISTS idx_consent_form_versions_template_id;
    
    DROP TABLE IF EXISTS user_consents;
    DROP TABLE IF EXISTS consent_form_versions;
    DROP TABLE IF EXISTS consent_form_templates;
  `);
}
