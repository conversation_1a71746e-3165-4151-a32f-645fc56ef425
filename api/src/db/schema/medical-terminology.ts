import { pgTable, uuid, varchar, text, jsonb, timestamp, boolean, index, unique } from 'drizzle-orm/pg-core';

// Medical terminology cache table
export const medicalTerminologyCache = pgTable('medical_terminology_cache', {
  id: uuid('id').primaryKey().defaultRandom(),
  terminologyType: varchar('terminology_type', { length: 20 }).notNull(),
  searchTerm: varchar('search_term', { length: 255 }).notNull(),
  results: jsonb('results').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}, (table) => ({
  uniqueTerminologySearch: unique().on(table.terminologyType, table.searchTerm),
  expiresAtIdx: index('idx_medical_cache_expires').on(table.expiresAt),
}));

export type MedicalTerminologyCache = typeof medicalTerminologyCache.$inferSelect;
export type NewMedicalTerminologyCache = typeof medicalTerminologyCache.$inferInsert;
