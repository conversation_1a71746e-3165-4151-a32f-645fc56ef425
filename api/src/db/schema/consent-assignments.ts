import { pgTable, text, timestamp, varchar, boolean, uuid, jsonb } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { legalComplianceTemplates } from "./legal-compliance";

/**
 * Consent Assignment Rules
 * Defines when and to whom specific consent documents should be assigned
 */
export const consentAssignmentRules = pgTable("consent_assignment_rules", {
  id: uuid("id").primaryKey().defaultRandom(),
  templateId: uuid("template_id").notNull().references(() => legalComplianceTemplates.id),
  
  // Assignment trigger type
  triggerType: varchar("trigger_type", { length: 50 }).notNull(), // 'login', 'role_assignment', 'case_creation', 'manual'
  
  // Assignment conditions (JSON for flexibility)
  conditions: jsonb("conditions"), // { "userRole": "patient", "firstLogin": true } or { "caseType": "consultation" }
  
  // Assignment behavior
  isRequired: boolean("is_required").default(true).notNull(), // Block access until accepted
  isRecurring: boolean("is_recurring").default(false).notNull(), // Re-require on version updates
  priority: varchar("priority", { length: 20 }).default("medium").notNull(), // 'high', 'medium', 'low'
  
  // Metadata
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: uuid("created_by"),
});

/**
 * User Consent Requirements
 * Tracks which consents are currently required for each user
 */
export const userConsentRequirements = pgTable("user_consent_requirements", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").notNull(),
  ruleId: uuid("rule_id").notNull().references(() => consentAssignmentRules.id),
  templateId: uuid("template_id").notNull().references(() => legalComplianceTemplates.id),
  
  // Requirement status
  status: varchar("status", { length: 20 }).default("pending").notNull(), // 'pending', 'accepted', 'declined', 'expired'
  isBlocking: boolean("is_blocking").default(true).notNull(), // Whether this blocks user access
  
  // Context information
  triggerContext: jsonb("trigger_context"), // Additional context about why this was assigned
  assignedAt: timestamp("assigned_at").defaultNow().notNull(),
  dueDate: timestamp("due_date"),
  
  // Resolution tracking
  resolvedAt: timestamp("resolved_at"),
  resolvedBy: uuid("resolved_by"), // User who accepted/declined
  agreementId: uuid("agreement_id"), // Reference to userLegalAgreements when accepted
});

/**
 * Case Consent Requirements
 * Tracks consent requirements specific to cases
 */
export const caseConsentRequirements = pgTable("case_consent_requirements", {
  id: uuid("id").primaryKey().defaultRandom(),
  caseId: uuid("case_id").notNull(), // Reference to cases table
  ruleId: uuid("rule_id").notNull().references(() => consentAssignmentRules.id),
  templateId: uuid("template_id").notNull().references(() => legalComplianceTemplates.id),
  
  // Who needs to consent
  requiredFromUserId: uuid("required_from_user_id"), // Specific user (e.g., patient)
  requiredFromRole: varchar("required_from_role", { length: 50 }), // Or any user with this role
  
  // Requirement status
  status: varchar("status", { length: 20 }).default("pending").notNull(),
  isBlocking: boolean("is_blocking").default(true).notNull(),
  
  // Context and timing
  assignedAt: timestamp("assigned_at").defaultNow().notNull(),
  dueDate: timestamp("due_date"),
  resolvedAt: timestamp("resolved_at"),
  agreementId: uuid("agreement_id"), // Reference to userLegalAgreements when accepted
});

// Relations
export const consentAssignmentRulesRelations = relations(consentAssignmentRules, ({ one, many }) => ({
  template: one(legalComplianceTemplates, {
    fields: [consentAssignmentRules.templateId],
    references: [legalComplianceTemplates.id],
  }),
  userRequirements: many(userConsentRequirements),
  caseRequirements: many(caseConsentRequirements),
}));

export const userConsentRequirementsRelations = relations(userConsentRequirements, ({ one }) => ({
  rule: one(consentAssignmentRules, {
    fields: [userConsentRequirements.ruleId],
    references: [consentAssignmentRules.id],
  }),
  template: one(legalComplianceTemplates, {
    fields: [userConsentRequirements.templateId],
    references: [legalComplianceTemplates.id],
  }),
}));

export const caseConsentRequirementsRelations = relations(caseConsentRequirements, ({ one }) => ({
  rule: one(consentAssignmentRules, {
    fields: [caseConsentRequirements.ruleId],
    references: [consentAssignmentRules.id],
  }),
  template: one(legalComplianceTemplates, {
    fields: [caseConsentRequirements.templateId],
    references: [legalComplianceTemplates.id],
  }),
}));
