import { pgTable, uuid, varchar, text, timestamp, pgEnum } from 'drizzle-orm/pg-core';
import { users } from './users';
import { cases, urgencyLevelEnum } from './cases';

// Opinion status enum
export const opinionStatusEnum = pgEnum('opinion_status', [
  'draft', 'submitted', 'reviewed', 'approved'
]);

// Confidence level enum
export const confidenceLevelEnum = pgEnum('confidence_level', [
  'low', 'medium', 'high'
]);

// Medical opinions table
export const medicalOpinions = pgTable('medical_opinions', {
  id: uuid('id').primaryKey().defaultRandom(),
  caseId: uuid('case_id').notNull().references(() => cases.id, { onDelete: 'cascade' }),
  doctorId: uuid('doctor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  diagnosis: text('diagnosis').notNull(),
  recommendations: text('recommendations').notNull(),
  treatmentPlan: text('treatment_plan'),
  followUpInstructions: text('follow_up_instructions'),
  urgencyLevel: urgencyLevelEnum('urgency_level').notNull().default('medium'),
  confidenceLevel: confidenceLevelEnum('confidence_level').notNull().default('medium'),
  additionalTests: text('additional_tests'),
  referralSpecialty: varchar('referral_specialty', { length: 100 }),
  notes: text('notes'),
  status: opinionStatusEnum('status').notNull().default('draft'),
  submittedAt: timestamp('submitted_at'),
  approvedBy: uuid('approved_by').references(() => users.id, { onDelete: 'set null' }),
  approvedAt: timestamp('approved_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});
