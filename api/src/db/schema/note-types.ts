import { pgTable, uuid, varchar, text, boolean, integer, jsonb, timestamp } from 'drizzle-orm/pg-core';

// Configurable note types table - drive entire UI from this
export const noteTypes = pgTable('note_types', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Basic Configuration
  key: varchar('key', { length: 50 }).notNull().unique(), // e.g., 'clinical_notes', 'raccha_ai_notes'
  name: varchar('name', { length: 100 }).notNull(), // e.g., 'Clinical Notes', 'Raccha.AI Notes'
  description: text('description'), // e.g., 'Private clinical observations and notes'
  
  // UI Configuration
  icon: varchar('icon', { length: 50 }).default('FileText'), // Lucide icon name
  color: varchar('color', { length: 50 }).default('blue'), // Tailwind color scheme
  category: varchar('category', { length: 50 }).default('clinical'), // clinical, ai, administrative, etc.
  
  // Access Control
  allowedRoles: jsonb('allowed_roles').notNull().default('["doctor", "admin"]'), // JSON array of roles
  requiresDoctor: boolean('requires_doctor').default(false), // Must have doctor assigned to case
  requiresPermission: varchar('requires_permission', { length: 100 }), // Optional specific permission
  
  // Behavior Configuration
  autoSave: boolean('auto_save').default(true),
  autoSaveDelay: integer('auto_save_delay').default(2000), // milliseconds
  richText: boolean('rich_text').default(true),
  showDoctorInfo: boolean('show_doctor_info').default(true),
  showInSidebar: boolean('show_in_sidebar').default(false), // Show in right sidebar vs main content
  
  // Template Configuration
  placeholder: text('placeholder'),
  template: jsonb('template'), // Optional structured template/schema
  
  // AI/Automation Configuration
  aiEnabled: boolean('ai_enabled').default(false), // Can AI agents write to this note type
  aiModel: varchar('ai_model', { length: 100 }), // e.g., 'raccha.ai', 'gpt-4'
  aiPrompt: text('ai_prompt'), // System prompt for AI generation
  
  // Display Configuration
  sortOrder: integer('sort_order').default(0), // Order in UI
  isActive: boolean('is_active').default(true),
  
  // Metadata
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  createdBy: uuid('created_by'), // Who configured this note type
});

// Relations
export const noteTypesRelations = {
  // Could add relations to users, permissions, etc. if needed
};

// Default note types that should be seeded
export const defaultNoteTypes = [
  {
    key: 'clinical_notes',
    name: 'Clinical Notes',
    description: 'Private clinical observations and notes',
    icon: 'Stethoscope',
    color: 'blue',
    category: 'clinical',
    allowedRoles: ['doctor', 'admin'],
    requiresDoctor: false,
    showInSidebar: true,
    placeholder: 'Add your clinical observations and notes here...',
    sortOrder: 1,
  },
  {
    key: 'second_opinion',
    name: 'Second Opinion',
    description: 'Specialist second opinion notes',
    icon: 'Users',
    color: 'green',
    category: 'clinical',
    allowedRoles: ['doctor', 'admin'],
    requiresDoctor: true,
    showInSidebar: true,
    placeholder: 'Provide your second opinion analysis...',
    sortOrder: 2,
  },
  {
    key: 'consultation',
    name: 'Consultation Notes',
    description: 'Consultation session notes',
    icon: 'MessageSquare',
    color: 'purple',
    category: 'clinical',
    allowedRoles: ['doctor', 'admin'],
    requiresDoctor: true,
    showInSidebar: true,
    placeholder: 'Document consultation discussion and recommendations...',
    sortOrder: 3,
  },
  {
    key: 'progress_note',
    name: 'Progress Notes',
    description: 'Patient progress tracking',
    icon: 'TrendingUp',
    color: 'orange',
    category: 'clinical',
    allowedRoles: ['doctor', 'admin'],
    requiresDoctor: false,
    showInSidebar: true,
    placeholder: 'Track patient progress and treatment response...',
    sortOrder: 4,
  },
  {
    key: 'medical_opinion',
    name: 'Medical Opinion',
    description: 'Comprehensive medical opinion and diagnosis by attending physician',
    icon: 'Stethoscope',
    color: 'red',
    category: 'clinical',
    allowedRoles: ['doctor', 'admin'],
    requiresDoctor: true,
    showInSidebar: false, // This will be shown in main content area
    placeholder: 'Provide your comprehensive medical opinion, diagnosis, treatment recommendations, and clinical assessment...',
    sortOrder: 5,
  },
  {
    key: 'raccha_ai_notes',
    name: 'Raccha.AI Notes',
    description: 'AI-generated clinical insights and recommendations',
    icon: 'Brain',
    color: 'indigo',
    category: 'ai',
    allowedRoles: ['doctor', 'admin', 'agent'],
    requiresDoctor: false,
    showInSidebar: false,
    aiEnabled: true,
    aiModel: 'raccha.ai',
    aiPrompt: 'Analyze the case data and provide clinical insights, recommendations, and potential diagnoses.',
    placeholder: 'AI-generated insights will appear here...',
    sortOrder: 10,
  },
  {
    key: 'administrative_notes',
    name: 'Administrative Notes',
    description: 'Administrative and billing notes',
    icon: 'Clipboard',
    color: 'gray',
    category: 'administrative',
    allowedRoles: ['admin', 'agent'],
    requiresDoctor: false,
    showInSidebar: false,
    placeholder: 'Administrative notes and billing information...',
    sortOrder: 20,
  },
  // Individual note types for yJS collaborative editing
  {
    key: 'current_medications',
    name: 'Current Medications',
    description: 'Patient current medications and dosages',
    icon: 'Pill',
    color: 'blue',
    category: 'clinical',
    allowedRoles: ['patient', 'doctor', 'admin'],
    requiresDoctor: false,
    showInSidebar: false,
    placeholder: 'List your current medications, dosages, and frequency...',
    template: {
      medications: [
        {
          name: '',
          dosage: '',
          frequency: '',
          prescribedBy: '',
          startDate: ''
        }
      ]
    },
    sortOrder: 30,
  },
  {
    key: 'medical_history',
    name: 'Medical History',
    description: 'Patient past medical history and conditions',
    icon: 'History',
    color: 'green',
    category: 'clinical',
    allowedRoles: ['patient', 'doctor', 'admin'],
    requiresDoctor: false,
    showInSidebar: false,
    placeholder: 'Describe your past medical history, surgeries, and chronic conditions...',
    template: {
      conditions: [],
      surgeries: [],
      allergies: [],
      familyHistory: ''
    },
    sortOrder: 31,
  },
  {
    key: 'symptoms',
    name: 'Symptoms',
    description: 'Current symptoms and their details',
    icon: 'Activity',
    color: 'red',
    category: 'clinical',
    allowedRoles: ['patient', 'doctor', 'admin'],
    requiresDoctor: false,
    showInSidebar: false,
    placeholder: 'Describe your current symptoms, when they started, and their severity...',
    template: {
      primarySymptoms: '',
      duration: '',
      severity: '',
      triggers: '',
      relievingFactors: ''
    },
    sortOrder: 32,
  },
  {
    key: 'case_description',
    name: 'Case Description',
    description: 'Overall case description and chief complaint',
    icon: 'FileText',
    color: 'purple',
    category: 'clinical',
    allowedRoles: ['patient', 'doctor', 'admin'],
    requiresDoctor: false,
    showInSidebar: false,
    placeholder: 'Provide a detailed description of your case and what brings you here today...',
    template: {
      chiefComplaint: '',
      detailedDescription: '',
      goals: '',
      concerns: ''
    },
    sortOrder: 33,
  },
];
