import { pgTable, uuid, varchar, text, timestamp, boolean, integer, jsonb } from 'drizzle-orm/pg-core';
import { cases } from './cases';
import { users } from './users';
import { caseDoctors } from './case-doctors';
import { noteTypes } from './note-types';

// Case notes table for all clinical documentation
export const caseNotes = pgTable('case_notes', {
  id: uuid('id').primaryKey().defaultRandom(),
  caseId: uuid('case_id').notNull().references(() => cases.id, { onDelete: 'cascade' }),
  doctorId: uuid('doctor_id').notNull().references(() => users.id), // Current database structure
  noteTypeId: uuid('note_type_id').notNull().references(() => noteTypes.id), // Reference to configurable note type
  structuredContent: jsonb('structured_content').notNull().default('{}'),
  // rawContent field removed as requested
  version: integer('version').notNull().default(1),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),

  // New collaborative editing fields
  yjsDocument: text('yjs_document'), // Store yJS document as base64 encoded binary
  canvasBlocks: jsonb('canvas_blocks').default('[]'),
  medicalCodes: jsonb('medical_codes').default('{}'),
  activeEditors: jsonb('active_editors').default('[]'),
  documentVersion: integer('document_version').default(1),
  collaborationEnabled: boolean('collaboration_enabled').default(true),
});

// Relations for better type inference
export const caseNotesRelations = {
  case: {
    relation: 'one',
    references: cases,
    fields: [caseNotes.caseId],
    referencedFields: [cases.id]
  },
  doctor: {
    relation: 'one',
    references: users,
    fields: [caseNotes.doctorId],
    referencedFields: [users.id]
  },
  noteType: {
    relation: 'one',
    references: noteTypes,
    fields: [caseNotes.noteTypeId],
    referencedFields: [noteTypes.id]
  }
};
