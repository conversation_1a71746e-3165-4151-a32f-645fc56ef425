import { pgTable, uuid, varchar, text, timestamp, integer, pgEnum } from 'drizzle-orm/pg-core';
import { users } from './users';
import { cases } from './cases';

// Appointment status enum
export const appointmentStatusEnum = pgEnum('appointment_status', [
  'scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'
]);

// Appointment type enum
export const appointmentTypeEnum = pgEnum('appointment_type', [
  'consultation', 'follow_up', 'review', 'emergency'
]);

// Appointments table
export const appointments = pgTable('appointments', {
  id: uuid('id').primaryKey().defaultRandom(),
  patientId: uuid('patient_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  doctorId: uuid('doctor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  caseId: uuid('case_id').references(() => cases.id, { onDelete: 'set null' }),
  appointmentType: appointmentTypeEnum('appointment_type').notNull().default('consultation'),
  scheduledAt: timestamp('scheduled_at').notNull(),
  duration: integer('duration').notNull().default(30), // Duration in minutes
  status: appointmentStatusEnum('status').notNull().default('scheduled'),
  notes: text('notes'),
  meetingLink: varchar('meeting_link', { length: 500 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});
