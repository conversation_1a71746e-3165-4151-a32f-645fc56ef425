import { pgTable, uuid, varchar, text, timestamp, boolean, pgEnum, integer, jsonb } from 'drizzle-orm/pg-core';
import { users } from './users';

// Case status enum
export const caseStatusEnum = pgEnum('case_status', [
  'draft', 'submitted', 'in_review', 'assigned', 'completed', 'cancelled'
]);

// Urgency level enum
export const urgencyLevelEnum = pgEnum('urgency_level', [
  'low', 'medium', 'high', 'urgent'
]);

// Document type enum
export const documentTypeEnum = pgEnum('document_type', [
  'lab_report', 'imaging', 'prescription', 'medical_history', 'insurance', 'other'
]);

// Doctor acceptance status enum
export const doctorAcceptanceStatusEnum = pgEnum('doctor_acceptance_status', [
  'pending', 'accepted', 'declined', 'closed'
]);

// Cases table - clinical details now stored in case_notes table
export const cases = pgTable('cases', {
  id: uuid('id').primaryKey().defaultRandom(),
  patientId: uuid('patient_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(),
  urgencyLevel: urgencyLevelEnum('urgency_level').notNull().default('medium'),
  specialtyRequired: varchar('specialty_required', { length: 100 }),
  status: caseStatusEnum('status').notNull().default('draft'),
  // Note: structured_content column removed - clinical details are stored in case_notes table
  // Note: assignedDoctorId column removed - using case_doctors table for multi-doctor assignments
  submittedAt: timestamp('submitted_at'),
  assignedAt: timestamp('assigned_at'),
  completedAt: timestamp('completed_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Note: caseDoctors table moved to case-doctors.ts for better organization

// Medical documents table
export const medicalDocuments = pgTable('medical_documents', {
  id: uuid('id').primaryKey().defaultRandom(),
  caseId: uuid('case_id').references(() => cases.id, { onDelete: 'cascade' }),
  uploadedBy: uuid('uploaded_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  documentType: documentTypeEnum('document_type').notNull().default('other'),
  fileName: varchar('file_name', { length: 255 }).notNull(),
  originalFileName: varchar('original_file_name', { length: 255 }).notNull(),
  filePath: varchar('file_path', { length: 500 }).notNull(),
  fileSize: integer('file_size').notNull(),
  mimeType: varchar('mime_type', { length: 100 }).notNull(),
  isDeleted: boolean('is_deleted').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});
