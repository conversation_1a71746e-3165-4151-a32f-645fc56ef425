import { pgTable, uuid, varchar, text, boolean, timestamp, jsonb, primaryKey } from 'drizzle-orm/pg-core';
import { users } from './users';

// Roles table - defines available roles in the system
export const roles = pgTable('roles', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 50 }).notNull().unique(), // e.g., 'patient', 'doctor', 'admin', 'agent'
  displayName: varchar('display_name', { length: 100 }).notNull(), // e.g., 'Healthcare Provider', 'System Administrator'
  description: text('description'), // Role description
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Permissions table - defines granular permissions
export const permissions = pgTable('permissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull().unique(), // e.g., 'cases:read', 'cases:write', 'api/cases:get'
  displayName: varchar('display_name', { length: 150 }).notNull(), // e.g., 'Read Cases', 'Manage User Accounts'
  description: text('description'), // Permission description
  resource: varchar('resource', { length: 50 }).notNull(), // e.g., 'cases', 'users', 'documents'
  action: varchar('action', { length: 50 }).notNull(), // e.g., 'read', 'write', 'delete', 'manage'
  scope: varchar('scope', { length: 50 }).default('global'), // e.g., 'global', 'own', 'assigned', 'department'
  
  // Filter conditions for data access (JSON format)
  // Example: { "patientId": "{{userId}}", "status": ["active", "pending"] }
  filterConditions: jsonb('filter_conditions'),
  
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Role-Permission mapping table
export const rolePermissions = pgTable('role_permissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  roleId: uuid('role_id').references(() => roles.id, { onDelete: 'cascade' }).notNull(),
  permissionId: uuid('permission_id').references(() => permissions.id, { onDelete: 'cascade' }).notNull(),
  
  // Override filter conditions for this specific role-permission combination
  overrideFilterConditions: jsonb('override_filter_conditions'),
  
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  pk: primaryKey({ columns: [table.roleId, table.permissionId] }),
}));

// User-Role mapping table (many-to-many)
export const userRoles = pgTable('user_roles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  roleId: uuid('role_id').references(() => roles.id, { onDelete: 'cascade' }).notNull(),
  
  // Additional context for this user-role assignment
  assignedBy: uuid('assigned_by').references(() => users.id),
  assignedAt: timestamp('assigned_at').defaultNow().notNull(),
  expiresAt: timestamp('expires_at'), // Optional expiration for temporary roles
  
  // Context-specific data (e.g., department, location, case assignments)
  context: jsonb('context'),
  
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  pk: primaryKey({ columns: [table.userId, table.roleId] }),
}));

// Permission audit log for compliance
export const permissionAuditLog = pgTable('permission_audit_log', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  permissionName: varchar('permission_name', { length: 100 }).notNull(),
  resource: varchar('resource', { length: 50 }).notNull(),
  action: varchar('action', { length: 50 }).notNull(),
  
  // Request context
  endpoint: varchar('endpoint', { length: 200 }),
  method: varchar('method', { length: 10 }),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  
  // Result
  granted: boolean('granted').notNull(),
  reason: text('reason'), // Why permission was granted/denied
  filterApplied: jsonb('filter_applied'), // What filters were applied
  
  timestamp: timestamp('timestamp').defaultNow().notNull(),
});

// Types for TypeScript
export type Role = typeof roles.$inferSelect;
export type NewRole = typeof roles.$inferInsert;

export type Permission = typeof permissions.$inferSelect;
export type NewPermission = typeof permissions.$inferInsert;

export type RolePermission = typeof rolePermissions.$inferSelect;
export type NewRolePermission = typeof rolePermissions.$inferInsert;

export type UserRole = typeof userRoles.$inferSelect;
export type NewUserRole = typeof userRoles.$inferInsert;

export type PermissionAuditLog = typeof permissionAuditLog.$inferSelect;
export type NewPermissionAuditLog = typeof permissionAuditLog.$inferInsert;
