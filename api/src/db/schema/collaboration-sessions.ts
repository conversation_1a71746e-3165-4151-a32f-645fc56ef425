import { pgTable, uuid, timestamp, boolean, jsonb, varchar, index, unique } from 'drizzle-orm/pg-core';
import { cases } from './cases';
import { noteTypes } from './note-types';
import { users } from './users';

// Collaboration sessions table
export const collaborationSessions = pgTable('collaboration_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  caseId: uuid('case_id').notNull().references(() => cases.id, { onDelete: 'cascade' }),
  noteTypeId: uuid('note_type_id').notNull().references(() => noteTypes.id, { onDelete: 'cascade' }),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  sessionStart: timestamp('session_start').notNull().defaultNow(),
  sessionEnd: timestamp('session_end'),
  isActive: boolean('is_active').notNull().default(true),
  cursorPosition: jsonb('cursor_position'),
  userColor: varchar('user_color', { length: 7 }), // Hex color for user identification
}, (table) => ({
  activeSessionsIdx: index('idx_collaboration_active').on(table.caseId, table.noteTypeId, table.isActive),
  uniqueActiveSession: unique().on(table.caseId, table.noteTypeId, table.userId, table.isActive),
}));

export type CollaborationSession = typeof collaborationSessions.$inferSelect;
export type NewCollaborationSession = typeof collaborationSessions.$inferInsert;
