import { pgTable, uuid, text, timestamp, boolean } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';
import { cases, medicalDocuments } from './cases';

// Case discussions table for collaborative case communication
export const caseDiscussions = pgTable('case_discussions', {
  id: uuid('id').primaryKey().defaultRandom(),
  caseId: uuid('case_id').notNull().references(() => cases.id, { onDelete: 'cascade' }),
  authorId: uuid('author_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  content: text('content').notNull(),
  hasAttachments: boolean('has_attachments').notNull().default(false),
  isRead: boolean('is_read').notNull().default(false),
  isDeleted: boolean('is_deleted').notNull().default(false),
  // Patient visibility flag - determines if patients can see this message
  // Patient messages are always visible to patients (true by default for patient authors)
  // Doctor/admin messages can be marked as visible or hidden from patients
  isVisibleToPatient: boolean('is_visible_to_patient').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Discussion attachments table to link discussions with documents
export const discussionAttachments = pgTable('discussion_attachments', {
  id: uuid('id').primaryKey().defaultRandom(),
  discussionId: uuid('discussion_id').notNull().references(() => caseDiscussions.id, { onDelete: 'cascade' }),
  documentId: uuid('document_id').notNull().references(() => medicalDocuments.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Define relations using the new Drizzle relations API
export const caseDiscussionsRelations = relations(caseDiscussions, ({ one }) => ({
  case: one(cases, {
    fields: [caseDiscussions.caseId],
    references: [cases.id],
  }),
  author: one(users, {
    fields: [caseDiscussions.authorId],
    references: [users.id],
  }),
}));

export const discussionAttachmentsRelations = relations(discussionAttachments, ({ one }) => ({
  discussion: one(caseDiscussions, {
    fields: [discussionAttachments.discussionId],
    references: [caseDiscussions.id],
  }),
  document: one(medicalDocuments, {
    fields: [discussionAttachments.documentId],
    references: [medicalDocuments.id],
  }),
}));

// Type inference from Drizzle tables
export type CaseDiscussion = typeof caseDiscussions.$inferSelect;
export type NewCaseDiscussion = typeof caseDiscussions.$inferInsert;
export type DiscussionAttachment = typeof discussionAttachments.$inferSelect;
export type NewDiscussionAttachment = typeof discussionAttachments.$inferInsert;
