import { pgTable, uuid, varchar, text, timestamp, boolean, pgEnum } from 'drizzle-orm/pg-core';

// User role enum
export const userRoleEnum = pgEnum('user_role', ['patient', 'doctor', 'agent', 'admin', 'Admin', 'Sales', 'Ops', 'Agent', 'Exec']);

// Social login provider enum
export const socialProviderEnum = pgEnum('social_provider', ['google', 'whatsapp', 'facebook', 'apple', 'microsoft']);

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }), // Made optional for social logins
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  role: userRoleEnum('role').notNull().default('patient'),
  isActive: boolean('is_active').notNull().default(true),
  isEmailVerified: boolean('is_email_verified').notNull().default(false),
  emailVerificationToken: varchar('email_verification_token', { length: 255 }),
  passwordResetToken: varchar('password_reset_token', { length: 255 }),
  passwordResetExpires: timestamp('password_reset_expires'),
  lastLoginAt: timestamp('last_login_at'),
  // Social login fields
  profilePictureUrl: varchar('profile_picture_url', { length: 500 }),
  phoneNumber: varchar('phone_number', { length: 20 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Social logins table for tracking multiple social accounts per user
export const socialLogins = pgTable('social_logins', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  provider: socialProviderEnum('provider').notNull(),
  providerId: varchar('provider_id', { length: 255 }).notNull(), // Google ID, WhatsApp number, etc.
  providerEmail: varchar('provider_email', { length: 255 }), // Email from provider
  providerData: text('provider_data'), // JSON string for additional provider data
  isVerified: boolean('is_verified').notNull().default(false),
  lastUsedAt: timestamp('last_used_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// OTP verification table for WhatsApp and other OTP-based auth
export const otpVerifications = pgTable('otp_verifications', {
  id: uuid('id').primaryKey().defaultRandom(),
  identifier: varchar('identifier', { length: 255 }).notNull(), // Phone number or email
  identifierType: varchar('identifier_type', { length: 50 }).notNull(), // 'phone' or 'email'
  otp: varchar('otp', { length: 10 }).notNull(),
  purpose: varchar('purpose', { length: 50 }).notNull(), // 'login', 'registration', 'verification'
  isUsed: boolean('is_used').notNull().default(false),
  expiresAt: timestamp('expires_at').notNull(),
  attempts: varchar('attempts', { length: 10 }).notNull().default('0'),
  maxAttempts: varchar('max_attempts', { length: 10 }).notNull().default('3'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// User profiles table for extended information
export const userProfiles = pgTable('user_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  phoneNumber: varchar('phone_number', { length: 20 }),
  dateOfBirth: timestamp('date_of_birth'),
  gender: varchar('gender', { length: 20 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 100 }),
  country: varchar('country', { length: 100 }),
  zipCode: varchar('zip_code', { length: 20 }),
  emergencyContactName: varchar('emergency_contact_name', { length: 200 }),
  emergencyContactPhone: varchar('emergency_contact_phone', { length: 20 }),
  emergencyContactRelationship: varchar('emergency_contact_relationship', { length: 100 }),
  profilePictureUrl: varchar('profile_picture_url', { length: 500 }),
  bio: text('bio'),
  // Preferences fields
  notificationEmail: boolean('notification_email').default(true),
  notificationSms: boolean('notification_sms').default(false),
  notificationPush: boolean('notification_push').default(true),
  privacyShareDataForResearch: boolean('privacy_share_data_for_research').default(false),
  privacyAllowMarketingCommunications: boolean('privacy_allow_marketing_communications').default(false),
  // Medical information fields
  bloodType: varchar('blood_type', { length: 10 }),
  insuranceProvider: varchar('insurance_provider', { length: 200 }),
  insurancePolicyNumber: varchar('insurance_policy_number', { length: 100 }),
  allergies: text('allergies'), // JSON array stored as text
  medications: text('medications'), // JSON array stored as text
  medicalConditions: text('medical_conditions'), // JSON array stored as text
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Audit log for HIPAA compliance
export const auditLogs = pgTable('audit_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id),
  action: varchar('action', { length: 100 }).notNull(),
  resource: varchar('resource', { length: 100 }).notNull(),
  resourceId: varchar('resource_id', { length: 255 }),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  metadata: text('metadata'), // JSON string for additional context
  timestamp: timestamp('timestamp').notNull().defaultNow(),
});

// Type inference from Drizzle tables

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type UserProfile = typeof userProfiles.$inferSelect;
export type NewUserProfile = typeof userProfiles.$inferInsert;
export type AuditLog = typeof auditLogs.$inferSelect;
export type NewAuditLog = typeof auditLogs.$inferInsert;
export type SocialLogin = typeof socialLogins.$inferSelect;
export type NewSocialLogin = typeof socialLogins.$inferInsert;
export type OtpVerification = typeof otpVerifications.$inferSelect;
export type NewOtpVerification = typeof otpVerifications.$inferInsert;
