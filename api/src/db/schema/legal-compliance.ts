import { pgTable, serial, text, timestamp, varchar, integer, boolean, uuid } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { users } from "./users";

/**
 * Legal compliance templates table
 * Stores the base templates for legal and compliance documents
 */
export const legalComplianceTemplates = pgTable("legal_compliance_templates", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: uuid("created_by"),
  isActive: boolean("is_active").default(true).notNull(),
});

/**
 * Legal compliance versions table
 * Stores different versions of legal compliance document templates
 */
export const legalComplianceVersions = pgTable("legal_compliance_versions", {
  id: uuid("id").primaryKey().defaultRandom(),
  templateId: uuid("template_id").notNull().references(() => legalComplianceTemplates.id),
  version: integer("version").notNull(),
  content: text("content").notNull(),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: uuid("created_by"),
});

/**
 * User legal agreement records table
 * Tracks which users have agreed to which legal document versions
 */
export const userLegalAgreements = pgTable("user_legal_agreements", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").notNull(),
  documentVersionId: uuid("document_version_id").notNull().references(() => legalComplianceVersions.id),
  consentedAt: timestamp("consented_at").defaultNow().notNull(),
  ipAddress: varchar("ip_address", { length: 45 }),
  userAgent: text("user_agent"),
  isRevoked: boolean("is_revoked").default(false).notNull(),
  revokedAt: timestamp("revoked_at"),
});

// Relations
export const legalComplianceTemplatesRelations = relations(legalComplianceTemplates, ({ many }) => ({
  versions: many(legalComplianceVersions),
}));

export const legalComplianceVersionsRelations = relations(legalComplianceVersions, ({ one, many }) => ({
  template: one(legalComplianceTemplates, {
    fields: [legalComplianceVersions.templateId],
    references: [legalComplianceTemplates.id],
  }),
  userAgreements: many(userLegalAgreements),
}));

export const userLegalAgreementsRelations = relations(userLegalAgreements, ({ one }) => ({
  documentVersion: one(legalComplianceVersions, {
    fields: [userLegalAgreements.documentVersionId],
    references: [legalComplianceVersions.id],
  }),
}));
