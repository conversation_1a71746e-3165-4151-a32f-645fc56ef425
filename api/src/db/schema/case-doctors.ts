import { pgTable, uuid, varchar, timestamp, boolean, pgEnum, integer, text } from 'drizzle-orm/pg-core';
import { cases } from './cases';
import { users } from './users';

// Doctor acceptance status enum
export const doctorAcceptanceStatusEnum = pgEnum('doctor_acceptance_status', [
  'pending', 'accepted', 'declined', 'closed'
]);

// Junction table for case-doctor relationships
export const caseDoctors = pgTable('case_doctors', {
  id: uuid('id').primaryKey().defaultRandom(),
  caseId: uuid('case_id').notNull().references(() => cases.id, { onDelete: 'cascade' }),
  doctorId: uuid('doctor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  assignedBy: uuid('assigned_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  assignedAt: timestamp('assigned_at').notNull().defaultNow(),
  acceptanceStatus: doctorAcceptanceStatusEnum('acceptance_status').notNull().default('pending'),
  acceptedAt: timestamp('accepted_at'),
  isActive: boolean('is_active').notNull().default(true),
  timeSpentMinutes: integer('time_spent_minutes').notNull().default(0),
  lastActivityAt: timestamp('last_activity_at'),
  notes: text('notes'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Relations for better type inference
export const caseDoctorsRelations = {
  case: {
    relation: 'one',
    references: cases,
    fields: [caseDoctors.caseId],
    referencedFields: [cases.id]
  },
  doctor: {
    relation: 'one',
    references: users,
    fields: [caseDoctors.doctorId],
    referencedFields: [users.id]
  },
  assignedByUser: {
    relation: 'one',
    references: users,
    fields: [caseDoctors.assignedBy],
    referencedFields: [users.id]
  }
};

// Unique constraint to prevent duplicate doctor assignments
export const caseDoctorsConstraints = {
  uniqueCaseDoctor: {
    columns: [caseDoctors.caseId, caseDoctors.doctorId],
    name: 'unique_case_doctor'
  }
};
