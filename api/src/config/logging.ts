/**
 * Logging Configuration
 * Environment-specific logging setup following the comprehensive logging strategy
 */

export interface LoggingConfig {
  level: string;
  enableConsole: boolean;
  enableFile: boolean;
  enableStructuredLogging: boolean;
  logDirectory: string;
  maxFileSize: number;
  maxFiles: number;
  enablePerformanceLogging: boolean;
  enableSecurityLogging: boolean;
  enableAuditLogging: boolean;
}

/**
 * Get logging configuration based on environment
 */
export function getLoggingConfig(): LoggingConfig {
  const env = process.env.NODE_ENV || 'development';
  
  const baseConfig: LoggingConfig = {
    logDirectory: process.env.LOG_DIRECTORY || 'logs',
    maxFileSize: parseInt(process.env.LOG_MAX_FILE_SIZE || '10485760'), // 10MB
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '10'),
    enableStructuredLogging: true,
    enablePerformanceLogging: true,
    enableSecurityLogging: true,
    enableAuditLogging: true,
  };

  switch (env) {
    case 'production':
      return {
        ...baseConfig,
        level: 'info', // INFO+ in production (no DEBUG logs)
        enableConsole: false, // No console output in production
        enableFile: true, // File logging enabled
      };

    case 'staging':
      return {
        ...baseConfig,
        level: 'info', // INFO+ in staging
        enableConsole: true, // Console for debugging staging issues
        enableFile: true, // File logging enabled
      };

    case 'test':
      return {
        ...baseConfig,
        level: 'warn', // Only WARN+ in tests (reduce noise)
        enableConsole: false, // No console output in tests
        enableFile: false, // No file logging in tests
        enablePerformanceLogging: false, // Disable performance logging in tests
      };

    case 'development':
    default:
      return {
        ...baseConfig,
        level: 'debug', // DEBUG level in development
        enableConsole: true, // Console output for development
        enableFile: false, // No file logging in development (optional)
      };
  }
}

/**
 * Log Level Guidelines
 * 
 * DEBUG (Development Only):
 * - Variable values during development
 * - Flow control debugging
 * - Temporary debugging statements
 * - NOT logged in production
 * 
 * INFO (Production Debugging):
 * - API request/response flows with context (user, session, request ID)
 * - Business logic milestones
 * - Data operations with context
 * - System state changes
 * - MUST include context for traceability in production
 * 
 * WARN (Ticket-worthy Issues):
 * - Recoverable errors
 * - Deprecated API usage
 * - Rate limiting triggered
 * - Validation failures
 * - Performance degradation
 * - Configuration issues
 * 
 * ERROR (Wake up the CTO):
 * - System failures
 * - Database connection failures
 * - External service failures
 * - Data corruption
 * - Security breaches
 * - Payment processing failures
 * - Unhandled exceptions
 */

/**
 * Context Requirements by Environment
 * 
 * Development:
 * - Context optional for DEBUG logs
 * - Context recommended for INFO+ logs
 * 
 * Production:
 * - Context REQUIRED for all INFO logs
 * - Must include at least one of: userId, sessionId, requestId
 * - Logs without context will generate warnings
 */

/**
 * Security and Audit Logging
 * 
 * Always logged regardless of level:
 * - Authentication attempts (success/failure)
 * - Authorization failures
 * - Data access (HIPAA compliance)
 * - Data modifications
 * - Administrative actions
 * - Privilege escalations
 * - Account lockouts
 * - Password resets
 */

export const SECURITY_EVENTS = {
  AUTH_SUCCESS: 'User authentication successful',
  AUTH_FAILURE: 'User authentication failed',
  PERMISSION_DENIED: 'Permission denied for resource access',
  SUSPICIOUS_ACTIVITY: 'Suspicious activity detected',
  DATA_ACCESS: 'Sensitive data accessed',
  DATA_MODIFICATION: 'Sensitive data modified',
  ACCOUNT_LOCKED: 'User account locked',
  PASSWORD_RESET: 'Password reset requested/completed',
  PRIVILEGE_ESCALATION: 'User privilege escalation detected',
} as const;

export const AUDIT_ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  EXPORT: 'export',
  IMPORT: 'import',
  SHARE: 'share',
  DOWNLOAD: 'download',
} as const;
