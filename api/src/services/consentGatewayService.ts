import { db } from '../db';
import { 
  cases,
  userLegalAgreements,
  legalComplianceTemplates,
  legalComplianceVersions 
} from '../db/schema';
import { eq, and, desc } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
export interface ConsentGatewayResult {
  canActivate: boolean;
  requiredConsents: Array<{
    templateId: string;
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  blockingReason?: string;
}

export class ConsentGatewayService {
  
  /**
   * Check if a case can be activated (submitted) based on consent requirements
   */
  async checkCaseActivation(caseId: string, userId: string, userRole: string): Promise<ConsentGatewayResult> {
    try {
      // Get required consents for case creation based on user role
      const requiredConsents = await this.getRequiredConsentsForCaseCreation(userRole);
      
      if (requiredConsents.length === 0) {
        return { canActivate: true, requiredConsents: [] };
      }

      // Check which consents user has already accepted
      const acceptedConsents = await this.getUserAcceptedConsents(userId);
      const acceptedTemplateIds = acceptedConsents.map(c => c.templateId);

      // Filter out already accepted consents
      const pendingConsents = requiredConsents.filter(
        consent => !acceptedTemplateIds.includes(consent.templateId)
      );

      if (pendingConsents.length === 0) {
        return { canActivate: true, requiredConsents: [] };
      }

      return {
        canActivate: false,
        requiredConsents: pendingConsents,
        blockingReason: `Case cannot be submitted until ${pendingConsents.length} consent(s) are accepted`
      };
    } catch (error) {
      logger.error('Error checking case activation:', error);
      return {
        canActivate: false,
        requiredConsents: [],
        blockingReason: 'Error checking consent requirements'
      };
    }
  }

  /**
   * Activate a case after consent requirements are met
   */
  async activateCase(caseId: string, userId: string, userRole: string): Promise<{ success: boolean; message: string }> {
    try {
      // Check if case can be activated
      const gatewayResult = await this.checkCaseActivation(caseId, userId, userRole);
      
      if (!gatewayResult.canActivate) {
        return {
          success: false,
          message: gatewayResult.blockingReason || 'Consent requirements not met'
        };
      }

      // Update case status from 'draft' to 'submitted'
      await db
        .update(cases)
        .set({
          status: 'submitted',
          submittedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(cases.id, caseId));

      return {
        success: true,
        message: 'Case submitted successfully'
      };
    } catch (error) {
      logger.error('Error activating case:', error);
      return {
        success: false,
        message: 'Failed to submit case'
      };
    }
  }

  /**
   * Check if user login can proceed based on consent requirements
   */
  async checkUserLoginActivation(userId: string, userRole: string): Promise<ConsentGatewayResult> {
    try {
      // Get required consents for login based on user role
      const requiredConsents = await this.getRequiredConsentsForLogin(userRole);
      
      if (requiredConsents.length === 0) {
        return { canActivate: true, requiredConsents: [] };
      }

      // Check which consents user has already accepted
      const acceptedConsents = await this.getUserAcceptedConsents(userId);
      const acceptedTemplateIds = acceptedConsents.map(c => c.templateId);

      // Filter out already accepted consents
      const pendingConsents = requiredConsents.filter(
        consent => !acceptedTemplateIds.includes(consent.templateId)
      );

      if (pendingConsents.length === 0) {
        return { canActivate: true, requiredConsents: [] };
      }

      return {
        canActivate: false,
        requiredConsents: pendingConsents,
        blockingReason: `Login access blocked until ${pendingConsents.length} consent(s) are accepted`
      };
    } catch (error) {
      logger.error('Error checking user login activation:', error);
      return {
        canActivate: false,
        requiredConsents: [],
        blockingReason: 'Error checking consent requirements'
      };
    }
  }

  /**
   * Private helper methods
   */
  private async getUserAcceptedConsents(userId: string) {
    return await db
      .select({
        templateId: legalComplianceVersions.templateId,
      })
      .from(userLegalAgreements)
      .innerJoin(legalComplianceVersions, eq(userLegalAgreements.documentVersionId, legalComplianceVersions.id))
      .where(eq(userLegalAgreements.userId, userId));
  }

  private async getRequiredConsentsForCaseCreation(userRole: string) {
    const consents = [];
    const templates = await this.getAllActiveTemplates();
    const templateMap = new Map(templates.map(t => [t.title, t]));

    // Define consent rules for case creation
    const caseCreationRules = [
      {
        title: 'Continuia Patient Consent Form',
        roles: ['patient'],
        priority: 'high' as const,
      },
    ];

    // Filter rules based on role
    for (const rule of caseCreationRules) {
      if (rule.roles.includes(userRole)) {
        const template = templateMap.get(rule.title);
        if (template) {
          consents.push({
            templateId: template.id,
            title: template.title,
            description: template.description || '',
            priority: rule.priority,
          });
        }
      }
    }

    return consents;
  }

  private async getRequiredConsentsForLogin(userRole: string) {
    const consents = [];
    const templates = await this.getAllActiveTemplates();
    const templateMap = new Map(templates.map(t => [t.title, t]));

    // Define consent rules for login
    const loginRules = [
      {
        title: 'Continuia Patient Terms of Service (TOS)',
        roles: ['patient', 'doctor', 'admin', 'agent'], // All users
        priority: 'high' as const,
      },
      {
        title: 'Physician Onboarding Packet',
        roles: ['doctor'],
        priority: 'high' as const,
      },
    ];

    // Filter rules based on role
    for (const rule of loginRules) {
      if (rule.roles.includes(userRole)) {
        const template = templateMap.get(rule.title);
        if (template) {
          consents.push({
            templateId: template.id,
            title: template.title,
            description: template.description || '',
            priority: rule.priority,
          });
        }
      }
    }

    return consents;
  }

  private async getAllActiveTemplates() {
    return await db
      .select()
      .from(legalComplianceTemplates)
      .where(eq(legalComplianceTemplates.isActive, true));
  }
}

export const consentGatewayService = new ConsentGatewayService();
