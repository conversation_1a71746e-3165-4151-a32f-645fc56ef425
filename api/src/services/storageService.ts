import { Client } from 'minio';
import { v4 as uuidv4 } from 'uuid';
import { AppError } from '../middleware/errorHandler';

import { logger } from '../utils/structuredLogger';
// MinIO client configuration
const minioClient = new Client({
  endPoint: process.env.MINIO_ENDPOINT || 'minio',
  port: parseInt(process.env.MINIO_PORT || '9000'),
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ROOT_USER || 'minioadmin', // Default development value
  secretKey: process.env.MINIO_ROOT_PASSWORD || 'minioadmin', // Default development value
});

// Bucket names
const BUCKET_NAME = 'medical-documents';
const PROFILES_BUCKET = 'profiles';

// Ensure buckets exist on startup
const ensureBucket = async () => {
  try {
    // Ensure medical documents bucket
    const bucketExists = await minioClient.bucketExists(BUCKET_NAME);
    if (!bucketExists) {
      await minioClient.makeBucket(BUCKET_NAME, 'us-east-1');
    }
    
    // Ensure profiles bucket
    const profilesBucketExists = await minioClient.bucketExists(PROFILES_BUCKET);
    if (!profilesBucketExists) {
      await minioClient.makeBucket(PROFILES_BUCKET, 'us-east-1');
    }
  } catch (error) {
    logger.error('Error ensuring MinIO buckets:', error);
  }
};

// Initialize buckets
ensureBucket();

/**
 * Storage service for handling file operations
 */
export const storageService = {
  /**
   * Upload a file to storage
   * @param file File buffer and metadata
   * @param userId User ID who is uploading the file
   * @returns File path and metadata
   */
  async uploadFile(file: {
    buffer: Buffer;
    originalname: string;
    mimetype: string;
    size: number;
  }, userId: string) {
    try {
      // Generate unique filename
      const fileExtension = file.originalname.split('.').pop();
      const fileName = `${uuidv4()}.${fileExtension}`;
      const filePath = `users/${userId}/${fileName}`;

      // Upload to MinIO
      await minioClient.putObject(
        BUCKET_NAME,
        filePath,
        file.buffer,
        file.size,
        {
          'Content-Type': file.mimetype,
          'Original-Name': file.originalname,
        }
      );

      return {
        fileName,
        filePath,
        originalFileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype,
      };
    } catch (error) {
      logger.error('Storage upload error:', error);
      throw new AppError('Failed to upload file to storage', 500);
    }
  },

  /**
   * Get a file from storage
   * @param filePath Path to the file in storage
   * @returns Stream of the file
   */
  async getFile(filePath: string) {
    try {
      return await minioClient.getObject(BUCKET_NAME, filePath);
    } catch (error) {
      logger.error('Storage get file error:', error);
      throw new AppError('Failed to get file from storage', 500);
    }
  },

  /**
   * Delete a file from storage
   * @param filePath Path to the file in storage
   */
  async deleteFile(filePath: string) {
    try {
      await minioClient.removeObject(BUCKET_NAME, filePath);
    } catch (error) {
      logger.error('Storage delete file error:', error);
      throw new AppError('Failed to delete file from storage', 500);
    }
  },

  /**
   * Check if a file exists in storage
   * @param filePath Path to the file in storage
   * @returns Boolean indicating if the file exists
   */
  async fileExists(filePath: string) {
    try {
      await minioClient.statObject(BUCKET_NAME, filePath);
      return true;
    } catch (error) {
      return false;
    }
  }
};
