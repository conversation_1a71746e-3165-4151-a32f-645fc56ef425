import { db } from '../db/index.js';
import { cases } from '../db/schema/cases.js';
import { caseDoctors } from '../db/schema/case-doctors.js';
import { users } from '../db/schema/users.js';
import { eq, and, inArray } from 'drizzle-orm';
import { AppError } from '../middleware/errorHandler.js';

import { logger } from '../utils/structuredLogger';
export interface CaseDoctorAssignment {
  id: string;
  caseId: string;
  doctorId: string;
  assignedBy: string;
  assignedAt: Date;
  isActive: boolean;
  doctor?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    specialization?: string;
  };
}

// Cache for doctor assignment checks to prevent repeated database queries
interface AssignmentCacheEntry {
  isAssigned: boolean;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class AssignmentCache {
  private cache = new Map<string, AssignmentCacheEntry>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes default TTL

  private getCacheKey(doctorId: string, caseId: string): string {
    return `${doctorId}:${caseId}`;
  }

  get(doctorId: string, caseId: string): boolean | null {
    const key = this.getCacheKey(doctorId, caseId);
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    logger.debug(`Cache HIT for doctor assignment: ${doctorId} -> ${caseId} = ${entry.isAssigned}`);
    return entry.isAssigned;
  }

  set(doctorId: string, caseId: string, isAssigned: boolean, ttl?: number): void {
    const key = this.getCacheKey(doctorId, caseId);
    const entry: AssignmentCacheEntry = {
      isAssigned,
      timestamp: Date.now(),
      ttl: ttl || this.DEFAULT_TTL
    };

    this.cache.set(key, entry);
    logger.debug('Debug output', undefined, { data: `Cache SET for doctor assignment: ${doctorId} -> ${caseId} = ${isAssigned} (TTL: ${entry.ttl}ms)` });
  }

  invalidate(doctorId: string, caseId: string): void {
    const key = this.getCacheKey(doctorId, caseId);
    this.cache.delete(key);
    logger.debug(`Cache INVALIDATED for doctor assignment: ${doctorId} -> ${caseId}`);
  }

  invalidateDoctor(doctorId: string): void {
    // Remove all cache entries for a specific doctor
    for (const [key] of this.cache) {
      if (key.startsWith(`${doctorId}:`)) {
        this.cache.delete(key);
      }
    }
    logger.debug(`Cache INVALIDATED for all assignments of doctor: ${doctorId}`);
  }

  invalidateCase(caseId: string): void {
    // Remove all cache entries for a specific case
    for (const [key] of this.cache) {
      if (key.endsWith(`:${caseId}`)) {
        this.cache.delete(key);
      }
    }
    logger.debug(`Cache INVALIDATED for all assignments to case: ${caseId}`);
  }

  clear(): void {
    this.cache.clear();
    logger.debug('Assignment cache CLEARED');
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Global cache instance
const assignmentCache = new AssignmentCache();

export class CaseDoctorService {
  /**
   * Get doctor information by ID
   */
  static async getDoctorById(doctorId: string): Promise<any | null> {
    if (!doctorId) {
      logger.warn('getDoctorById called with empty doctorId');
      return null;
    }
    
    try {
      const [doctor] = await db
        .select({
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
        })
        .from(users)
        .where(eq(users.id, doctorId))
        .limit(1);
      
      if (!doctor) {
        logger.warn('No doctor found with ID: ${doctorId}');
        return null;
      }
      
      logger.debug('Debug output', undefined, { data: `Found doctor: ${doctor.firstName} ${doctor.lastName} (${doctorId})` });
      return doctor;
    } catch (error) {
      logger.error(`Error fetching doctor by ID ${doctorId}:`, error);
      return null;
    }
  }
  /**
   * Assign a doctor to a case
   */
  static async assignDoctorToCase(
    caseId: string,
    doctorId: string,
    assignedBy: string
  ): Promise<CaseDoctorAssignment> {
    try {
      // Check if case exists
      const [caseExists] = await db
        .select()
        .from(cases)
        .where(eq(cases.id, caseId))
        .limit(1);

      if (!caseExists) {
        throw new AppError('Case not found', 404);
      }

      // Check if doctor exists (temporarily remove role check due to database schema issue)
      const [doctorExists] = await db
        .select()
        .from(users)
        .where(eq(users.id, doctorId))
        .limit(1);

      if (!doctorExists) {
        throw new AppError('Doctor not found', 404);
      }

      // Check if doctor is already assigned to this case
      const [existingAssignment] = await db
        .select()
        .from(caseDoctors)
        .where(
          and(
            eq(caseDoctors.caseId, caseId),
            eq(caseDoctors.doctorId, doctorId),
            eq(caseDoctors.isActive, true)
          )
        )
        .limit(1);

      if (existingAssignment) {
        throw new AppError('Doctor is already assigned to this case', 400);
      }

      // Create new assignment - only use fields that exist in the database
      const [newAssignment] = await db
        .insert(caseDoctors)
        .values({
          caseId,
          doctorId,
          assignedBy,
          assignedAt: new Date(),
          isActive: true,
        })
        .returning();

      // Invalidate cache for this doctor-case combination
      assignmentCache.invalidate(doctorId, caseId);
      logger.debug(`Cache invalidated after assigning doctor ${doctorId} to case ${caseId}`);

      return {
        id: newAssignment.id,
        caseId: newAssignment.caseId,
        doctorId: newAssignment.doctorId || '',
        assignedBy: newAssignment.assignedBy || '',
        assignedAt: newAssignment.assignedAt,
        isActive: newAssignment.isActive,
        doctor: {
          id: doctorExists.id,
          firstName: doctorExists.firstName,
          lastName: doctorExists.lastName,
          email: doctorExists.email,
          specialization: '', // No specialization field in users table
        },
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to assign doctor to case', 500);
    }
  }

  /**
   * Remove a doctor from a case
   */
  static async removeDoctorFromCase(
    caseId: string,
    doctorId: string,
    removedBy: string
  ): Promise<void> {
    try {
      // Check if assignment exists
      const [existingAssignment] = await db
        .select()
        .from(caseDoctors)
        .where(
          and(
            eq(caseDoctors.caseId, caseId),
            eq(caseDoctors.doctorId, doctorId),
            eq(caseDoctors.isActive, true)
          )
        )
        .limit(1);

      if (!existingAssignment) {
        throw new AppError('Doctor assignment not found', 404);
      }

      // Deactivate assignment instead of deleting for audit trail
      await db
        .update(caseDoctors)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(caseDoctors.id, existingAssignment.id));

      // Invalidate cache for this doctor-case combination
      assignmentCache.invalidate(doctorId, caseId);
      logger.debug(`Cache invalidated after removing doctor ${doctorId} from case ${caseId}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to remove doctor from case', 500);
    }
  }

  /**
   * Get all doctors assigned to a case with complete doctor information
   */
  static async getAssignedDoctors(caseId: string): Promise<CaseDoctorAssignment[]> {
    try {
      // Validate input
      if (!caseId) {
        logger.warn('getAssignedDoctors called with empty caseId');
        return []; // Return empty array instead of throwing error
      }

      logger.debug(`Getting assigned doctors for case: ${caseId}`);

      // Check if case exists
      let caseExists;
      try {
        [caseExists] = await db
          .select({ id: cases.id })
          .from(cases)
          .where(eq(cases.id, caseId))
          .limit(1);
      } catch (dbError) {
        logger.error('Database error checking case existence: ${dbError}');
        return []; // Return empty array on database error
      }

      if (!caseExists) {
        logger.warn('Case not found with ID: ${caseId}');
        return []; // Return empty array instead of throwing error
      }

      // Get all active doctor assignments for this case
      let assignments = [];
      try {
        assignments = await db
          .select({
            id: caseDoctors.id,
            caseId: caseDoctors.caseId,
            doctorId: caseDoctors.doctorId,
            assignedBy: caseDoctors.assignedBy,
            assignedAt: caseDoctors.assignedAt,
            isActive: caseDoctors.isActive,
          })
          .from(caseDoctors)
          .where(
            and(
              eq(caseDoctors.caseId, caseId),
              eq(caseDoctors.isActive, true)
            )
          ) || [];
        
        logger.debug(`Found ${assignments.length} doctor assignments for case ${caseId}`);
      } catch (dbError) {
        logger.error('Database error fetching assignments: ${dbError}');
        return []; // Return empty array on database error
      }

      // If no assignments found, return empty array
      if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
        console.info(`No doctor assignments found for case: ${caseId}`);
        return [];
      }

      // Extract valid doctor IDs from assignments
      const doctorIds = assignments
        .filter(a => a && typeof a === 'object')
        .map(a => a.doctorId)
        .filter(id => id && typeof id === 'string') as string[];
      
      logger.debug('Debug output', undefined, { data: `Extracted doctor IDs: ${JSON.stringify(doctorIds)}` });
      
      // If no valid doctor IDs, return assignments without doctor details
      if (!doctorIds || doctorIds.length === 0) {
        logger.warn(`No valid doctor IDs found for case: ${caseId}`);
        return assignments
          .filter(assignment => assignment && typeof assignment === 'object')
          .map(assignment => ({
            id: assignment.id || '',
            caseId: assignment.caseId || '',
            doctorId: assignment.doctorId || '',
            assignedBy: assignment.assignedBy || '',
            assignedAt: assignment.assignedAt || new Date(),
            isActive: !!assignment.isActive,
            doctor: undefined,
          }));
      }
      
      // Fetch all doctors in a single query
      let doctors: any[] = [];
      try {
        logger.debug('Debug output', undefined, { data: `Fetching doctor details for IDs: ${JSON.stringify(doctorIds)}` });
        doctors = await db
          .select({
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
          })
          .from(users)
          .where(inArray(users.id, doctorIds)) || [];
        
        // Log the results for debugging
        logger.debug('Debug output', undefined, { data: `Found ${doctors.length} doctors: ${JSON.stringify(doctors.map(d => ({ 
          id: d.id, 
          name: `${d.firstName || ''} ${d.lastName || ''}`.trim()
        })))}` });
        
        // Check if any doctors are missing
        if (doctors.length < doctorIds.length) {
          logger.warn(`Some doctors were not found: ${doctorIds.length} IDs vs ${doctors.length} found`);
        }
      } catch (dbError) {
        logger.error(`Database error fetching doctors: ${dbError}`);
        // Continue with empty doctors array
      }

      // Create a map of doctors by ID for easy lookup
      const doctorMap = new Map();
      if (doctors && Array.isArray(doctors)) {
        doctors.forEach(doctor => {
          if (doctor && typeof doctor === 'object' && doctor.id) {
            doctorMap.set(doctor.id, doctor);
          }
        });
      }

      logger.debug(`Created doctor map with ${doctorMap.size} entries`);

      // Map assignments with doctor details
      const result = assignments
        .filter(assignment => assignment && typeof assignment === 'object')
        .map(assignment => {
          const doctorId = assignment.doctorId;
          const doctor = doctorId ? doctorMap.get(doctorId) : undefined;
          
          // Log whether we found the doctor for this assignment
          if (doctorId) {
            if (doctor) {
              logger.debug(`Found doctor for ID ${doctorId}: ${doctor.firstName} ${doctor.lastName}`);
            } else {
              logger.warn('Doctor not found for ID ${doctorId}');
            }
          }
          
          return {
            id: assignment.id || '',
            caseId: assignment.caseId || '',
            doctorId: assignment.doctorId || '',
            assignedBy: assignment.assignedBy || '',
            assignedAt: assignment.assignedAt || new Date(),
            isActive: !!assignment.isActive,
            doctor: doctor && typeof doctor === 'object' ? {
              id: doctor.id || '',
              firstName: doctor.firstName || '',
              lastName: doctor.lastName || '',
              email: doctor.email || '',
              specialization: '', // No specialization field in users table
            } : undefined,
          };
        });
        
      logger.debug(`Returning ${result.length} doctor assignments with doctor details`);
      return result;
    } catch (error) {
      logger.error('Error getting assigned doctors:', error);
      // Return empty array instead of throwing error to prevent API failures
      return [];
    }
  }

  /**
   * Get all cases assigned to a doctor
   */
  static async getCasesForDoctor(doctorId: string): Promise<string[]> {
    try {
      logger.debug(`Getting cases for doctor ID: ${doctorId}`);
      
      // First check if the doctor exists
      const doctor = await this.getDoctorById(doctorId);
      if (!doctor) {
        logger.warn('Doctor with ID ${doctorId} not found in users table');
        return [];
      }
      
      logger.debug(`Found doctor: ${doctor.firstName} ${doctor.lastName}`);
      
      // Get case assignments - ONLY active assignments, no fallback
      const assignments = await db
        .select({ caseId: caseDoctors.caseId })
        .from(caseDoctors)
        .where(
          and(
            eq(caseDoctors.doctorId, doctorId),
            eq(caseDoctors.isActive, true)
          )
        )
        .execute();
      
      logger.debug(`Found ${assignments.length} case assignments for doctor ${doctorId}`);
      
      const caseIds = assignments.map(assignment => assignment.caseId);
      logger.debug('Debug output', undefined, { data: `Returning ${caseIds.length} case IDs for doctor ${doctorId}: ${JSON.stringify(caseIds)}` });
      return caseIds;
    } catch (error) {
      logger.error(`Error in getCasesForDoctor for ${doctorId}:`, error);
      return []; // Return empty array instead of throwing to avoid 500 errors
    }
  }

  /**
   * Check if a doctor is assigned to a case (with caching)
   */
  static async isDoctorAssignedToCase(doctorId: string, caseId: string): Promise<boolean> {
    try {
      logger.debug(`isDoctorAssignedToCase: Checking doctor ${doctorId} for case ${caseId}`);

      // Check cache first
      const cachedResult = assignmentCache.get(doctorId, caseId);
      if (cachedResult !== null) {
        return cachedResult;
      }

      logger.debug(`Cache MISS for doctor assignment: ${doctorId} -> ${caseId}, querying database`);

      // Only select columns that actually exist in the database
      const [assignment] = await db
        .select({
          id: caseDoctors.id,
          caseId: caseDoctors.caseId,
          doctorId: caseDoctors.doctorId,
          isActive: caseDoctors.isActive
        })
        .from(caseDoctors)
        .where(
          and(
            eq(caseDoctors.caseId, caseId),
            eq(caseDoctors.doctorId, doctorId),
            eq(caseDoctors.isActive, true)
          )
        )
        .limit(1);

      const isAssigned = !!assignment;

      logger.debug('Debug output', undefined, { data: `isDoctorAssignedToCase: Assignment found: ${isAssigned ? 'YES' : 'NO'}` });
      if (assignment) {
        logger.debug('Debug output', undefined, { data: `isDoctorAssignedToCase: Assignment details: ${JSON.stringify({
          id: assignment.id,
          caseId: assignment.caseId,
          doctorId: assignment.doctorId,
          isActive: assignment.isActive
        })}` });
      }

      // Cache the result for 5 minutes
      assignmentCache.set(doctorId, caseId, isAssigned);

      return isAssigned;
    } catch (error) {
      logger.error('isDoctorAssignedToCase: Error checking assignment:', error);
      return false;
    }
  }

  /**
   * Check if a doctor has accepted their assignment to a case
   */
  static async hasDoctorAcceptedCase(doctorId: string, caseId: string): Promise<boolean> {
    try {
      logger.debug(`hasDoctorAcceptedCase: Checking if doctor ${doctorId} has accepted case ${caseId}`);
      
      const [assignment] = await db
        .select({
          id: caseDoctors.id,
          acceptanceStatus: caseDoctors.acceptanceStatus,
          acceptedAt: caseDoctors.acceptedAt,
        })
        .from(caseDoctors)
        .where(
          and(
            eq(caseDoctors.caseId, caseId),
            eq(caseDoctors.doctorId, doctorId),
            eq(caseDoctors.isActive, true)
          )
        )
        .limit(1);

      const hasAccepted = assignment?.acceptanceStatus === 'accepted';
      logger.debug('Debug output', undefined, { data: `hasDoctorAcceptedCase: Doctor ${doctorId} acceptance status for case ${caseId}: ${assignment?.acceptanceStatus || 'not_assigned'} - ${hasAccepted ? 'ACCEPTED' : 'NOT_ACCEPTED'}` });
      
      return hasAccepted;
    } catch (error) {
      logger.error('hasDoctorAcceptedCase: Error checking acceptance status:', error);
      return false;
    }
  }

  /**
   * Get doctor IDs assigned to multiple cases (for bulk operations)
   */
  static async getDoctorIdsForCases(caseIds: string[]): Promise<Record<string, string[]>> {
    try {
      if (caseIds.length === 0) return {};

      const assignments = await db
        .select({
          caseId: caseDoctors.caseId,
          doctorId: caseDoctors.doctorId,
        })
        .from(caseDoctors)
        .where(
          and(
            inArray(caseDoctors.caseId, caseIds),
            eq(caseDoctors.isActive, true)
          )
        );

      const result: Record<string, string[]> = {};
      for (const assignment of assignments) {
        if (assignment.caseId && assignment.doctorId) {
          if (!result[assignment.caseId]) {
            result[assignment.caseId] = [];
          }
          result[assignment.caseId].push(assignment.doctorId);
        }
      }

      return result;
    } catch (error) {
      throw new AppError('Failed to get doctor assignments for cases', 500);
    }
  }

  /**
   * Cache management methods for debugging and monitoring
   */
  static clearAssignmentCache(): void {
    assignmentCache.clear();
  }

  static invalidateAssignmentCache(doctorId: string, caseId: string): void {
    assignmentCache.invalidate(doctorId, caseId);
  }

  static invalidateDoctorAssignmentCache(doctorId: string): void {
    assignmentCache.invalidateDoctor(doctorId);
  }

  static invalidateCaseAssignmentCache(caseId: string): void {
    assignmentCache.invalidateCase(caseId);
  }

  static getAssignmentCacheStats(): { size: number; keys: string[] } {
    return assignmentCache.getStats();
  }
}
