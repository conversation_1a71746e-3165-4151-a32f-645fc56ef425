import { db } from '../db';
import { 
  consentAssignmentRules, 
  userConsentRequirements, 
  caseConsentRequirements,
  legalComplianceTemplates,
  legalComplianceVersions,
  userLegalAgreements 
} from '../db/schema';
import { eq, and, or, isNull, desc } from 'drizzle-orm';

export interface ConsentAssignmentConditions {
  userRole?: string;
  firstLogin?: boolean;
  caseType?: string;
  userStatus?: string;
  [key: string]: any;
}

export interface ConsentRequirement {
  id: string;
  templateId: string;
  templateTitle: string;
  latestVersion: number;
  isRequired: boolean;
  priority: 'high' | 'medium' | 'low';
  dueDate?: Date;
  assignedAt: Date;
  triggerContext?: any;
}

export class ConsentManagementService {
  
  /**
   * Get all pending consent requirements for a user
   */
  async getUserPendingConsents(userId: string): Promise<ConsentRequirement[]> {
    const requirements = await db
      .select({
        id: userConsentRequirements.id,
        templateId: userConsentRequirements.templateId,
        templateTitle: legalComplianceTemplates.title,
        isRequired: userConsentRequirements.isBlocking,
        priority: (consentAssignmentRules.priority as unknown) as 'high' | 'medium' | 'low',
        dueDate: userConsentRequirements.dueDate,
        assignedAt: userConsentRequirements.assignedAt,
        triggerContext: userConsentRequirements.triggerContext,
      })
      .from(userConsentRequirements)
      .innerJoin(consentAssignmentRules, eq(userConsentRequirements.ruleId, consentAssignmentRules.id))
      .innerJoin(legalComplianceTemplates, eq(userConsentRequirements.templateId, legalComplianceTemplates.id))
      .where(
        and(
          eq(userConsentRequirements.userId, userId),
          eq(userConsentRequirements.status, 'pending'),
          eq(userConsentRequirements.isBlocking, true)
        )
      )
      .orderBy(desc(consentAssignmentRules.priority));

    // Get latest version for each template
    const enrichedRequirements = await Promise.all(
      requirements.map(async (req) => {
        const latestVersion = await this.getLatestTemplateVersion(req.templateId);
        return {
          ...req,
          latestVersion: latestVersion?.version || 1,
        } as ConsentRequirement;
      })
    );

    return enrichedRequirements;
  }

  /**
   * Check if user has any blocking consent requirements
   */
  async hasBlockingConsents(userId: string): Promise<boolean> {
    const blockingCount = await db
      .select({ count: userConsentRequirements.id })
      .from(userConsentRequirements)
      .where(
        and(
          eq(userConsentRequirements.userId, userId),
          eq(userConsentRequirements.status, 'pending'),
          eq(userConsentRequirements.isBlocking, true)
        )
      );

    return blockingCount.length > 0;
  }

  /**
   * Assign consents based on trigger type and conditions
   */
  async assignConsents(
    triggerType: 'login' | 'role_assignment' | 'case_creation' | 'manual',
    context: {
      userId?: string;
      userRole?: string;
      caseId?: string;
      caseType?: string;
      isFirstLogin?: boolean;
      [key: string]: any;
    }
  ): Promise<void> {
    // Get all active rules for this trigger type
    const rules = await db
      .select()
      .from(consentAssignmentRules)
      .innerJoin(legalComplianceTemplates, eq(consentAssignmentRules.templateId, legalComplianceTemplates.id))
      .where(
        and(
          eq(consentAssignmentRules.triggerType, triggerType),
          eq(consentAssignmentRules.isActive, true),
          eq(legalComplianceTemplates.isActive, true)
        )
      );

    for (const rule of rules) {
      const ruleData = rule.consent_assignment_rules;
      const templateData = rule.legal_compliance_templates;

      // Check if conditions match
      if (this.evaluateConditions(ruleData.conditions as ConsentAssignmentConditions, context)) {
        await this.createConsentRequirement(ruleData, templateData, context);
      }
    }
  }

  /**
   * Accept a consent requirement
   */
  async acceptConsent(
    userId: string,
    requirementId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    // Get the requirement
    const requirement = await db
      .select()
      .from(userConsentRequirements)
      .where(eq(userConsentRequirements.id, requirementId))
      .limit(1);

    if (!requirement.length) {
      throw new Error('Consent requirement not found');
    }

    const req = requirement[0];

    // Get the latest version of the template
    const latestVersion = await this.getLatestTemplateVersion(req.templateId);
    if (!latestVersion) {
      throw new Error('Template version not found');
    }

    // Create user legal agreement
    const [agreement] = await db
      .insert(userLegalAgreements)
      .values({
        userId,
        documentVersionId: latestVersion.id,
        ipAddress,
        userAgent,
      })
      .returning();

    // Update requirement status
    await db
      .update(userConsentRequirements)
      .set({
        status: 'accepted',
        resolvedAt: new Date(),
        resolvedBy: userId,
        agreementId: agreement.id,
      })
      .where(eq(userConsentRequirements.id, requirementId));
  }

  /**
   * Create predefined consent assignment rules
   */
  async createPredefinedRules(): Promise<void> {
    const rules = [
      // Terms of Service for all users on first login
      {
        name: 'Terms of Service - All Users',
        description: 'Require all users to accept Terms of Service on first login',
        triggerType: 'login',
        conditions: { firstLogin: true },
        templateTitle: 'Continuia Patient Terms of Service (TOS)',
        isRequired: true,
        priority: 'high',
      },
      // Physician Onboarding for doctors
      {
        name: 'Physician Onboarding - Doctors',
        description: 'Require doctors to complete onboarding packet',
        triggerType: 'role_assignment',
        conditions: { userRole: 'doctor' },
        templateTitle: 'Physician Onboarding Packet',
        isRequired: true,
        priority: 'high',
      },
      // Patient Consent Form for case creation
      {
        name: 'Patient Consent - Case Creation',
        description: 'Require patient consent form when creating new cases',
        triggerType: 'case_creation',
        conditions: {},
        templateTitle: 'Continuia Patient Consent Form',
        isRequired: true,
        priority: 'high',
      },
    ];

    for (const ruleData of rules) {
      // Find the template
      const template = await db
        .select()
        .from(legalComplianceTemplates)
        .where(eq(legalComplianceTemplates.title, ruleData.templateTitle))
        .limit(1);

      if (template.length) {
        await db
          .insert(consentAssignmentRules)
          .values({
            templateId: template[0].id,
            triggerType: ruleData.triggerType as any,
            conditions: ruleData.conditions,
            name: ruleData.name,
            description: ruleData.description,
            isRequired: ruleData.isRequired,
            priority: ruleData.priority as any,
          })
          .onConflictDoNothing();
      }
    }
  }

  /**
   * Private helper methods
   */
  private async getLatestTemplateVersion(templateId: string) {
    const [version] = await db
      .select()
      .from(legalComplianceVersions)
      .where(eq(legalComplianceVersions.templateId, templateId))
      .orderBy(desc(legalComplianceVersions.version))
      .limit(1);

    return version;
  }

  private evaluateConditions(
    ruleConditions: ConsentAssignmentConditions | null,
    context: any
  ): boolean {
    if (!ruleConditions) return true;

    for (const [key, value] of Object.entries(ruleConditions)) {
      if (context[key] !== value) {
        return false;
      }
    }

    return true;
  }

  private async createConsentRequirement(
    rule: any,
    template: any,
    context: any
  ): Promise<void> {
    if (context.userId) {
      // Check if requirement already exists
      const existing = await db
        .select()
        .from(userConsentRequirements)
        .where(
          and(
            eq(userConsentRequirements.userId, context.userId),
            eq(userConsentRequirements.ruleId, rule.id),
            eq(userConsentRequirements.status, 'pending')
          )
        )
        .limit(1);

      if (!existing.length) {
        await db
          .insert(userConsentRequirements)
          .values({
            userId: context.userId,
            ruleId: rule.id,
            templateId: template.id,
            isBlocking: rule.isRequired,
            triggerContext: context,
          });
      }
    }

    if (context.caseId) {
      // Check if case requirement already exists
      const existing = await db
        .select()
        .from(caseConsentRequirements)
        .where(
          and(
            eq(caseConsentRequirements.caseId, context.caseId),
            eq(caseConsentRequirements.ruleId, rule.id),
            eq(caseConsentRequirements.status, 'pending')
          )
        )
        .limit(1);

      if (!existing.length) {
        await db
          .insert(caseConsentRequirements)
          .values({
            caseId: context.caseId,
            ruleId: rule.id,
            templateId: template.id,
            requiredFromUserId: context.userId,
            requiredFromRole: context.userRole,
            isBlocking: rule.isRequired,
          });
      }
    }
  }
}

export const consentManagementService = new ConsentManagementService();
