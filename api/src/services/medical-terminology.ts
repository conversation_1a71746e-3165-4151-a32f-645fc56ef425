import axios from 'axios';
import NodeCache from 'node-cache';
import { db, sql } from '../db/index';
import { medicalTerminologyCache } from '../db/schema';
import { eq, and, gt } from 'drizzle-orm';

// Cache for 1 hour in memory, 24 hours in database
const memoryCache = new NodeCache({ stdTTL: 3600 });

export interface SnomedConcept {
  conceptId: string;
  fsn: {
    term: string;
  };
  pt: {
    term: string;
  };
  active: boolean;
  definitionStatus: string;
}

export interface RxNavDrug {
  rxcui: string;
  name: string;
  synonym?: string;
  tty?: string;
  language?: string;
}

export interface MedicalSearchResult {
  id: string;
  term: string;
  description?: string;
  code: string;
  system: 'snomed' | 'rxnav';
  active?: boolean;
}

class MedicalTerminologyService {
  private readonly SNOMED_BASE_URL = 'https://browser.ihtsdotools.org/snowstorm/snomed-ct';
  private readonly RXNAV_BASE_URL = 'https://rxnav.nlm.nih.gov/REST';

  /**
   * Search SNOMED CT concepts
   */
  async searchSnomed(searchTerm: string, limit: number = 20): Promise<MedicalSearchResult[]> {
    const cacheKey = `snomed:${searchTerm}:${limit}`;
    
    // Check memory cache first
    const cached = memoryCache.get<MedicalSearchResult[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Check database cache
    const dbCached = await this.getFromDbCache('snomed', searchTerm);
    if (dbCached) {
      memoryCache.set(cacheKey, dbCached);
      return dbCached;
    }

    try {
      const response = await axios.get(`${this.SNOMED_BASE_URL}/browser/MAIN/concepts`, {
        params: {
          term: searchTerm,
          activeFilter: true,
          limit,
          returnIdOnly: false
        },
        timeout: 5000
      });

      const concepts: SnomedConcept[] = response.data.items || [];
      const results: MedicalSearchResult[] = concepts.map(concept => ({
        id: concept.conceptId,
        term: concept.pt?.term || concept.fsn?.term || 'Unknown term',
        description: concept.fsn?.term,
        code: concept.conceptId,
        system: 'snomed' as const,
        active: concept.active
      }));

      // Cache results
      memoryCache.set(cacheKey, results);
      await this.saveToDbCache('snomed', searchTerm, results);

      return results;
    } catch (error) {
      console.error('SNOMED search error:', error);
      return [];
    }
  }

  /**
   * Search RxNAV drugs
   */
  async searchRxNav(searchTerm: string, limit: number = 20): Promise<MedicalSearchResult[]> {
    const cacheKey = `rxnav:${searchTerm}:${limit}`;
    
    // Check memory cache first
    const cached = memoryCache.get<MedicalSearchResult[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Check database cache
    const dbCached = await this.getFromDbCache('rxnav', searchTerm);
    if (dbCached) {
      memoryCache.set(cacheKey, dbCached);
      return dbCached;
    }

    try {
      const response = await axios.get(`${this.RXNAV_BASE_URL}/drugs.json`, {
        params: {
          name: searchTerm
        },
        timeout: 5000
      });

      const drugGroup = response.data.drugGroup;
      if (!drugGroup || !drugGroup.conceptGroup) {
        return [];
      }

      const results: MedicalSearchResult[] = [];
      
      // Process concept groups
      for (const group of drugGroup.conceptGroup) {
        if (group.conceptProperties) {
          const concepts = group.conceptProperties.slice(0, limit);
          for (const concept of concepts) {
            results.push({
              id: concept.rxcui,
              term: concept.name,
              description: concept.synonym,
              code: concept.rxcui,
              system: 'rxnav' as const
            });
          }
        }
      }

      // Cache results
      memoryCache.set(cacheKey, results);
      await this.saveToDbCache('rxnav', searchTerm, results);

      return results.slice(0, limit);
    } catch (error) {
      console.error('RxNAV search error:', error);
      return [];
    }
  }

  /**
   * Get concept details by ID
   */
  async getSnomedConcept(conceptId: string): Promise<SnomedConcept | null> {
    try {
      const response = await axios.get(`${this.SNOMED_BASE_URL}/browser/MAIN/concepts/${conceptId}`, {
        timeout: 5000
      });
      return response.data;
    } catch (error) {
      console.error('SNOMED concept fetch error:', error);
      return null;
    }
  }

  /**
   * Get drug details by RxCUI
   */
  async getRxNavDrug(rxcui: string): Promise<any> {
    try {
      const response = await axios.get(`${this.RXNAV_BASE_URL}/rxcui/${rxcui}/properties.json`, {
        timeout: 5000
      });
      return response.data;
    } catch (error) {
      console.error('RxNAV drug fetch error:', error);
      return null;
    }
  }

  /**
   * Combined search across both terminologies
   */
  async searchMedicalTerms(searchTerm: string, limit: number = 20): Promise<{
    snomed: MedicalSearchResult[];
    rxnav: MedicalSearchResult[];
  }> {
    const [snomedResults, rxnavResults] = await Promise.all([
      this.searchSnomed(searchTerm, Math.ceil(limit / 2)),
      this.searchRxNav(searchTerm, Math.ceil(limit / 2))
    ]);

    return {
      snomed: snomedResults,
      rxnav: rxnavResults
    };
  }

  /**
   * Save search results to database cache
   */
  private async saveToDbCache(
    terminologyType: 'snomed' | 'rxnav',
    searchTerm: string,
    results: MedicalSearchResult[]
  ): Promise<void> {
    try {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // Cache for 24 hours

      await db.insert(medicalTerminologyCache).values({
        terminologyType,
        searchTerm: searchTerm.toLowerCase(),
        results: results as any,
        expiresAt
      }).onConflictDoUpdate({
        target: [medicalTerminologyCache.terminologyType, medicalTerminologyCache.searchTerm],
        set: {
          results: results as any,
          expiresAt
        }
      });
    } catch (error) {
      console.error('Database cache save error:', error);
    }
  }

  /**
   * Get search results from database cache
   */
  private async getFromDbCache(
    terminologyType: 'snomed' | 'rxnav',
    searchTerm: string
  ): Promise<MedicalSearchResult[] | null> {
    try {
      const cached = await db
        .select()
        .from(medicalTerminologyCache)
        .where(
          and(
            eq(medicalTerminologyCache.terminologyType, terminologyType),
            eq(medicalTerminologyCache.searchTerm, searchTerm.toLowerCase()),
            gt(medicalTerminologyCache.expiresAt, new Date())
          )
        )
        .limit(1);

      if (cached.length > 0) {
        return cached[0].results as MedicalSearchResult[];
      }
      return null;
    } catch (error) {
      console.error('Database cache fetch error:', error);
      return null;
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredCache(): Promise<void> {
    try {
      await db.execute(sql`SELECT cleanup_expired_medical_cache()`);
    } catch (error) {
      console.error('Cache cleanup error:', error);
    }
  }
}

export const medicalTerminologyService = new MedicalTerminologyService();
