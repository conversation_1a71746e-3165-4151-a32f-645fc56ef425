import { AppError } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { db } from '../db/index.js';
import { caseDoctors } from '../db/schema/case-doctors.js';
import { eq, and } from 'drizzle-orm';
import * as SharedPermissions from '/shared/permissions.js';
import type { UserContext, ResourceContext } from '/shared/permissions.js';

import { logger } from '../utils/structuredLogger';
// Access the default export which contains our actual exports
const { PERMISSION_MATRIX, PermissionService } = SharedPermissions.default;

export interface User {
  id: string;
  role: 'patient' | 'doctor' | 'agent' | 'admin';
  email: string;
}

export interface AuthorizationContext {
  user: User;
  resource?: any;
  resourceType: string;
  action: string;
  resourceId?: string;
}

export class AuthorizationService {
  /**
   * Check if user has permission to perform action on resource
   */
  static async authorize(context: AuthorizationContext): Promise<boolean> {
    const { user, resource, resourceType, action, resourceId } = context;

    try {
      // Debug logging
      logger.debug('Authorization context:', { requestId: 'context-needed' }, {
        data: {
          user: { id: user.id, role: user.role },
          resourceType,
          action,
          resourceId,
          hasResource: !!resource
        }
      });
      
      // Convert user to shared UserContext format
      // Handle all possible role formats more robustly
      let userRole = 'guest';
      
      // Debug the user object to see what fields are available
      logger.info('User object for role extraction:', { requestId: 'context-needed' }, { data: JSON.stringify(user) });
      
      // Try all possible ways to extract role
      if (user.role && typeof user.role === 'string') {
        userRole = user.role;
        logger.info('Found role in user.role:', { requestId: 'context-needed' }, { data: userRole });
      } else if (user.primaryRole && typeof user.primaryRole === 'string') {
        userRole = user.primaryRole;
        logger.info('Found role in user.primaryRole:', { requestId: 'context-needed' }, { data: userRole });
      } else if (user.roles && Array.isArray(user.roles) && user.roles.length > 0) {
        userRole = user.roles[0];
        logger.info('Found role in user.roles[0]:', { requestId: 'context-needed' }, { data: userRole });
      } else if (user.opalUser && user.opalUser.role) {
        userRole = user.opalUser.role;
        logger.info('Found role in user.opalUser.role:', { requestId: 'context-needed' }, { data: userRole });
      } else {
        logger.warn('Could not extract role from user object, using default:', undefined, { data: userRole });
      }
      
      // For <EMAIL>, force role to doctor for testing
      if (user.email === '<EMAIL>') {
        userRole = 'doctor';
        logger.debug('Force setting role to <NAME_EMAIL>');
      }
      
      const userContext: UserContext = {
        id: user.id,
        role: userRole,
        email: user.email
      };

      // Convert resource to shared ResourceContext format
      const resourceContext: ResourceContext | undefined = resource ? {
        ...resource
      } : undefined;
      
      // Debug logging
      logger.debug('Permission check:', { requestId: 'context-needed' }, {
        data: {
          permission: `${resourceType}:${action}`,
          resourceContext
        }
      });
      logger.debug('PERMISSION_MATRIX keys:', { requestId: 'context-needed' }, {
        data: Object.keys(PERMISSION_MATRIX)
      });
      logger.debug('PERMISSION_MATRIX doctor_credentials:', { requestId: 'context-needed' }, {
        data: PERMISSION_MATRIX.doctor_credentials
      });

      // Use shared permission service
      const isAuthorized = PermissionService.canAccessResource(userContext, `${resourceType}:${action}`, resourceContext);
      
      // Debug logging
      logger.debug('Authorization result:', { requestId: 'context-needed' }, { data: isAuthorized });

      if (isAuthorized) {
        // Log successful authorization
        logAuditEvent(
          user.id,
          'AUTHORIZATION_SUCCESS',
          resourceType,
          {
            action,
            resourceId,
            role: user.role
          }
        );
      } else {
        // Log failed authorization
        logAuditEvent(
          user.id,
          'AUTHORIZATION_FAILED',
          resourceType,
          {
            action,
            resourceId,
            role: user.role,
            allowedRoles: PERMISSION_MATRIX[resourceType as keyof typeof PERMISSION_MATRIX]?.[action as keyof (typeof PERMISSION_MATRIX)[keyof typeof PERMISSION_MATRIX]] || []
          }
        );
      }

      return isAuthorized;
    } catch (error) {
      // Log authorization error
      logAuditEvent(
        user.id,
        'AUTHORIZATION_ERROR',
        resourceType,
        {
          action,
          resourceId,
          role: user.role,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      );
      throw error;
    }
  }

  /**
   * Middleware factory for route protection
   */
  static requirePermission(resourceType: string, action: string) {
    return async (req: any, res: any, next: any) => {
      try {
        const user = req.user;
        if (!user) {
          throw new AppError('Authentication required', 401);
        }

        const resourceId = req.params.id || req.params.caseId || req.params.appointmentId || req.params.documentId;
        
        // For list operations (read without specific resource), we need to handle modifiers differently
        let context: AuthorizationContext;
        
        if (action === 'read' && !resourceId) {
          // For list operations, we check if user has any permission to access the resource type
          // The actual filtering will be done in the route handler
          context = {
            user,
            resource: undefined,
            resourceType,
            action,
            resourceId: undefined
          };
        } else {
          // For specific resource operations, check with resource context
          let resource = null;
          if (resourceId && (action === 'read' || action === 'update' || action === 'delete')) {
            // Resource will be fetched in the route handler if needed
            // This middleware just checks role-based permissions
          }
          
          context = {
            user,
            resource,
            resourceType,
            action,
            resourceId
          };
        }

        const isAuthorized = await AuthorizationService.authorize(context);
        
        if (!isAuthorized) {
          throw new AppError('Insufficient permissions', 403);
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  /**
   * Check if a doctor is assigned to a case (supports multi-doctor assignment)
   */
  static async isDoctorAssignedToCase(doctorId: string, caseId: string): Promise<boolean> {
    try {
      const assignment = await db
        .select()
        .from(caseDoctors)
        .where(
          and(
            eq(caseDoctors.caseId, caseId),
            eq(caseDoctors.doctorId, doctorId),
            eq(caseDoctors.isActive, true)
          )
        )
        .limit(1);
      
      return assignment.length > 0;
    } catch (error) {
      logger.error('Error checking doctor assignment:', error);
      return false;
    }
  }

  /**
   * Check resource ownership for detailed authorization
   */
  static async checkResourceOwnership(
    user: User,
    resource: any,
    resourceType: string,
    action: string
  ): Promise<boolean> {
    const context: AuthorizationContext = {
      user,
      resource,
      resourceType,
      action
    };

    return await this.authorize(context);
  }
}
