import { logger } from '../utils/logger.js';
import { db } from '../db/index.js';
import { roles, permissions, rolePermissions, userRoles } from '../db/schema/permissions.js';
import { eq, and } from 'drizzle-orm';

// Policy evaluation context
export interface PolicyContext {
  user: {
    id: string;
    email: string;
    roles: string[];
    firstName: string;
    lastName: string;
  };
  resource?: {
    id?: string;
    type: string;
    patientId?: string;
    assignedDoctor?: string;
    assignedTo?: string;
    status?: string;
    createdBy?: string;
    [key: string]: any;
  };
  action: string;
  environment: {
    time: Date;
    ip?: string;
    userAgent?: string;
    endpoint?: string;
    method?: string;
  };
  request?: {
    body?: any;
    params?: any;
    query?: any;
  };
}

// Policy evaluation result
export interface PolicyResult {
  allow: boolean;
  reason: string;
  matchedPolicies: string[];
  appliedFilters?: Record<string, any>;
  metadata?: Record<string, any>;
}

// Policy rule definition
export interface PolicyRule {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  effect: 'allow' | 'deny';
  conditions: PolicyCondition[];
  filters?: Record<string, any>;
  priority?: number;
}

// Policy condition types
export type PolicyCondition = 
  | RoleCondition
  | ResourceOwnershipCondition
  | TimeCondition
  | CustomCondition;

export interface RoleCondition {
  type: 'role';
  roles: string[];
  operator?: 'any' | 'all';
}

export interface ResourceOwnershipCondition {
  type: 'ownership';
  field: string; // e.g., 'patientId', 'assignedDoctor'
  userField?: string; // defaults to 'id'
}

export interface TimeCondition {
  type: 'time';
  operator: 'before' | 'after' | 'between';
  value: string | string[];
}

export interface CustomCondition {
  type: 'custom';
  expression: string; // JavaScript expression
}

// OPAL-style Policy Engine
export class PolicyEngine {
  private static instance: PolicyEngine;
  private policies: Map<string, PolicyRule[]> = new Map();
  private auditLog: Array<{ context: PolicyContext; result: PolicyResult; timestamp: Date }> = [];

  static getInstance(): PolicyEngine {
    if (!PolicyEngine.instance) {
      PolicyEngine.instance = new PolicyEngine();
    }
    return PolicyEngine.instance;
  }

  // Initialize policies from database (async)
  async initialize(): Promise<void> {
    await this.loadPoliciesFromDatabase();
  }

  // Load policies from database
  private async loadPoliciesFromDatabase(): Promise<void> {
    try {
      // Get all permissions with their associated roles
      const dbPermissions = await db
        .select({
          permissionId: permissions.id,
          permissionName: permissions.name,
          displayName: permissions.displayName,
          description: permissions.description,
          resource: permissions.resource,
          action: permissions.action,
          scope: permissions.scope,
          filterConditions: permissions.filterConditions,
          roleName: roles.name,
        })
        .from(permissions)
        .innerJoin(rolePermissions, eq(permissions.id, rolePermissions.permissionId))
        .innerJoin(roles, eq(rolePermissions.roleId, roles.id))
        .where(and(
          eq(permissions.isActive, true),
          eq(roles.isActive, true),
          eq(rolePermissions.isActive, true)
        ));

      // Convert database permissions to PolicyRule format
      const policyRules: PolicyRule[] = [];
      const permissionGroups = new Map<string, any[]>();

      // Group permissions by permission ID
      for (const perm of dbPermissions) {
        if (!permissionGroups.has(perm.permissionId)) {
          permissionGroups.set(perm.permissionId, []);
        }
        permissionGroups.get(perm.permissionId)!.push(perm);
      }

      // Create policy rules from grouped permissions
      for (const [permissionId, perms] of permissionGroups.entries()) {
        const firstPerm = perms[0];
        const rolesForPermission = perms.map(p => p.roleName);

        const conditions: PolicyCondition[] = [
          { type: 'role', roles: rolesForPermission }
        ];

        // Add ownership condition based on scope and filter conditions
        if (firstPerm.scope === 'own' && firstPerm.filterConditions) {
          const filters = firstPerm.filterConditions as any;
          if (filters.patientId === '{{userId}}') {
            conditions.push({ type: 'ownership', field: 'patientId' });
          }
          if (filters.assignedDoctor === '{{userId}}') {
            conditions.push({ type: 'ownership', field: 'assignedDoctor' });
          }
          if (filters.uploadedBy === '{{userId}}') {
            conditions.push({ type: 'ownership', field: 'uploadedBy' });
          }
        }

        const policyRule: PolicyRule = {
          id: firstPerm.permissionName,
          name: firstPerm.displayName,
          description: firstPerm.description || '',
          resource: firstPerm.resource,
          action: firstPerm.action,
          effect: 'allow',
          conditions,
          priority: firstPerm.scope === 'global' ? 100 : 50
        };

        policyRules.push(policyRule);
      }

      // Add admin full access policy
      policyRules.push({
        id: 'admin-full-access',
        name: 'Admin Full Access',
        description: 'Administrators have full system access',
        resource: '*',
        action: '*',
        effect: 'allow',
        conditions: [
          { type: 'role', roles: ['admin'] }
        ],
        priority: 200
      });

      // Clear existing policies and load new ones
      this.policies.clear();

      // Group policies by resource for efficient lookup
      for (const policy of policyRules) {
        const key = `${policy.resource}:${policy.action}`;
        if (!this.policies.has(key)) {
          this.policies.set(key, []);
        }
        this.policies.get(key)!.push(policy);
      }

      // Sort policies by priority (higher priority first)
      for (const [key, policyList] of this.policies.entries()) {
        policyList.sort((a, b) => (b.priority || 0) - (a.priority || 0));
      }

      logger.info(`Loaded ${policyRules.length} policies from database`);
    } catch (error) {
      logger.error('Failed to load policies from database:', error);
      // Fallback to basic admin policy if database loading fails
      this.loadFallbackPolicies();
    }
  }

  // Fallback policies in case database loading fails
  private loadFallbackPolicies(): void {
    const fallbackPolicies: PolicyRule[] = [
      {
        id: 'admin-full-access',
        name: 'Admin Full Access',
        description: 'Administrators have full system access',
        resource: '*',
        action: '*',
        effect: 'allow',
        conditions: [
          { type: 'role', roles: ['admin'] }
        ],
        priority: 200
      },
      // Basic patient permissions
      {
        id: 'patient-cases-read',
        name: 'Patient Cases Read',
        description: 'Patients can read their own cases',
        resource: 'cases',
        action: 'read',
        effect: 'allow',
        conditions: [
          { type: 'role', roles: ['patient'] },
          { type: 'ownership', field: 'patientId' }
        ],
        priority: 100
      },
      {
        id: 'patient-appointments-read',
        name: 'Patient Appointments Read',
        description: 'Patients can read their own appointments',
        resource: 'appointments',
        action: 'read',
        effect: 'allow',
        conditions: [
          { type: 'role', roles: ['patient'] },
          { type: 'ownership', field: 'patientId' }
        ],
        priority: 100
      },
      // Basic doctor permissions
      {
        id: 'doctor-cases-read',
        name: 'Doctor Cases Read',
        description: 'Doctors can read assigned cases',
        resource: 'cases',
        action: 'read',
        effect: 'allow',
        conditions: [
          { type: 'role', roles: ['doctor'] }
        ],
        priority: 100
      },
      {
        id: 'doctor-appointments-read',
        name: 'Doctor Appointments Read',
        description: 'Doctors can read their appointments',
        resource: 'appointments',
        action: 'read',
        effect: 'allow',
        conditions: [
          { type: 'role', roles: ['doctor'] }
        ],
        priority: 100
      }
    ];

    for (const policy of fallbackPolicies) {
      const key = `${policy.resource}:${policy.action}`;
      if (!this.policies.has(key)) {
        this.policies.set(key, []);
      }
      this.policies.get(key)!.push(policy);
    }

    logger.warn('Loaded fallback policies due to database error');
  }

  // Evaluate policy for a given context
  async evaluate(context: PolicyContext): Promise<PolicyResult> {
    const startTime = Date.now();
    
    try {
      // Get applicable policies
      const applicablePolicies = this.getApplicablePolicies(context.resource?.type || '', context.action);
      
      if (applicablePolicies.length === 0) {
        const result: PolicyResult = {
          allow: false,
          reason: 'No applicable policies found',
          matchedPolicies: []
        };
        
        this.auditLog.push({ context, result, timestamp: new Date() });
        return result;
      }

      // Evaluate policies in priority order
      const matchedPolicies: string[] = [];
      let finalDecision = false;
      let finalReason = 'No matching conditions';

      for (const policy of applicablePolicies) {
        const conditionResult = await this.evaluateConditions(policy.conditions, context);
        
        if (conditionResult.matches) {
          matchedPolicies.push(policy.id);
          
          if (policy.effect === 'deny') {
            // Deny takes precedence
            finalDecision = false;
            finalReason = `Denied by policy: ${policy.name}`;
            break;
          } else if (policy.effect === 'allow') {
            finalDecision = true;
            finalReason = `Allowed by policy: ${policy.name}`;
            // Continue to check for deny policies
          }
        }
      }

      const result: PolicyResult = {
        allow: finalDecision,
        reason: finalReason,
        matchedPolicies,
        metadata: {
          evaluationTime: Date.now() - startTime,
          policiesEvaluated: applicablePolicies.length
        }
      };

      // Add to audit log
      this.auditLog.push({ context, result, timestamp: new Date() });
      
      // Log security-relevant decisions
      if (!finalDecision) {
        logger.warn(`Access denied for user ${context.user.id}: ${finalReason}`, {
          userId: context.user.id,
          resource: context.resource?.type,
          action: context.action,
          reason: finalReason
        });
      }

      return result;
    } catch (error) {
      logger.error('Policy evaluation error:', error);
      
      const result: PolicyResult = {
        allow: false,
        reason: 'Policy evaluation error',
        matchedPolicies: []
      };
      
      this.auditLog.push({ context, result, timestamp: new Date() });
      return result;
    }
  }

  // Get applicable policies for resource and action
  private getApplicablePolicies(resource: string, action: string): PolicyRule[] {
    const policies: PolicyRule[] = [];
    
    // Exact match
    const exactKey = `${resource}:${action}`;
    if (this.policies.has(exactKey)) {
      policies.push(...this.policies.get(exactKey)!);
    }
    
    // Wildcard resource
    const wildcardResourceKey = `*:${action}`;
    if (this.policies.has(wildcardResourceKey)) {
      policies.push(...this.policies.get(wildcardResourceKey)!);
    }
    
    // Wildcard action
    const wildcardActionKey = `${resource}:*`;
    if (this.policies.has(wildcardActionKey)) {
      policies.push(...this.policies.get(wildcardActionKey)!);
    }
    
    // Double wildcard
    const doubleWildcardKey = '*:*';
    if (this.policies.has(doubleWildcardKey)) {
      policies.push(...this.policies.get(doubleWildcardKey)!);
    }
    
    // Sort by priority
    return policies.sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }

  // Evaluate policy conditions
  private async evaluateConditions(
    conditions: PolicyCondition[], 
    context: PolicyContext
  ): Promise<{ matches: boolean; reason: string }> {
    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition, context);
      if (!result.matches) {
        return result;
      }
    }
    
    return { matches: true, reason: 'All conditions satisfied' };
  }

  // Evaluate individual condition
  private async evaluateCondition(
    condition: PolicyCondition, 
    context: PolicyContext
  ): Promise<{ matches: boolean; reason: string }> {
    switch (condition.type) {
      case 'role':
        return this.evaluateRoleCondition(condition, context);
      
      case 'ownership':
        return this.evaluateOwnershipCondition(condition, context);
      
      case 'time':
        return this.evaluateTimeCondition(condition, context);
      
      case 'custom':
        return this.evaluateCustomCondition(condition, context);
      
      default:
        return { matches: false, reason: 'Unknown condition type' };
    }
  }

  // Evaluate role condition
  private evaluateRoleCondition(
    condition: RoleCondition, 
    context: PolicyContext
  ): { matches: boolean; reason: string } {
    const operator = condition.operator || 'any';
    const userRoles = context.user.roles;
    
    if (operator === 'any') {
      const hasRole = condition.roles.some(role => userRoles.includes(role));
      return {
        matches: hasRole,
        reason: hasRole ? 'User has required role' : `User lacks required roles: ${condition.roles.join(', ')}`
      };
    } else if (operator === 'all') {
      const hasAllRoles = condition.roles.every(role => userRoles.includes(role));
      return {
        matches: hasAllRoles,
        reason: hasAllRoles ? 'User has all required roles' : 'User lacks some required roles'
      };
    }
    
    return { matches: false, reason: 'Invalid role operator' };
  }

  // Evaluate ownership condition
  private evaluateOwnershipCondition(
    condition: ResourceOwnershipCondition,
    context: PolicyContext
  ): { matches: boolean; reason: string } {
    if (!context.resource) {
      return { matches: false, reason: 'No resource context provided' };
    }
    
    const resourceValue = context.resource[condition.field];
    const userField = condition.userField || 'id';
    const userValue = (context.user as any)[userField];
    
    // Check if this is a creation operation by looking at the HTTP method and resource ID
    const isCreation = context.environment.method === 'POST' && !context.resource.id;
    
    if (!resourceValue) {
      if (isCreation) {
        // For creation operations, if the ownership field is not set in resource context,
        // it means the middleware didn't set it properly
        return {
          matches: false,
          reason: `Creation operation but ${condition.field} not set in resource context`
        };
      }
      
      return { matches: false, reason: `Resource field ${condition.field} not found` };
    }
    
    const matches = resourceValue === userValue;
    return {
      matches,
      reason: matches ?
        (isCreation ? 'User creating resource for themselves' : 'User owns the resource') :
        'User does not own the resource'
    };
  }

  // Evaluate time condition
  private evaluateTimeCondition(
    condition: TimeCondition, 
    context: PolicyContext
  ): { matches: boolean; reason: string } {
    const now = context.environment.time;
    
    try {
      switch (condition.operator) {
        case 'before':
          const beforeTime = new Date(condition.value as string);
          return {
            matches: now < beforeTime,
            reason: `Current time ${now.toISOString()} vs ${beforeTime.toISOString()}`
          };
        
        case 'after':
          const afterTime = new Date(condition.value as string);
          return {
            matches: now > afterTime,
            reason: `Current time ${now.toISOString()} vs ${afterTime.toISOString()}`
          };
        
        case 'between':
          const [startTime, endTime] = (condition.value as string[]).map(t => new Date(t));
          const inRange = now >= startTime && now <= endTime;
          return {
            matches: inRange,
            reason: `Current time ${now.toISOString()} in range ${startTime.toISOString()} - ${endTime.toISOString()}`
          };
        
        default:
          return { matches: false, reason: 'Invalid time operator' };
      }
    } catch (error) {
      return { matches: false, reason: 'Invalid time format' };
    }
  }

  // Evaluate custom JavaScript condition
  private evaluateCustomCondition(
    condition: CustomCondition, 
    context: PolicyContext
  ): { matches: boolean; reason: string } {
    try {
      // Create a safe evaluation context
      const evalContext = {
        context,
        user: context.user,
        resource: context.resource,
        action: context.action,
        environment: context.environment,
        request: context.request
      };
      
      // Use Function constructor for safer evaluation than eval()
      const func = new Function('context', 'user', 'resource', 'action', 'environment', 'request', 
        `return ${condition.expression}`);
      
      const result = func(
        evalContext.context,
        evalContext.user,
        evalContext.resource,
        evalContext.action,
        evalContext.environment,
        evalContext.request
      );
      
      return {
        matches: Boolean(result),
        reason: `Custom condition evaluated to: ${result}`
      };
    } catch (error) {
      logger.error('Custom condition evaluation error:', error);
      return { matches: false, reason: 'Custom condition evaluation failed' };
    }
  }

  // Add or update a policy
  addPolicy(policy: PolicyRule): void {
    const key = `${policy.resource}:${policy.action}`;
    if (!this.policies.has(key)) {
      this.policies.set(key, []);
    }
    
    // Remove existing policy with same ID
    const existingPolicies = this.policies.get(key)!;
    const filteredPolicies = existingPolicies.filter(p => p.id !== policy.id);
    
    // Add new policy and sort by priority
    filteredPolicies.push(policy);
    filteredPolicies.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    
    this.policies.set(key, filteredPolicies);
    
    logger.info(`Added/updated policy: ${policy.id}`);
  }

  // Remove a policy
  removePolicy(policyId: string): boolean {
    let removed = false;
    
    for (const [key, policyList] of this.policies.entries()) {
      const originalLength = policyList.length;
      const filteredPolicies = policyList.filter(p => p.id !== policyId);
      
      if (filteredPolicies.length < originalLength) {
        this.policies.set(key, filteredPolicies);
        removed = true;
      }
    }
    
    if (removed) {
      logger.info(`Removed policy: ${policyId}`);
    }
    
    return removed;
  }

  // Get audit log (for compliance and debugging)
  getAuditLog(limit: number = 100): Array<{ context: PolicyContext; result: PolicyResult; timestamp: Date }> {
    return this.auditLog.slice(-limit);
  }

  // Clear audit log
  clearAuditLog(): void {
    this.auditLog = [];
  }
}
