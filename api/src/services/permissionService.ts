import { db } from '../db/index.js';
import { 
  roles, 
  permissions, 
  rolePermissions, 
  userRoles, 
  permissionAuditLog,
  type Permission,
  type Role,
  type UserRole 
} from '../db/schema/permissions.js';
import { users } from '../db/schema/users.js';
import { eq, and, inArray } from 'drizzle-orm';
import { logger } from '../utils/logger.js';

// Enhanced user context with permissions
export interface UserPermissionContext {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: Role[];
  permissions: Permission[];
  isActive: boolean;
}

// Permission check result
export interface PermissionCheckResult {
  granted: boolean;
  reason: string;
  appliedFilters?: any;
  matchedPermissions: Permission[];
}

// Permission filter context for dynamic filtering
export interface FilterContext {
  userId: string;
  userRoles: string[];
  requestData?: any;
  [key: string]: any;
}

export class PermissionService {
  private static instance: PermissionService;
  private permissionCache = new Map<string, Permission[]>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): PermissionService {
    if (!PermissionService.instance) {
      PermissionService.instance = new PermissionService();
    }
    return PermissionService.instance;
  }

  // Get user's complete permission context
  async getUserPermissionContext(userId: string): Promise<UserPermissionContext | null> {
    try {
      // Get user with their roles
      const userWithRoles = await db
        .select({
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          isActive: users.isActive,
          roleName: roles.name,
          roleDisplayName: roles.displayName,
          roleDescription: roles.description,
        })
        .from(users)
        .leftJoin(userRoles, and(
          eq(userRoles.userId, users.id),
          eq(userRoles.isActive, true)
        ))
        .leftJoin(roles, eq(roles.id, userRoles.roleId))
        .where(eq(users.id, userId));

      if (!userWithRoles.length || !userWithRoles[0]) {
        return null;
      }

      const user = userWithRoles[0];
      const userRolesList: Role[] = userWithRoles
        .filter(row => row.roleName)
        .map(row => ({
          id: '', // We'll get this from a separate query if needed
          name: row.roleName!,
          displayName: row.roleDisplayName!,
          description: row.roleDescription,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

      // Get all permissions for user's roles
      const userPermissions = await this.getUserPermissions(userId);

      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roles: userRolesList,
        permissions: userPermissions,
        isActive: user.isActive,
      };
    } catch (error) {
      logger.error('Error getting user permission context:', error);
      return null;
    }
  }

  // Get all permissions for a user (with caching)
  async getUserPermissions(userId: string): Promise<Permission[]> {
    const cacheKey = `user_permissions_${userId}`;
    const now = Date.now();

    // Check cache
    if (this.permissionCache.has(cacheKey)) {
      const expiry = this.cacheExpiry.get(cacheKey);
      if (expiry && expiry > now) {
        return this.permissionCache.get(cacheKey)!;
      }
    }

    try {
      // Get user's permissions through their roles
      const userPermissions = await db
        .select({
          id: permissions.id,
          name: permissions.name,
          displayName: permissions.displayName,
          description: permissions.description,
          resource: permissions.resource,
          action: permissions.action,
          scope: permissions.scope,
          filterConditions: permissions.filterConditions,
          isActive: permissions.isActive,
          createdAt: permissions.createdAt,
          updatedAt: permissions.updatedAt,
          // Override conditions from role-permission mapping
          overrideFilterConditions: rolePermissions.overrideFilterConditions,
        })
        .from(userRoles)
        .innerJoin(rolePermissions, eq(rolePermissions.roleId, userRoles.roleId))
        .innerJoin(permissions, and(
          eq(permissions.id, rolePermissions.permissionId),
          eq(permissions.isActive, true)
        ))
        .where(and(
          eq(userRoles.userId, userId),
          eq(userRoles.isActive, true),
          eq(rolePermissions.isActive, true)
        ));

      // Process permissions and apply overrides
      const processedPermissions: Permission[] = userPermissions.map(perm => ({
        id: perm.id,
        name: perm.name,
        displayName: perm.displayName,
        description: perm.description,
        resource: perm.resource,
        action: perm.action,
        scope: perm.scope,
        // Use override conditions if available, otherwise use default
        filterConditions: perm.overrideFilterConditions || perm.filterConditions,
        isActive: perm.isActive,
        createdAt: perm.createdAt,
        updatedAt: perm.updatedAt,
      }));

      // Cache the result
      this.permissionCache.set(cacheKey, processedPermissions);
      this.cacheExpiry.set(cacheKey, now + this.CACHE_TTL);

      return processedPermissions;
    } catch (error) {
      logger.error('Error getting user permissions:', error);
      return [];
    }
  }

  // Check if user has a specific permission
  async hasPermission(
    userId: string, 
    permissionName: string, 
    context?: FilterContext
  ): Promise<PermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      const matchedPermissions = userPermissions.filter(p => p.name === permissionName);

      if (matchedPermissions.length === 0) {
        await this.logPermissionCheck(userId, permissionName, false, 'Permission not found', context);
        return {
          granted: false,
          reason: 'Permission not found',
          matchedPermissions: [],
        };
      }

      // For now, if user has the permission, grant access
      // In the future, we can add more complex filtering logic here
      const result: PermissionCheckResult = {
        granted: true,
        reason: 'Permission granted',
        matchedPermissions,
      };

      await this.logPermissionCheck(userId, permissionName, true, 'Permission granted', context);
      return result;
    } catch (error) {
      logger.error('Error checking permission:', error);
      await this.logPermissionCheck(userId, permissionName, false, 'Error checking permission', context);
      return {
        granted: false,
        reason: 'Error checking permission',
        matchedPermissions: [],
      };
    }
  }

  // Check if user has permission for a specific resource and action
  async hasResourcePermission(
    userId: string,
    resource: string,
    action: string,
    context?: FilterContext
  ): Promise<PermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      const matchedPermissions = userPermissions.filter(p => 
        p.resource === resource && p.action === action
      );

      if (matchedPermissions.length === 0) {
        const reason = `No permission for ${action} on ${resource}`;
        await this.logPermissionCheck(userId, `${resource}:${action}`, false, reason, context);
        return {
          granted: false,
          reason,
          matchedPermissions: [],
        };
      }

      // Apply scope-based filtering
      const scopeResult = this.applyScopeFiltering(matchedPermissions, context);

      await this.logPermissionCheck(userId, `${resource}:${action}`, scopeResult.granted, scopeResult.reason, context);
      return scopeResult;
    } catch (error) {
      logger.error('Error checking resource permission:', error);
      return {
        granted: false,
        reason: 'Error checking resource permission',
        matchedPermissions: [],
      };
    }
  }

  // Apply scope-based filtering (own, assigned, global, etc.)
  private applyScopeFiltering(
    permissions: Permission[],
    context?: FilterContext
  ): PermissionCheckResult {
    // For now, implement basic scope logic
    // This can be expanded based on specific business requirements

    const hasGlobalScope = permissions.some(p => p.scope === 'global');
    if (hasGlobalScope) {
      return {
        granted: true,
        reason: 'Global scope permission granted',
        matchedPermissions: permissions,
      };
    }

    const hasOwnScope = permissions.some(p => p.scope === 'own');
    if (hasOwnScope && context) {
      // Check if the resource belongs to the user
      // This logic would need to be customized based on the resource type
      return {
        granted: true,
        reason: 'Own scope permission granted',
        matchedPermissions: permissions.filter(p => p.scope === 'own'),
        appliedFilters: { userId: context.userId },
      };
    }

    return {
      granted: false,
      reason: 'No applicable scope found',
      matchedPermissions: [],
    };
  }

  // Generate SQL filters based on permission conditions
  generateSQLFilters(permissions: Permission[], context: FilterContext): any {
    const filters: any = {};

    for (const permission of permissions) {
      if (permission.filterConditions) {
        const conditions = permission.filterConditions as any;
        
        // Replace template variables with actual values
        for (const [key, value] of Object.entries(conditions)) {
          if (typeof value === 'string' && value.includes('{{')) {
            // Replace template variables like {{userId}} with actual values
            const processedValue = value.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
              return context[variable] || match;
            });
            filters[key] = processedValue;
          } else {
            filters[key] = value;
          }
        }
      }

      // Apply scope-based filters
      if (permission.scope === 'own') {
        filters.userId = context.userId;
      } else if (permission.scope === 'assigned') {
        filters.assignedTo = context.userId;
      }
    }

    return filters;
  }

  // Log permission check for audit trail
  private async logPermissionCheck(
    userId: string,
    permissionName: string,
    granted: boolean,
    reason: string,
    context?: FilterContext
  ): Promise<void> {
    try {
      const [resource, action] = permissionName.includes(':') 
        ? permissionName.split(':') 
        : [permissionName, 'unknown'];

      await db.insert(permissionAuditLog).values({
        userId,
        permissionName,
        resource,
        action,
        endpoint: context?.endpoint,
        method: context?.method,
        ipAddress: context?.ipAddress,
        userAgent: context?.userAgent,
        granted,
        reason,
        filterApplied: context ? JSON.stringify(context) : null,
      });
    } catch (error) {
      logger.error('Error logging permission check:', error);
    }
  }

  // Clear cache for a user (call when user roles change)
  clearUserCache(userId: string): void {
    const cacheKey = `user_permissions_${userId}`;
    this.permissionCache.delete(cacheKey);
    this.cacheExpiry.delete(cacheKey);
  }

  // Clear all cache
  clearAllCache(): void {
    this.permissionCache.clear();
    this.cacheExpiry.clear();
  }

  // Add role to user
  async addUserRole(userId: string, roleName: string, assignedBy?: string): Promise<boolean> {
    try {
      // Get role ID
      const [role] = await db
        .select({ id: roles.id })
        .from(roles)
        .where(eq(roles.name, roleName))
        .limit(1);

      if (!role) {
        logger.error(`Role not found: ${roleName}`);
        return false;
      }

      // Add user role
      await db.insert(userRoles).values({
        userId,
        roleId: role.id,
        assignedBy,
        assignedAt: new Date(),
      });

      // Clear user's permission cache
      this.clearUserCache(userId);

      logger.info(`Added role ${roleName} to user ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error adding user role:', error);
      return false;
    }
  }

  // Remove role from user
  async removeUserRole(userId: string, roleName: string): Promise<boolean> {
    try {
      // Get role ID
      const [role] = await db
        .select({ id: roles.id })
        .from(roles)
        .where(eq(roles.name, roleName))
        .limit(1);

      if (!role) {
        logger.error(`Role not found: ${roleName}`);
        return false;
      }

      // Deactivate user role
      await db
        .update(userRoles)
        .set({ isActive: false })
        .where(and(
          eq(userRoles.userId, userId),
          eq(userRoles.roleId, role.id)
        ));

      // Clear user's permission cache
      this.clearUserCache(userId);

      logger.info(`Removed role ${roleName} from user ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error removing user role:', error);
      return false;
    }
  }
}
