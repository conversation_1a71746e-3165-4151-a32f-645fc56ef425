import { logger } from '../utils/logger.js';

// Email service interface
export interface EmailService {
  sendEmail(params: EmailParams): Promise<EmailResult>;
}

export interface EmailParams {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
  fromName?: string;
  replyTo?: string;
  attachments?: EmailAttachment[];
  headers?: Record<string, string>;
}

export interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType?: string;
  cid?: string; // Content-ID for inline attachments
}

export interface EmailResult {
  messageId: string;
  success: boolean;
  error?: string;
  provider?: string;
}

// SMTP Email Service using nodemailer
class SMTPEmailService implements EmailService {
  private transporter: any;

  constructor() {
    this.initializeTransporter();
  }

  private async initializeTransporter() {
    try {
      // Dynamic import for nodemailer (will be installed later)
      const nodemailer = await import('nodemailer');
      
      const config: any = {
        host: process.env.SMTP_HOST || 'localhost',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
        // Additional security options
        tls: {
          rejectUnauthorized: false, // Allow self-signed certificates in development
        },
      };

      // Only add auth if credentials are provided (mailpit doesn't need auth)
      if (process.env.SMTP_USER && process.env.SMTP_PASS) {
        config.auth = {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        };
      }

      this.transporter = nodemailer.createTransport(config);

      // Verify connection
      await this.transporter.verify();
      logger.info('SMTP connection verified successfully');
    } catch (error) {
      logger.error('Failed to initialize SMTP transporter:', error);
      throw error;
    }
  }

  async sendEmail(params: EmailParams): Promise<EmailResult> {
    try {
      if (!this.transporter) {
        await this.initializeTransporter();
      }

      const fromEmail = params.from || process.env.SMTP_FROM_EMAIL || process.env.SMTP_USER;
      const fromName = params.fromName || process.env.SMTP_FROM_NAME || 'Continuia Healthcare';

      const mailOptions = {
        from: `"${fromName}" <${fromEmail}>`,
        to: Array.isArray(params.to) ? params.to.join(', ') : params.to,
        subject: params.subject,
        text: params.text,
        html: params.html,
        replyTo: params.replyTo,
        attachments: params.attachments?.map(att => ({
          filename: att.filename,
          content: att.content,
          contentType: att.contentType,
          cid: att.cid,
        })),
        headers: params.headers,
      };

      const result = await this.transporter.sendMail(mailOptions);

      logger.info('Email sent successfully via SMTP', {
        messageId: result.messageId,
        to: params.to,
        subject: params.subject,
      });

      return {
        messageId: result.messageId,
        success: true,
        provider: 'smtp',
      };
    } catch (error) {
      logger.error('Failed to send email via SMTP:', error);
      return {
        messageId: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: 'smtp',
      };
    }
  }
}

// SendGrid Email Service
class SendGridEmailService implements EmailService {
  private sgMail: any;

  constructor() {
    this.initializeSendGrid();
  }

  private async initializeSendGrid() {
    try {
      // Dynamic import for SendGrid (will be installed later)
      const sgMail = await import('@sendgrid/mail');
      this.sgMail = sgMail.default;
      
      const apiKey = process.env.SENDGRID_API_KEY;
      if (!apiKey) {
        throw new Error('SENDGRID_API_KEY is required');
      }

      this.sgMail.setApiKey(apiKey);
      logger.info('SendGrid initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize SendGrid:', error);
      throw error;
    }
  }

  async sendEmail(params: EmailParams): Promise<EmailResult> {
    try {
      if (!this.sgMail) {
        await this.initializeSendGrid();
      }

      const fromEmail = params.from || process.env.SMTP_FROM_EMAIL;
      const fromName = params.fromName || process.env.SMTP_FROM_NAME || 'Continuia Healthcare';

      const msg = {
        to: params.to,
        from: {
          email: fromEmail,
          name: fromName,
        },
        subject: params.subject,
        text: params.text,
        html: params.html,
        replyTo: params.replyTo,
        attachments: params.attachments?.map(att => ({
          filename: att.filename,
          content: Buffer.isBuffer(att.content) ? att.content.toString('base64') : att.content,
          type: att.contentType,
          disposition: att.cid ? 'inline' : 'attachment',
          contentId: att.cid,
        })),
        headers: params.headers,
      };

      const result = await this.sgMail.send(msg);

      logger.info('Email sent successfully via SendGrid', {
        messageId: result[0].headers['x-message-id'],
        to: params.to,
        subject: params.subject,
      });

      return {
        messageId: result[0].headers['x-message-id'] || 'sendgrid-' + Date.now(),
        success: true,
        provider: 'sendgrid',
      };
    } catch (error) {
      logger.error('Failed to send email via SendGrid:', error);
      return {
        messageId: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: 'sendgrid',
      };
    }
  }
}

// AWS SES Email Service
class SESEmailService implements EmailService {
  private ses: any;

  constructor() {
    this.initializeSES();
  }

  private async initializeSES() {
    try {
      // Dynamic import for AWS SDK (will be installed later)
      const AWS = await import('aws-sdk');
      
      AWS.config.update({
        region: process.env.AWS_REGION || 'us-east-1',
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      });

      this.ses = new AWS.SES();
      logger.info('AWS SES initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AWS SES:', error);
      throw error;
    }
  }

  async sendEmail(params: EmailParams): Promise<EmailResult> {
    try {
      if (!this.ses) {
        await this.initializeSES();
      }

      const fromEmail = params.from || process.env.SMTP_FROM_EMAIL;
      const fromName = params.fromName || process.env.SMTP_FROM_NAME || 'Continuia Healthcare';

      const sesParams = {
        Source: `"${fromName}" <${fromEmail}>`,
        Destination: {
          ToAddresses: Array.isArray(params.to) ? params.to : [params.to],
        },
        Message: {
          Subject: {
            Data: params.subject,
            Charset: 'UTF-8',
          },
          Body: {
            Text: params.text ? {
              Data: params.text,
              Charset: 'UTF-8',
            } : undefined,
            Html: params.html ? {
              Data: params.html,
              Charset: 'UTF-8',
            } : undefined,
          },
        },
        ReplyToAddresses: params.replyTo ? [params.replyTo] : undefined,
      };

      const result = await this.ses.sendEmail(sesParams).promise();

      logger.info('Email sent successfully via AWS SES', {
        messageId: result.MessageId,
        to: params.to,
        subject: params.subject,
      });

      return {
        messageId: result.MessageId,
        success: true,
        provider: 'ses',
      };
    } catch (error) {
      logger.error('Failed to send email via AWS SES:', error);
      return {
        messageId: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: 'ses',
      };
    }
  }
}

// Mock Email Service for development/testing
class MockEmailService implements EmailService {
  async sendEmail(params: EmailParams): Promise<EmailResult> {
    const messageId = `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    logger.info('Mock email sent', {
      messageId,
      to: params.to,
      subject: params.subject,
      html: params.html?.substring(0, 100) + '...',
      text: params.text?.substring(0, 100) + '...',
    });

    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 100));

    return {
      messageId,
      success: true,
      provider: 'mock',
    };
  }
}

// Email Service Factory
export class EmailServiceFactory {
  private static instance: EmailService | null = null;

  static getInstance(): EmailService {
    if (!this.instance) {
      const provider = process.env.EMAIL_PROVIDER || 'mock';
      
      switch (provider.toLowerCase()) {
        case 'smtp':
          this.instance = new SMTPEmailService();
          break;
        case 'sendgrid':
          this.instance = new SendGridEmailService();
          break;
        case 'ses':
          this.instance = new SESEmailService();
          break;
        case 'mock':
        default:
          this.instance = new MockEmailService();
          break;
      }

      logger.info(`Email service initialized with provider: ${provider}`);
    }

    return this.instance;
  }

  // Reset instance (useful for testing)
  static reset(): void {
    this.instance = null;
  }
}

// Convenience function to send emails
export async function sendEmail(params: EmailParams): Promise<EmailResult> {
  const emailService = EmailServiceFactory.getInstance();
  return emailService.sendEmail(params);
}

// Email template helpers
export class EmailTemplateHelper {
  static renderTemplate(template: string, variables: Record<string, any>): string {
    let rendered = template;
    
    // Replace variables in the format {{variableName}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      rendered = rendered.replace(regex, String(value || ''));
    });

    return rendered;
  }

  static createUnsubscribeLink(campaignId: string, userId: string): string {
    const baseUrl = process.env.VITE_API_URL || 'http://localhost:3001/api';
    return `${baseUrl}/campaigns/unsubscribe?campaign=${campaignId}&user=${userId}`;
  }

  static createTrackingPixel(executionId: string): string {
    const baseUrl = process.env.VITE_API_URL || 'http://localhost:3001/api';
    return `<img src="${baseUrl}/campaigns/track/open/${executionId}" width="1" height="1" style="display:none;" />`;
  }

  static wrapLinksForTracking(html: string, executionId: string): string {
    const baseUrl = process.env.VITE_API_URL || 'http://localhost:3001/api';
    
    // Replace all href attributes with tracking links
    return html.replace(
      /href="([^"]+)"/g,
      `href="${baseUrl}/campaigns/track/click/${executionId}?url=$1"`
    );
  }
}

export default EmailServiceFactory;