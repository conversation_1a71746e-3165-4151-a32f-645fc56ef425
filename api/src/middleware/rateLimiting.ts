import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';
import { logger, logAuditEvent } from '../utils/logger.js';

// Rate limiting configuration based on user roles and endpoint types
export const createRateLimiter = (options: {
  windowMs: number;
  maxRequests: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.maxRequests,
    message: {
      error: 'Rate limit exceeded',
      message: options.message,
      retryAfter: Math.ceil(options.windowMs / 1000),
      timestamp: new Date().toISOString(),
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    skipFailedRequests: options.skipFailedRequests || false,
    
    // Custom key generator to handle different user types
    keyGenerator: (req: Request) => {
      // Use user ID if authenticated, otherwise use IP
      if (req.user?.id) {
        return `user:${req.user.id}`;
      }
      return `ip:${req.ip}`;
    },
    
    // Enhanced logging for rate limit violations
    handler: (req: Request, res: Response) => {
      const userId = req.user?.id || null;
      const userRole = req.user?.roles?.[0] || 'anonymous';
      
      logAuditEvent(
        userId,
        'RATE_LIMIT_EXCEEDED',
        'security',
        {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.originalUrl,
          method: req.method,
          userRole,
          limit: options.maxRequests,
          window: options.windowMs,
        }
      );
      
      logger.warn(`Rate limit exceeded for ${userId ? `user ${userId}` : `IP ${req.ip}`} on ${req.method} ${req.originalUrl}`);
      
      res.status(429).json({
        error: 'Rate limit exceeded',
        message: options.message,
        retryAfter: Math.ceil(options.windowMs / 1000),
        timestamp: new Date().toISOString(),
      });
    },
    
    // Skip rate limiting for health checks and internal requests
    skip: (req: Request) => {
      const skipPaths = ['/api/health', '/api/metrics'];
      return skipPaths.some(path => req.path.startsWith(path));
    },
  });
};

// General API rate limiter - applies to all requests
export const generalRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: process.env.NODE_ENV === 'production' ? 1000 : 10000, // Much more lenient in development
  message: 'Too many requests from this IP, please try again later.',
});

// Authentication endpoints - stricter limits to prevent brute force
export const authRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: process.env.NODE_ENV === 'production' ? 5 : 50, // Very strict for auth
  message: 'Too many authentication attempts, please try again later.',
  skipSuccessfulRequests: true, // Only count failed attempts
});

// File upload rate limiter - prevent abuse of storage
export const uploadRateLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxRequests: process.env.NODE_ENV === 'production' ? 50 : 200, // Limit file uploads
  message: 'Too many file uploads, please try again later.',
});

// API endpoints for authenticated users - more lenient
export const apiRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: process.env.NODE_ENV === 'production' ? 500 : 5000, // Much higher limit for authenticated users in development
  message: 'API rate limit exceeded, please try again later.',
});

// Admin endpoints - higher limits for administrative tasks
export const adminRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: process.env.NODE_ENV === 'production' ? 1000 : 10000, // Much higher limits for admins in development
  message: 'Admin API rate limit exceeded, please try again later.',
});

// Role-based rate limiter middleware
export const roleBasedRateLimiter = (req: Request, res: Response, next: NextFunction) => {
  const userRole = req.user?.roles?.[0];
  
  // Apply different rate limits based on user role
  switch (userRole) {
    case 'admin':
      return adminRateLimiter(req, res, next);
    case 'doctor':
    case 'agent':
      return apiRateLimiter(req, res, next);
    case 'patient':
      return apiRateLimiter(req, res, next);
    default:
      // Anonymous users get the strictest limits
      return generalRateLimiter(req, res, next);
  }
};

// Burst protection - very short window with low limits to prevent rapid-fire attacks
export const burstProtection = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  maxRequests: process.env.NODE_ENV === 'production' ? 20 : 500, // Much higher burst limit for development
  message: 'Too many requests in a short period, please slow down.',
});
