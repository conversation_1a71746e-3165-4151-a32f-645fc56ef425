import { Request, Response, NextFunction } from 'express';
import { consentManagementService } from '../services/consentManagementService';

import { logger } from '../utils/structuredLogger';
/**
 * Middleware to check if user has blocking consent requirements
 * This should be used after authentication middleware
 */
export const checkBlockingConsents = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Skip consent check for consent-related endpoints
    if (req.path.startsWith('/api/consent-management') || 
        req.path.startsWith('/api/auth/logout') ||
        req.path.startsWith('/api/health')) {
      return next();
    }

    // Skip if no authenticated user
    if (!req.user?.id) {
      return next();
    }

    const hasBlocking = await consentManagementService.hasBlockingConsents(req.user.id);

    if (hasBlocking) {
      return res.status(403).json({
        success: false,
        error: 'CONSENT_REQUIRED',
        message: 'You must accept required consent documents before accessing this resource',
        redirectTo: '/consent-required',
      });
    }

    next();
  } catch (error) {
    logger.error('Error checking blocking consents:', error);
    // Don't block access on middleware errors, just log and continue
    next();
  }
};

/**
 * Trigger consent assignment on login
 */
export const triggerLoginConsents = async (
  userId: string,
  userRole: string,
  isFirstLogin: boolean = false
) => {
  try {
    await consentManagementService.assignConsents('login', {
      userId,
      userRole,
      firstLogin: isFirstLogin,
    });
  } catch (error) {
    logger.error('Error triggering login consents:', error);
    // Don't fail login on consent assignment errors
  }
};

/**
 * Trigger consent assignment on role change
 */
export const triggerRoleConsents = async (
  userId: string,
  newRole: string
) => {
  try {
    await consentManagementService.assignConsents('role_assignment', {
      userId,
      userRole: newRole,
    });
  } catch (error) {
    logger.error('Error triggering role consents:', error);
  }
};

/**
 * Trigger consent assignment on case creation
 */
export const triggerCaseConsents = async (
  caseId: string,
  userId: string,
  userRole: string,
  caseType?: string
) => {
  try {
    await consentManagementService.assignConsents('case_creation', {
      caseId,
      userId,
      userRole,
      caseType,
    });
  } catch (error) {
    logger.error('Error triggering case consents:', error);
  }
};
