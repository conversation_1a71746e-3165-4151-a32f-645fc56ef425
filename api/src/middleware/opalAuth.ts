import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { db } from '../db/index.js';
import { users } from '../db/schema/users.js';
import { eq } from 'drizzle-orm';
import { AppError, asyncHandler } from './errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { securityEventLogger } from './requestMonitoring.js';
import { PolicyEngine, type PolicyContext } from '../services/policyEngine.js';

import { logger } from '../utils/structuredLogger';
// Enhanced JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  roles: string[];
  sessionId: string;
  iat: number;
  exp: number;
}

// Enhanced user context for OPAL
export interface OPALUserContext {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  isActive: boolean;
  sessionId: string;
}

// Extend Request type for OPAL
declare global {
  namespace Express {
    interface Request {
      user?: OPALUserContext;
      policyContext?: PolicyContext;
    }
  }
}

// Token blacklist and session management (same as before)
const tokenBlacklist = new Set<string>();
const failedAttempts = new Map<string, { count: number; lastAttempt: number; lockedUntil?: number }>();
const activeSessions = new Map<string, Set<string>>();

// Utility functions (same as before)
function generateSessionId(): string {
  return crypto.randomBytes(32).toString('hex');
}

function isAccountLocked(identifier: string): boolean {
  const attempts = failedAttempts.get(identifier);
  if (!attempts) return false;
  
  const now = Date.now();
  if (attempts.lockedUntil && attempts.lockedUntil > now) {
    return true;
  }
  
  if (attempts.lockedUntil && attempts.lockedUntil <= now) {
    failedAttempts.delete(identifier);
  }
  
  return false;
}

function recordFailedAttempt(identifier: string): void {
  const now = Date.now();
  const attempts = failedAttempts.get(identifier) || { count: 0, lastAttempt: 0 };
  
  if (now - attempts.lastAttempt > 15 * 60 * 1000) {
    attempts.count = 0;
  }
  
  attempts.count++;
  attempts.lastAttempt = now;
  
  if (attempts.count >= 5) {
    attempts.lockedUntil = now + (30 * 60 * 1000);
    logger.warn(`Account locked due to failed attempts: ${identifier}`);
  }
  
  failedAttempts.set(identifier, attempts);
}

function clearFailedAttempts(identifier: string): void {
  failedAttempts.delete(identifier);
}

// OPAL-style authentication middleware
export const opalAuthMiddleware = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  let token: string | undefined;

  // Try to get token from Authorization header first (Bearer token)
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7);
  }
  // If no Bearer token, try to get from HTTP-only cookie
  else if (req.cookies && req.cookies.auth_token) {
    token = req.cookies.auth_token;
  }
  
  if (!token) {
    securityEventLogger.logFailedAuth(req, 'MISSING_TOKEN');
    throw new AppError('Access token required', 401);
  }

  if (tokenBlacklist.has(token)) {
    securityEventLogger.logFailedAuth(req, 'BLACKLISTED_TOKEN');
    throw new AppError('Token has been revoked', 401);
  }

  if (!process.env.JWT_SECRET) {
    throw new AppError('JWT_SECRET not configured', 500);
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload;
    
    if (!decoded.sessionId) {
      securityEventLogger.logFailedAuth(req, 'INVALID_TOKEN_FORMAT');
      throw new AppError('Invalid token format', 401);
    }

    // Check session validity
    const userSessions = activeSessions.get(decoded.userId);
    
    // For development/testing environment, auto-create session if not found
    if (!userSessions || !userSessions.has(decoded.sessionId)) {
      if (process.env.NODE_ENV !== 'production') {
        logger.info('Auto-creating session for user ${decoded.userId} (${decoded.sessionId}) in development mode', { requestId: 'context-needed' }, { data: undefined });
        
        // Create new session entry for this user
        if (!activeSessions.has(decoded.userId)) {
          activeSessions.set(decoded.userId, new Set());
        }
        
        // Add this session ID to the user's sessions
        activeSessions.get(decoded.userId)?.add(decoded.sessionId);
      } else {
        // In production, still enforce session validity
        securityEventLogger.logFailedAuth(req, 'SESSION_NOT_FOUND', { sessionId: decoded.sessionId });
        throw new AppError('Session not found or expired', 401);
      }
    }
    
    // Get user from database
    const [user] = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role, // Include the role field from the database
        isActive: users.isActive,
        lastLoginAt: users.lastLoginAt,
      })
      .from(users)
      .where(eq(users.id, decoded.userId))
      .limit(1);

    if (!user) {
      securityEventLogger.logFailedAuth(req, 'USER_NOT_FOUND', { userId: decoded.userId });
      throw new AppError('User not found', 401);
    }

    if (!user.isActive) {
      securityEventLogger.logFailedAuth(req, 'ACCOUNT_DEACTIVATED', { userId: decoded.userId });
      throw new AppError('Account is deactivated', 401);
    }

    // Use role from database instead of JWT token
    // Convert single role to array for compatibility with existing code
    const userRoles = user.role ? [user.role] : ['patient']; // Default to patient if no role in database

    // Set up OPAL user context
    req.user = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      roles: userRoles,
      isActive: user.isActive,
      sessionId: decoded.sessionId,
    };

    // Set up policy context for OPAL evaluation
    req.policyContext = {
      user: {
        id: user.id,
        email: user.email,
        roles: userRoles,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      action: '', // Will be set by specific middleware
      environment: {
        time: new Date(),
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        method: req.method,
      },
      request: {
        body: req.body,
        params: req.params,
        query: req.query,
      }
    };

    // Log successful authentication
    logAuditEvent(
      user.id,
      'SUCCESSFUL_AUTHENTICATION',
      'auth',
      {
        sessionId: decoded.sessionId,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
      }
    );

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      securityEventLogger.logFailedAuth(req, 'INVALID_TOKEN', { error: error.message });
      throw new AppError('Invalid token', 401);
    } else if (error instanceof jwt.TokenExpiredError) {
      securityEventLogger.logFailedAuth(req, 'EXPIRED_TOKEN');
      throw new AppError('Token expired', 401);
    }
    throw error;
  }
});

// OPAL-style resource authorization middleware
export const requireResourceAccess = (resource: string, action: string) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user || !req.policyContext) {
      securityEventLogger.logAccessViolation(req, 'NO_AUTH_CONTEXT');
      throw new AppError('Authentication required', 401);
    }

    // Update policy context with specific action
    req.policyContext.action = action;
    const resourceContext = extractResourceContext(req, resource);
    
    // Handle case where we need to fetch additional resource data
    if (resource === 'cases' && resourceContext._needsCaseData && req.params.id) {
      try {
        // Import schemas dynamically to avoid circular dependencies
        const { cases } = await import('../db/schema/cases.js');
        const { caseDoctors } = await import('../db/schema/case-doctors.js');
        const { eq, and } = await import('drizzle-orm');
        
        // Fetch case data to get ownership information
        const [caseData] = await db
          .select({
            id: cases.id,
            patientId: cases.patientId,
            status: cases.status,
          })
          .from(cases)
          .where(eq(cases.id, req.params.id))
          .limit(1);
        
        if (caseData) {
          // Add the fetched case data to resource context
          resourceContext.patientId = caseData.patientId;
          resourceContext.status = caseData.status;
          
          // Fetch assigned doctors for this case
          const assignedDoctors = await db
            .select({
              doctorId: caseDoctors.doctorId,
            })
            .from(caseDoctors)
            .where(and(
              eq(caseDoctors.caseId, req.params.id),
              eq(caseDoctors.isActive, true)
            ));
          
          // Check if current user is an assigned doctor
          if (req.user && assignedDoctors.some((doc: any) => doc.doctorId === req.user!.id)) {
            resourceContext.assignedDoctor = req.user.id;
            logger.info('[DEBUG] User ${req.user.id} is assigned doctor for case ${req.params.id}', { requestId: 'context-needed' }, { data: undefined });
          }
        }
        
        // Remove the internal flag
        delete resourceContext._needsCaseData;
      } catch (error) {
        logger.error('Failed to fetch case data for authorization:', error);
        // Continue without case data - let policy engine handle the missing context
        delete resourceContext._needsCaseData;
      }
    }
    
    // Handle document data fetching for ownership evaluation
    if (resource === 'documents' && resourceContext._needsDocumentData && (req.params.id || req.params.documentId)) {
      try {
        // Import schemas dynamically to avoid circular dependencies
        const { medicalDocuments, cases } = await import('../db/schema/cases.js');
        const { eq } = await import('drizzle-orm');
        
        const documentId = req.params.id || req.params.documentId;
        
        logger.info('[DEBUG] Fetching document data for authorization: documentId=${documentId}, method=${req.method}, user=${req.user?.id}', { requestId: 'context-needed' }, { data: undefined });
        
        // Fetch document data to get ownership information
        const [documentData] = await db
          .select({
            id: medicalDocuments.id,
            uploadedBy: medicalDocuments.uploadedBy,
            caseId: medicalDocuments.caseId,
          })
          .from(medicalDocuments)
          .where(eq(medicalDocuments.id, documentId))
          .limit(1);
        
        logger.info('[DEBUG] Document data fetched:', { requestId: 'context-needed' }, { data: documentData });
        
        if (documentData) {
          // Add the fetched document data to resource context
          resourceContext.uploadedBy = documentData.uploadedBy;
          resourceContext.caseId = documentData.caseId;
          
          // If document is attached to a case, fetch case ownership info for case-based permissions
          if (documentData.caseId) {
            logger.info('[DEBUG] Document attached to case ${documentData.caseId}, fetching case ownership and doctor assignments', { requestId: 'context-needed' }, { data: undefined });
            
            // Import case doctors schema
            const { caseDoctors } = await import('../db/schema/case-doctors.js');
            const { and } = await import('drizzle-orm');
            
            const [caseData] = await db
              .select({
                id: cases.id,
                patientId: cases.patientId,
              })
              .from(cases)
              .where(eq(cases.id, documentData.caseId))
              .limit(1);
            
            if (caseData) {
              // Add case ownership info - this allows case-based document permissions
              resourceContext.patientId = caseData.patientId;
              logger.debug(`[DEBUG] Case ownership: patientId=${caseData.patientId}`);
              
              // Fetch assigned doctors for this case
              const assignedDoctors = await db
                .select({
                  doctorId: caseDoctors.doctorId,
                })
                .from(caseDoctors)
                .where(and(
                  eq(caseDoctors.caseId, documentData.caseId),
                  eq(caseDoctors.isActive, true)
                ));
              
              // Check if current user is an assigned doctor
              if (req.user && assignedDoctors.some((doc: any) => doc.doctorId === req.user!.id)) {
                resourceContext.assignedDoctor = req.user.id;
                logger.info('[DEBUG] User ${req.user.id} is assigned doctor for case ${documentData.caseId}', { requestId: 'context-needed' }, { data: undefined });
              }
              
              logger.debug('Debug output', undefined, { data: { message: '[DEBUG] Assigned doctors for case:', doctorIds: assignedDoctors.map((d: any) => d.doctorId) } });
            }
          }
          
          logger.debug('Debug output', undefined, { data: `[DEBUG] Final resource context:`, resourceContext });
        } else {
          logger.info('[DEBUG] No document found with ID: ${documentId}', { requestId: 'context-needed' }, { data: undefined });
        }
        
        // Remove the internal flag
        delete resourceContext._needsDocumentData;
      } catch (error) {
        logger.error('Failed to fetch document data for authorization:', error);
        logger.info('[DEBUG] Error fetching document data:', { requestId: 'context-needed' }, { data: error });
        // Continue without document data - let policy engine handle the missing context
        delete resourceContext._needsDocumentData;
      }
    }
    
    req.policyContext.resource = {
      type: resource,
      id: req.params.id || req.params.documentId,
      ...resourceContext,
    };

    logger.debug('Debug output', undefined, { data: JSON.stringify({
      message: '[DEBUG] Policy evaluation context',
      user: req.policyContext.user,
      resource: req.policyContext.resource,
      action: req.policyContext.action,
      method: req.method,
      url: req.originalUrl
    }) });

    // Evaluate policy
    const policyEngine = PolicyEngine.getInstance();
    const policyResult = await policyEngine.evaluate(req.policyContext);
    
    logger.debug('Debug output', undefined, { data: `[DEBUG] Policy evaluation result:`, policyResult });

    if (!policyResult.allow) {
      securityEventLogger.logAccessViolation(req, 'POLICY_DENIED', {
        resource,
        action,
        reason: policyResult.reason,
        matchedPolicies: policyResult.matchedPolicies,
      });
      
      throw new AppError(`Access denied: ${policyResult.reason}`, 403);
    }

    // Log successful authorization
    logger.info(`Access granted for ${req.user.id}: ${resource}:${action}`, {
      userId: req.user.id,
      resource,
      action,
      reason: policyResult.reason,
      matchedPolicies: policyResult.matchedPolicies,
    });

    next();
  });
};

// Extract resource context from request
function extractResourceContext(req: Request, resource: string): Record<string, any> {
  const context: Record<string, any> = {};

  // Extract common resource fields based on resource type
  switch (resource) {
    case 'cases':
      // For cases, we might need to fetch the case to get patientId, assignedDoctor, etc.
      if (req.params.id) {
        context.id = req.params.id;
        
        // For PUT/PATCH requests (updates), we need to fetch the case to get ownership info
        // This is critical for ownership-based permissions to work
        if ((req.method === 'PUT' || req.method === 'PATCH') && req.user) {
          // Note: This will be handled asynchronously in the policy evaluation
          // We set a flag to indicate that case data needs to be fetched
          context._needsCaseData = true;
        }
      }
      if (req.body) {
        context.patientId = req.body.patientId;
        context.assignedDoctor = req.body.assignedDoctor;
        context.status = req.body.status;
      }
      
      // For case creation (POST with no ID), set patientId to current user for patients
      if (req.method === 'POST' && !req.params.id && req.user) {
        if (req.user.roles.includes('patient')) {
          context.patientId = req.user.id;
        }
      }
      
      // For list endpoints (GET with no ID)
      if (req.method === 'GET' && !req.params.id && req.user) {
        // Set patientId to user's ID if user is a patient
        // This allows patients to access their own cases in list views
        if (req.user.roles.includes('patient')) {
          context.patientId = req.user.id;
        }
        
        // Set assignedDoctor to user's ID if user is a doctor
        // This allows doctors to access cases assigned to them in list views
        if (req.user.roles.includes('doctor')) {
          context.assignedDoctor = req.user.id;
        }
      }
      break;

    case 'documents':
      if (req.params.id || req.params.documentId) {
        context.id = req.params.id || req.params.documentId;
        
        // For DELETE/PUT/PATCH/GET requests (updates/deletes/reads), we need to fetch the document to get ownership info
        // This is critical for ownership-based permissions to work
        if ((req.method === 'DELETE' || req.method === 'PUT' || req.method === 'PATCH' || req.method === 'GET') && req.user) {
          // Note: This will be handled asynchronously in the policy evaluation
          // We set a flag to indicate that document data needs to be fetched
          context._needsDocumentData = true;
        }
      }
      if (req.body) {
        context.patientId = req.body.patientId;
        context.caseId = req.body.caseId;
      }
      
      // For document list requests (GET /documents), set patientId to current user for patients
      // This allows patients to access their own documents
      if (req.method === 'GET' && !req.params.id && !req.params.documentId && req.user) {
        if (req.user.roles.includes('patient')) {
          context.patientId = req.user.id;
        }
        // For caseId filtering, we'll need to validate case ownership in the route handler
        if (req.query.caseId && typeof req.query.caseId === 'string') {
          context.caseId = req.query.caseId;
        }
      }
      
      // For document upload requests (POST /upload), set patientId to current user for patients
      // This allows patients to upload their own documents
      if (req.method === 'POST' && req.user) {
        if (req.user.roles.includes('patient')) {
          context.patientId = req.user.id;
        }
        // For multipart form data, caseId might be in the form fields
        if (req.body && req.body.caseId) {
          context.caseId = req.body.caseId;
        }
      }
      break;

    case 'appointments':
      if (req.params.id) {
        context.id = req.params.id;
      }
      if (req.body) {
        context.patientId = req.body.patientId;
        context.assignedDoctor = req.body.assignedDoctor;
      }
      break;

    default:
      // Generic resource context
      if (req.params.id) {
        context.id = req.params.id;
      }
      break;
  }

  return context;
}

// Convenience middleware for common operations
export const requireCaseRead = requireResourceAccess('cases', 'read');
export const requireCaseWrite = requireResourceAccess('cases', 'write');
export const requireCaseDelete = requireResourceAccess('cases', 'delete');

export const requireDocumentRead = requireResourceAccess('documents', 'read');
export const requireDocumentWrite = requireResourceAccess('documents', 'write');
export const requireDocumentDelete = requireResourceAccess('documents', 'delete');

export const requireAppointmentRead = requireResourceAccess('appointments', 'read');
export const requireAppointmentWrite = requireResourceAccess('appointments', 'write');
export const requireAppointmentDelete = requireResourceAccess('appointments', 'delete');

// Legacy role-based middleware (for backward compatibility)
export const requireRole = (allowedRoles: string[]) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new AppError('Authentication required', 401);
    }

    const hasRequiredRole = allowedRoles.some(role => req.user!.roles.includes(role));
    
    if (!hasRequiredRole) {
      securityEventLogger.logAccessViolation(req, 'INSUFFICIENT_ROLE', {
        userRoles: req.user.roles,
        requiredRoles: allowedRoles,
      });
      throw new AppError('Insufficient permissions', 403);
    }

    next();
  });
};

// Login attempt validation middleware
export const loginAttemptValidator = (req: Request, res: Response, next: NextFunction) => {
  const identifier = req.body.email || req.ip;
  
  if (isAccountLocked(identifier)) {
    const attempts = failedAttempts.get(identifier);
    const lockTimeRemaining = attempts?.lockedUntil ? Math.ceil((attempts.lockedUntil - Date.now()) / 1000 / 60) : 0;
    
    securityEventLogger.logFailedAuth(req, 'ACCOUNT_LOCKED', {
      identifier,
      lockTimeRemaining,
    });
    
    return res.status(429).json({
      error: 'Account Locked',
      message: `Too many failed login attempts. Try again in ${lockTimeRemaining} minutes.`,
      lockTimeRemaining,
      timestamp: new Date().toISOString(),
    });
  }
  
  next();
};

// Session management functions
export const sessionManager = {
  createSession: (userId: string): string => {
    const sessionId = generateSessionId();
    
    if (!activeSessions.has(userId)) {
      activeSessions.set(userId, new Set());
    }
    
    const userSessions = activeSessions.get(userId)!;
    
    if (userSessions.size >= 5) {
      const oldestSession = userSessions.values().next().value;
      userSessions.delete(oldestSession);
      logger.info(`Removed oldest session for user ${userId} due to session limit`);
    }
    
    userSessions.add(sessionId);
    return sessionId;
  },

  destroySession: (userId: string, sessionId: string): void => {
    const userSessions = activeSessions.get(userId);
    if (userSessions) {
      userSessions.delete(sessionId);
      if (userSessions.size === 0) {
        activeSessions.delete(userId);
      }
    }
  },

  destroyAllUserSessions: (userId: string): void => {
    activeSessions.delete(userId);
  },

  blacklistToken: (token: string): void => {
    tokenBlacklist.add(token);
  },
};

// Export utility functions
export const authUtils = {
  recordFailedAttempt,
  clearFailedAttempts,
  isAccountLocked,
};
