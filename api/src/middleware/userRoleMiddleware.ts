import { Request, Response, NextFunction } from 'express';
import { extractUserId, extractUserRoles, extractPrimaryRole, logUserRoleInfo } from '../utils/userRoleExtractor.js';

import { logger } from '../utils/structuredLogger';
/**
 * Middleware to extract and normalize user roles from request
 * This ensures consistent user role extraction across all routes
 */
export const userRoleMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Extract user ID and roles using our utility functions
    const userId = extractUserId(req);
    const userRoles = extractUserRoles(req);
    const primaryRole = extractPrimaryRole(req);
    
    // Attach normalized user data to request object for consistent access in route handlers
    (req as any).normalizedUser = {
      id: userId,
      roles: userRoles,
      primaryRole: primaryRole
    };
    
    // Log user role information in development environment
    if (process.env.NODE_ENV !== 'production') {
      logUserRoleInfo(req, 'User role middleware');
    }
    
    next();
  } catch (error) {
    logger.error('Error in userRoleMiddleware:', error);
    next(error);
  }
};

/**
 * Middleware to require specific roles for access
 * @param allowedRoles Array of roles that are allowed to access the route
 */
export const requireRoles = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const normalizedUser = (req as any).normalizedUser;
    
    // If normalizedUser is not available, userRoleMiddleware wasn't called
    if (!normalizedUser) {
      return res.status(500).json({ 
        error: 'Server configuration error: userRoleMiddleware must be used before requireRoles' 
      });
    }
    
    // Check if user has any of the allowed roles
    const hasAllowedRole = normalizedUser.roles.some(role => allowedRoles.includes(role));
    
    if (hasAllowedRole) {
      next();
    } else {
      res.status(403).json({ 
        error: 'Forbidden', 
        message: `Access denied. Required roles: ${allowedRoles.join(', ')}` 
      });
    }
  };
};

/**
 * Middleware to require admin role
 */
export const requireAdmin = requireRoles(['admin']);

/**
 * Middleware to require doctor role
 */
export const requireDoctor = requireRoles(['doctor']);

/**
 * Middleware to require patient role
 */
export const requirePatient = requireRoles(['patient']);

/**
 * Middleware to require agent role
 */
export const requireAgent = requireRoles(['agent']);

/**
 * Middleware to require admin or agent roles
 */
export const requireAdminOrAgent = requireRoles(['admin', 'agent']);

/**
 * Middleware to require any authenticated user
 */
export const requireAuthenticated = (req: Request, res: Response, next: NextFunction): void => {
  const normalizedUser = (req as any).normalizedUser;
  
  if (!normalizedUser || !normalizedUser.id) {
    return res.status(401).json({ error: 'Unauthorized', message: 'Authentication required' });
  }
  
  next();
};
