import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler.js';
import { CaseDoctorService } from '../services/caseDoctorService.js';
import { logAuditEvent } from '../utils/logger.js';

/**
 * Middleware to ensure doctors have accepted their case assignment before making changes
 * This enforces the business rule that doctors can only modify cases after explicit acceptance
 */
export const requireDoctorAcceptance = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const opalUser = (req as any).user;
    const userId = opalUser?.id;
    const userRoles = opalUser?.roles || [];
    
    // Only apply this check to doctors
    if (!userRoles.includes('doctor')) {
      return next(); // Non-doctors can proceed (admins, patients, etc.)
    }
    
    // Extract case ID from various possible locations in the request
    const caseId = req.params.id || req.params.caseId || req.body.caseId;
    
    // Skip acceptance check for non-case operations (like credential documents)
    if (!caseId) {
      // Check if this is a case-related operation by examining the path
      const isCaseRelated = req.path.includes('/cases/') ||
                           req.path.includes('/case-notes/') ||
                           req.path.includes('/discussions/') ||
                           (req.body && req.body.caseId);
      
      if (!isCaseRelated) {
        return next(); // Allow non-case operations (like credential documents)
      }
      
      throw new AppError('Case ID is required for case-related operations', 400);
    }
    
    if (!userId) {
      throw new AppError('User ID is required', 401);
    }
    
    // Check if doctor is assigned to the case
    const isAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
    
    if (!isAssigned) {
      // Log unauthorized access attempt
      logAuditEvent(
        userId,
        'DOCTOR_UNAUTHORIZED_CASE_ACCESS_ATTEMPT',
        'cases',
        {
          caseId,
          reason: 'doctor_not_assigned',
          action: req.method + ' ' + req.path,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );
      
      throw new AppError('You are not assigned to this case', 403);
    }
    
    // Check if doctor has accepted the case assignment
    const hasAccepted = await CaseDoctorService.hasDoctorAcceptedCase(userId, caseId);
    
    if (!hasAccepted) {
      // Log attempt to modify case without acceptance
      logAuditEvent(
        userId,
        'DOCTOR_CASE_MODIFICATION_WITHOUT_ACCEPTANCE',
        'cases',
        {
          caseId,
          reason: 'case_not_accepted',
          action: req.method + ' ' + req.path,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );
      
      throw new AppError(
        'You must accept this case assignment before making any changes. Please accept the case first.',
        403
      );
    }
    
    // Log successful acceptance check for audit trail
    logAuditEvent(
      userId,
      'DOCTOR_ACCEPTED_CASE_ACCESS',
      'cases',
      {
        caseId,
        action: req.method + ' ' + req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );
    
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Helper function to check if a route should require doctor acceptance
 * This can be used for conditional middleware application
 */
export const shouldRequireDoctorAcceptance = (req: Request): boolean => {
  const opalUser = (req as any).user;
  const userRoles = opalUser?.roles || [];
  
  // Only doctors need acceptance check
  if (!userRoles.includes('doctor')) {
    return false;
  }
  
  // Check if this is a modification operation (not just reading)
  const isModificationOperation = ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method);
  
  // Check if this is a case-related endpoint
  const isCaseRelated = req.path.includes('/cases/') || req.path.includes('/case-notes/');
  
  return isModificationOperation && isCaseRelated;
};