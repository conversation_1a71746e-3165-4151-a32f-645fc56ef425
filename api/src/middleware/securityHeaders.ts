import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import { logger } from '../utils/logger.js';

// Enhanced Content Security Policy for healthcare application
// Configured for react-doc-viewer dependencies: pdfjs-dist, react-pdf, styled-components, wl-msg-reader
const cspDirectives = {
  defaultSrc: ["'self'"],
  styleSrc: [
    "'self'",
    "'unsafe-inline'", // Required for styled-components and react-doc-viewer inline styles
    "https://fonts.googleapis.com"
  ],
  scriptSrc: [
    "'self'",
    // Allow blob URLs for PDF.js workers and document processing
    "blob:",
    // Allow eval for development and styled-components (remove unsafe-eval in production)
    ...(process.env.NODE_ENV === 'development' ? ["'unsafe-eval'"] : [])
  ],
  workerSrc: [
    "'self'", // Local PDF.js worker files hosted in /js/pdfjs/
    "blob:" // Critical for PDF.js workers (pdfjs-dist dependency)
  ],
  imgSrc: [
    "'self'",
    "data:", // Data URLs for embedded images in documents
    "https:", // Allow all HTTPS images for document previews and thumbnails
    "blob:" // For file previews, document thumbnails, and PDF rendering
  ],
  fontSrc: [
    "'self'",
    "https://fonts.gstatic.com",
    "data:" // Data URLs for embedded fonts in documents
  ],
  connectSrc: [
    "'self'",
    process.env.FRONTEND_URL || "http://localhost:5173",
    "https://care.continuia.ai",
    "https://desk.continuia.ai",
    "https://continuia.health",
    "https://www.continuia.ai",
    "https://continuia.ai",
    "https://www.continuia.ai",
    "https://dev--continuia-web.netlify.app",
    // WebSocket connections for collaborative editing
    "ws://api.continuia.ai",
    "wss://api.continuia.ai",
    "ws://api.continuia.health",
    "wss://api.continuia.health",
    "ws://api:3001", // Internal Docker service
    "wss://api:3001" // Internal Docker service
  ],
  mediaSrc: ["'self'", "blob:", "data:"], // For media content in documents
  objectSrc: ["'none'"], // Prevent object/embed/applet for security
  frameSrc: [
    "'self'",
    "data:", // Allow data URLs for document embedding (PDF.js uses this)
    "https://view.officeapps.live.com", // Microsoft Office Online viewer for DOC/DOCX/PPT/PPTX
    "https://docs.google.com" // Google Docs viewer (alternative fallback)
  ],
  frameAncestors: ["'none'"], // Prevent being framed (clickjacking protection)
  baseUri: ["'self'"],
  formAction: ["'self'"],
  // Allow manifest for PWA features
  manifestSrc: ["'self'"],
  // Child source for workers (fallback for older browsers)
  childSrc: ["'self'", "blob:"],
  ...(process.env.NODE_ENV === 'production' && { upgradeInsecureRequests: [] }),
};

// Enhanced helmet configuration for healthcare security
export const enhancedSecurityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: cspDirectives,
    reportOnly: false, // Always enforce CSP to ensure frame-src works properly
  },
  
  // HTTP Strict Transport Security (HSTS)
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  
  // Prevent MIME type sniffing
  noSniff: true,
  
  // X-Frame-Options - disabled to allow CSP frame-src to control framing
  frameguard: false, // Let CSP frameSrc directive handle frame control
  
  // X-XSS-Protection
  xssFilter: true,
  
  // Referrer Policy
  referrerPolicy: {
    policy: ['no-referrer-when-downgrade', 'strict-origin-when-cross-origin'],
  },
  
  // Hide X-Powered-By header
  hidePoweredBy: true,
});

// Custom security headers middleware for additional protection
export const customSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // HIPAA-specific security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Download-Options', 'noopen');
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  
  // Cache control for sensitive data
  if (req.path.includes('/api/')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  
  // Custom headers for API identification (without revealing too much)
  res.setHeader('X-API-Version', '1.0');
  res.setHeader('X-Request-ID', generateRequestId());
  
  // Security headers for healthcare compliance
  res.setHeader('X-Healthcare-API', 'true');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  
  next();
};

// Generate unique request ID for tracking
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// CORS configuration with enhanced security
export const enhancedCorsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost:5173',
      'http://localhost:3000', // React dev server
      'http://localhost:5173', // Vite dev server
      // Production domains
      'https://care.continuia.ai',
      'https://desk.continuia.ai',
      'https://continuia.health',
      'https://www.continuia.ai',
      'https://continuia.ai',
      'https://www.continuia.ai',
      'https://dev--continuia-web.netlify.app'
    ];
    
    // In development, be more permissive
    if (process.env.NODE_ENV === 'development') {
      if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
        return callback(null, true);
      }
    }
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logger.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error('Not allowed by CORS'), false);
    }
  },
  credentials: true,
  optionsSuccessStatus: 200, // For legacy browser support
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Request-ID',
  ],
  exposedHeaders: [
    'X-Request-ID',
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
  ],
  maxAge: 86400, // 24 hours for preflight cache
};

// Request sanitization middleware
export const requestSanitizer = (req: Request, res: Response, next: NextFunction) => {
  // Remove potentially dangerous headers
  delete req.headers['x-forwarded-host'];
  delete req.headers['x-forwarded-server'];
  
  // Sanitize query parameters
  if (req.query) {
    for (const key in req.query) {
      if (typeof req.query[key] === 'string') {
        // Basic XSS prevention
        req.query[key] = (req.query[key] as string)
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '');
      }
    }
  }
  
  next();
};

// DDoS protection middleware
export const ddosProtection = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent') || '';
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
  ];
  
  // Allow legitimate bots but log suspicious activity
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
  
  if (isSuspicious && process.env.NODE_ENV === 'production') {
    logger.warn(`Suspicious user agent detected: ${userAgent} from IP: ${req.ip}`);
    
    // Don't block immediately, but apply stricter rate limiting
    req.headers['x-suspicious-request'] = 'true';
  }
  
  // Check for common attack patterns in URL
  const suspiciousUrlPatterns = [
    /\.\./,  // Directory traversal
    /\/etc\/passwd/,
    /\/proc\/self\/environ/,
    /<script/i,
    /javascript:/i,
    /vbscript:/i,
  ];
  
  const hasSuspiciousUrl = suspiciousUrlPatterns.some(pattern => 
    pattern.test(req.originalUrl)
  );
  
  if (hasSuspiciousUrl) {
    logger.error(`Suspicious URL pattern detected: ${req.originalUrl} from IP: ${req.ip}`);
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Invalid request format',
      timestamp: new Date().toISOString(),
    });
  }
  
  next();
};
