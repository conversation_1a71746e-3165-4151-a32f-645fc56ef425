import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logger, StructuredLogger } from '../utils/structuredLogger';

// Extend Express Request to include logging context
declare global {
  namespace Express {
    interface Request {
      id: string;
      startTime: number;
      logger: ReturnType<StructuredLogger['child']>;
    }
  }
}

/**
 * Request logging middleware
 * Adds request ID, timing, and structured logging context to each request
 */
export const requestLoggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Generate unique request ID
  req.id = req.headers['x-request-id'] as string || uuidv4();
  req.startTime = Date.now();

  // Extract base context from request
  const baseContext = StructuredLogger.extractRequestContext(req);
  
  // Create child logger with request context
  req.logger = logger.child(baseContext);

  // Log incoming request (INFO level for production traceability)
  req.logger.info('Incoming request', {
    body: req.method === 'GET' ? undefined : sanitizeRequestBody(req.body),
    query: req.query,
    params: req.params
  });

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - req.startTime;
    
    // Log response with performance metrics
    req.logger.info('Request completed', {
      statusCode: res.statusCode,
      duration
    });

    // Log performance warning if request is slow
    if (duration > 5000) {
      req.logger.warn('Slow request detected', {
        statusCode: res.statusCode,
        duration,
        threshold: 5000
      });
    }

    // Log performance metrics
    req.logger.performance(`${req.method} ${req.path}`, duration, {
      statusCode: res.statusCode
    });

    return originalJson.call(this, body);
  };

  // Handle errors in response
  res.on('finish', () => {
    if (res.statusCode >= 400) {
      const duration = Date.now() - req.startTime;
      const level = res.statusCode >= 500 ? 'error' : 'warn';
      
      req.logger[level](`Request failed with status ${res.statusCode}`, undefined, {
        statusCode: res.statusCode,
        duration
      });
    }
  });

  next();
};

/**
 * Sanitize request body for logging
 * Remove sensitive information like passwords, tokens, etc.
 */
function sanitizeRequestBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = [
    'password',
    'passwordHash',
    'token',
    'accessToken',
    'refreshToken',
    'secret',
    'apiKey',
    'creditCard',
    'ssn',
    'socialSecurityNumber'
  ];

  const sanitized = { ...body };
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

/**
 * Error logging middleware
 * Logs all unhandled errors with full context
 */
export const errorLoggingMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const duration = Date.now() - req.startTime;
  
  // Log error with full context
  req.logger.error('Unhandled request error', error, {
    statusCode: res.statusCode || 500,
    duration,
    stack: error.stack
  });

  next(error);
};

/**
 * Security event logging helper
 */
export const logSecurityEvent = (
  req: Request,
  eventType: string,
  message: string,
  meta?: any
) => {
  req.logger.security(eventType as any, message, undefined, {
    ...meta,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  });
};

/**
 * Audit event logging helper
 */
export const logAuditEvent = (
  req: Request,
  action: string,
  resource: string,
  meta?: any
) => {
  req.logger.audit(action, resource, undefined, {
    ...meta,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  });
};
