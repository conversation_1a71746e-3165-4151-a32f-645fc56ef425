import { Request, Response, NextFunction } from 'express';
import { logger, logAuditEvent } from '../utils/logger.js';

// Request monitoring and security logging middleware
export class RequestMonitor {
  private static instance: RequestMonitor;
  private requestCounts: Map<string, number> = new Map();
  private suspiciousIPs: Set<string> = new Set();
  private lastCleanup: number = Date.now();

  static getInstance(): RequestMonitor {
    if (!RequestMonitor.instance) {
      RequestMonitor.instance = new RequestMonitor();
    }
    return RequestMonitor.instance;
  }

  // Clean up old data periodically
  private cleanup() {
    const now = Date.now();
    if (now - this.lastCleanup > 3600000) { // 1 hour
      this.requestCounts.clear();
      this.suspiciousIPs.clear();
      this.lastCleanup = now;
    }
  }

  // Track request patterns for anomaly detection
  trackRequest(req: Request) {
    this.cleanup();
    
    const key = `${req.ip}:${req.method}:${req.path}`;
    const count = this.requestCounts.get(key) || 0;
    this.requestCounts.set(key, count + 1);

    // Flag suspicious patterns
    if (count > 100) { // More than 100 identical requests
      this.suspiciousIPs.add(req.ip);
      logger.warn(`Suspicious request pattern detected from IP ${req.ip}: ${count} identical requests`);
    }
  }

  isSuspiciousIP(ip: string): boolean {
    return this.suspiciousIPs.has(ip);
  }
}

// Enhanced request logging middleware
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const requestId = `req_${startTime}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Add request ID to request object
  (req as any).requestId = requestId;
  res.setHeader('X-Request-ID', requestId);

  // Track request in monitor
  const monitor = RequestMonitor.getInstance();
  monitor.trackRequest(req);

  // Log request start (excluding sensitive routes)
  const sensitiveRoutes = ['/api/auth/login', '/api/auth/register'];
  const isSensitive = sensitiveRoutes.some(route => req.path.startsWith(route));

  if (!isSensitive) {
    logger.info(`[${requestId}] ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      userRole: req.user?.role,
      contentType: req.get('Content-Type'),
      contentLength: req.get('Content-Length'),
    });
  }

  // Override res.json to log responses
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - startTime;
    
    // Log response (excluding sensitive data)
    if (!isSensitive) {
      logger.info(`[${requestId}] Response ${res.statusCode} (${duration}ms)`, {
        statusCode: res.statusCode,
        duration,
        responseSize: JSON.stringify(body).length,
      });
    }

    // Log slow requests
    if (duration > 5000) { // > 5 seconds
      logger.warn(`[${requestId}] Slow request detected: ${duration}ms for ${req.method} ${req.path}`);
    }

    // Log errors
    if (res.statusCode >= 400) {
      const userId = req.user?.id || null;
      logAuditEvent(
        userId,
        'API_ERROR',
        'api',
        {
          requestId,
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          duration,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          error: res.statusCode >= 500 ? body.message : undefined,
        }
      );
    }

    return originalJson.call(this, body);
  };

  next();
};

// Security event logger
export const securityEventLogger = {
  logFailedAuth: (req: Request, reason: string, additionalData?: any) => {
    logAuditEvent(
      null,
      'FAILED_AUTHENTICATION',
      'security',
      {
        reason,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        timestamp: new Date().toISOString(),
        ...additionalData,
      }
    );
  },

  logSuspiciousActivity: (req: Request, activity: string, details?: any) => {
    const userId = req.user?.id || null;
    logAuditEvent(
      userId,
      'SUSPICIOUS_ACTIVITY',
      'security',
      {
        activity,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        timestamp: new Date().toISOString(),
        ...details,
      }
    );
  },

  logAccessViolation: (req: Request, violation: string, details?: any) => {
    const userId = req.user?.id || null;
    logAuditEvent(
      userId,
      'ACCESS_VIOLATION',
      'security',
      {
        violation,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        userRole: req.user?.role,
        timestamp: new Date().toISOString(),
        ...details,
      }
    );
  },

  logDataAccess: (req: Request, resourceType: string, resourceId: string, action: string) => {
    const userId = req.user?.id || null;
    logAuditEvent(
      userId,
      'DATA_ACCESS',
      'audit',
      {
        resourceType,
        resourceId,
        action,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        userRole: req.user?.role,
        timestamp: new Date().toISOString(),
      }
    );
  },
};

// Anomaly detection middleware
export const anomalyDetection = (req: Request, res: Response, next: NextFunction) => {
  const monitor = RequestMonitor.getInstance();
  
  // Check if IP is flagged as suspicious
  if (monitor.isSuspiciousIP(req.ip)) {
    securityEventLogger.logSuspiciousActivity(req, 'REQUEST_FROM_FLAGGED_IP', {
      flaggedIP: req.ip,
    });
    
    // Apply additional security measures for suspicious IPs
    res.setHeader('X-Security-Level', 'HIGH');
  }

  // Detect unusual request patterns
  const userAgent = req.get('User-Agent') || '';
  const isHeadlessChrome = userAgent.includes('HeadlessChrome');
  const isAutomatedTool = /curl|wget|python|java|go-http|axios/i.test(userAgent);
  
  if (isHeadlessChrome || (isAutomatedTool && process.env.NODE_ENV === 'production')) {
    securityEventLogger.logSuspiciousActivity(req, 'AUTOMATED_TOOL_DETECTED', {
      userAgent,
      isHeadlessChrome,
      isAutomatedTool,
    });
  }

  // Detect potential SQL injection attempts
  const sqlInjectionPatterns = [
    /(\%27)|(\')|(\-\-)|(\%23)|(#)/i,
    /((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))/i,
    /\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/i,
    /((\%27)|(\'))union/i,
  ];

  const queryString = req.originalUrl;
  const hasSQLInjection = sqlInjectionPatterns.some(pattern => pattern.test(queryString));
  
  if (hasSQLInjection) {
    securityEventLogger.logSuspiciousActivity(req, 'SQL_INJECTION_ATTEMPT', {
      queryString,
      patterns: sqlInjectionPatterns.map(p => p.toString()),
    });
    
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Invalid request format',
      timestamp: new Date().toISOString(),
    });
  }

  next();
};

// Performance monitoring middleware
export const performanceMonitor = (req: Request, res: Response, next: NextFunction) => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

    // Log performance metrics for slow requests or high memory usage
    if (duration > 1000 || memoryDelta > 10 * 1024 * 1024) { // > 1s or > 10MB
      logger.warn('Performance concern detected', {
        method: req.method,
        path: req.path,
        duration: `${duration.toFixed(2)}ms`,
        memoryDelta: `${(memoryDelta / 1024 / 1024).toFixed(2)}MB`,
        statusCode: res.statusCode,
        userId: req.user?.id,
      });
    }
  });

  next();
};
