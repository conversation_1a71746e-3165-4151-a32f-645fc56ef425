import { PolicyEngine, type PolicyContext, type PolicyResult } from '../services/policyEngine.js';
import { logger } from './logger.js';

// Utility functions for easy policy checking throughout the application

// Check if user can perform action on resource
export async function canUserAccess(
  userId: string,
  userRoles: string[],
  resource: string,
  action: string,
  resourceData?: Record<string, any>,
  requestContext?: Record<string, any>
): Promise<boolean> {
  const policyEngine = PolicyEngine.getInstance();
  
  const context: PolicyContext = {
    user: {
      id: userId,
      email: '', // Not needed for most policy decisions
      roles: userRoles,
      firstName: '',
      lastName: '',
    },
    resource: resourceData ? {
      type: resource,
      ...resourceData,
    } : { type: resource },
    action,
    environment: {
      time: new Date(),
      ...requestContext,
    },
  };

  const result = await policyEngine.evaluate(context);
  return result.allow;
}

// Check multiple permissions at once
export async function checkMultiplePermissions(
  userId: string,
  userRoles: string[],
  checks: Array<{
    resource: string;
    action: string;
    resourceData?: Record<string, any>;
  }>
): Promise<Record<string, boolean>> {
  const results: Record<string, boolean> = {};
  
  for (const check of checks) {
    const key = `${check.resource}:${check.action}`;
    results[key] = await canUserAccess(
      userId,
      userRoles,
      check.resource,
      check.action,
      check.resourceData
    );
  }
  
  return results;
}

// Generate SQL filters based on user permissions
export async function generatePermissionFilters(
  userId: string,
  userRoles: string[],
  resource: string,
  action: string = 'read'
): Promise<Record<string, any>> {
  const filters: Record<string, any> = {};
  
  // Basic role-based filtering
  if (userRoles.includes('admin')) {
    // Admins see everything
    return {};
  }
  
  if (userRoles.includes('patient')) {
    // Patients only see their own data
    filters.patientId = userId;
  }
  
  if (userRoles.includes('doctor')) {
    // Doctors see assigned cases and related data
    if (resource === 'cases') {
      filters.assignedDoctor = userId;
    } else if (resource === 'appointments') {
      filters.assignedDoctor = userId;
    }
    // For documents, we'd need to join with cases to check assignment
  }
  
  return filters;
}

// UI permission helper - returns permissions for frontend components
export async function getUIPermissions(
  userId: string,
  userRoles: string[]
): Promise<{
  cases: {
    canRead: boolean;
    canWrite: boolean;
    canDelete: boolean;
    canCreate: boolean;
  };
  documents: {
    canRead: boolean;
    canWrite: boolean;
    canDelete: boolean;
    canUpload: boolean;
  };
  appointments: {
    canRead: boolean;
    canWrite: boolean;
    canDelete: boolean;
    canSchedule: boolean;
  };
  users: {
    canRead: boolean;
    canWrite: boolean;
    canDelete: boolean;
    canManage: boolean;
  };
  admin: {
    canAccessAdminPanel: boolean;
    canViewAuditLogs: boolean;
    canManageSystem: boolean;
  };
}> {
  const permissions = await checkMultiplePermissions(userId, userRoles, [
    // Cases
    { resource: 'cases', action: 'read' },
    { resource: 'cases', action: 'write' },
    { resource: 'cases', action: 'delete' },
    { resource: 'cases', action: 'create' },
    
    // Documents
    { resource: 'documents', action: 'read' },
    { resource: 'documents', action: 'write' },
    { resource: 'documents', action: 'delete' },
    { resource: 'documents', action: 'upload' },
    
    // Appointments
    { resource: 'appointments', action: 'read' },
    { resource: 'appointments', action: 'write' },
    { resource: 'appointments', action: 'delete' },
    { resource: 'appointments', action: 'schedule' },
    
    // Users
    { resource: 'users', action: 'read' },
    { resource: 'users', action: 'write' },
    { resource: 'users', action: 'delete' },
    { resource: 'users', action: 'manage' },
    
    // Admin
    { resource: 'admin', action: 'access' },
    { resource: 'admin', action: 'audit' },
    { resource: 'admin', action: 'manage' },
  ]);

  return {
    cases: {
      canRead: permissions['cases:read'] || false,
      canWrite: permissions['cases:write'] || false,
      canDelete: permissions['cases:delete'] || false,
      canCreate: permissions['cases:create'] || false,
    },
    documents: {
      canRead: permissions['documents:read'] || false,
      canWrite: permissions['documents:write'] || false,
      canDelete: permissions['documents:delete'] || false,
      canUpload: permissions['documents:upload'] || false,
    },
    appointments: {
      canRead: permissions['appointments:read'] || false,
      canWrite: permissions['appointments:write'] || false,
      canDelete: permissions['appointments:delete'] || false,
      canSchedule: permissions['appointments:schedule'] || false,
    },
    users: {
      canRead: permissions['users:read'] || false,
      canWrite: permissions['users:write'] || false,
      canDelete: permissions['users:delete'] || false,
      canManage: permissions['users:manage'] || false,
    },
    admin: {
      canAccessAdminPanel: permissions['admin:access'] || false,
      canViewAuditLogs: permissions['admin:audit'] || false,
      canManageSystem: permissions['admin:manage'] || false,
    },
  };
}

// Resource ownership checking utilities
export async function checkResourceOwnership(
  userId: string,
  resourceType: string,
  resourceId: string,
  ownershipField: string = 'patientId'
): Promise<boolean> {
  // This would typically query the database to check ownership
  // For now, we'll implement a basic check
  
  try {
    // Import the appropriate database schema and query
    // This is a placeholder - in real implementation, you'd query the actual resource
    
    switch (resourceType) {
      case 'cases':
        // Query cases table to check if user owns or is assigned to the case
        // const case = await db.select().from(cases).where(eq(cases.id, resourceId)).limit(1);
        // return case[0]?.patientId === userId || case[0]?.assignedDoctor === userId;
        return true; // Placeholder
        
      case 'documents':
        // Query documents table to check ownership
        return true; // Placeholder
        
      case 'appointments':
        // Query appointments table to check ownership
        return true; // Placeholder
        
      default:
        return false;
    }
  } catch (error) {
    logger.error(`Error checking resource ownership: ${error}`);
    return false;
  }
}

// Policy evaluation with detailed logging
export async function evaluatePolicy(
  context: PolicyContext,
  logDetails: boolean = false
): Promise<PolicyResult> {
  const policyEngine = PolicyEngine.getInstance();
  const result = await policyEngine.evaluate(context);
  
  if (logDetails) {
    logger.info('Policy evaluation result', {
      userId: context.user.id,
      resource: context.resource?.type,
      action: context.action,
      allowed: result.allow,
      reason: result.reason,
      matchedPolicies: result.matchedPolicies,
      evaluationTime: result.metadata?.evaluationTime,
    });
  }
  
  return result;
}

// Batch permission checking for lists of resources
export async function checkBatchPermissions(
  userId: string,
  userRoles: string[],
  resources: Array<{
    id: string;
    type: string;
    data: Record<string, any>;
  }>,
  action: string
): Promise<Record<string, boolean>> {
  const results: Record<string, boolean> = {};
  
  for (const resource of resources) {
    results[resource.id] = await canUserAccess(
      userId,
      userRoles,
      resource.type,
      action,
      { id: resource.id, ...resource.data }
    );
  }
  
  return results;
}

// Helper to create policy context from Express request
export function createPolicyContextFromRequest(
  req: any, // Express Request
  resource: string,
  action: string,
  resourceData?: Record<string, any>
): PolicyContext {
  return {
    user: {
      id: req.user?.id || '',
      email: req.user?.email || '',
      roles: req.user?.roles || [],
      firstName: req.user?.firstName || '',
      lastName: req.user?.lastName || '',
    },
    resource: {
      type: resource,
      id: req.params?.id,
      ...resourceData,
    },
    action,
    environment: {
      time: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl,
      method: req.method,
    },
    request: {
      body: req.body,
      params: req.params,
      query: req.query,
    },
  };
}

// Export commonly used permission constants
export const PERMISSIONS = {
  CASES: {
    READ: 'cases:read',
    WRITE: 'cases:write',
    DELETE: 'cases:delete',
    CREATE: 'cases:create',
  },
  DOCUMENTS: {
    READ: 'documents:read',
    WRITE: 'documents:write',
    DELETE: 'documents:delete',
    UPLOAD: 'documents:upload',
  },
  APPOINTMENTS: {
    READ: 'appointments:read',
    WRITE: 'appointments:write',
    DELETE: 'appointments:delete',
    SCHEDULE: 'appointments:schedule',
  },
  USERS: {
    READ: 'users:read',
    WRITE: 'users:write',
    DELETE: 'users:delete',
    MANAGE: 'users:manage',
  },
  ADMIN: {
    ACCESS: 'admin:access',
    AUDIT: 'admin:audit',
    MANAGE: 'admin:manage',
  },
} as const;

// Export role constants
export const ROLES = {
  PATIENT: 'patient',
  DOCTOR: 'doctor',
  ADMIN: 'admin',
  AGENT: 'agent',
} as const;
