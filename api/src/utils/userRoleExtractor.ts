import { Request } from 'express';

import { logger } from '../utils/structuredLogger';
/**
 * Interface to augment Express Request with OPAL user context
 * This is for TypeScript type checking only and doesn't affect runtime
 */
interface OPALUserContext {
  id: string;
  roles: string[];
  [key: string]: any;
}

/**
 * Interface to augment Express Request with legacy user context
 * This is for TypeScript type checking only and doesn't affect runtime
 */
interface LegacyUserContext {
  id: string;
  role?: string;
  roles?: string[];
  [key: string]: any;
}

/**
 * Extracts user ID from request object, supporting both standard and OPAL auth
 * @param req Express request object
 * @returns User ID string or empty string if not found
 */
export const extractUserId = (req: Request): string => {
  const opalUser = (req as any).opalUser as OPALUserContext | undefined;
  const legacyUser = (req as any).user as LegacyUserContext | undefined;
  
  // Return user ID or empty string, ensuring string type
  return ((opalUser?.id || legacyUser?.id || '').toString());
};

/**
 * Extracts user roles from request object, supporting both standard and OPAL auth
 * @param req Express request object
 * @returns Array of user roles or empty array if none found
 */
export const extractUserRoles = (req: Request): string[] => {
  const opalUser = (req as any).opalUser as OPALUserContext | undefined;
  const legacyUser = (req as any).user as LegacyUserContext | undefined;
  
  // Try to get roles from various sources with fallbacks
  if (opalUser?.roles && Array.isArray(opalUser.roles)) {
    return opalUser.roles;
  }
  
  if (legacyUser?.roles && Array.isArray(legacyUser.roles)) {
    return legacyUser.roles;
  }
  
  if (legacyUser?.role) {
    return [legacyUser.role];
  }
  
  return [];
};

/**
 * Extracts primary user role from request object for backward compatibility
 * @param req Express request object
 * @param defaultRole Default role to return if no roles found
 * @returns Primary user role string
 */
export const extractPrimaryRole = (req: Request, defaultRole: string = 'guest'): string => {
  const roles = extractUserRoles(req);
  return roles.length > 0 ? roles[0] : defaultRole;
};

/**
 * Checks if user has a specific role
 * @param req Express request object
 * @param role Role to check for
 * @returns True if user has the specified role
 */
export const hasRole = (req: Request, role: string): boolean => {
  const roles = extractUserRoles(req);
  return roles.includes(role);
};

/**
 * Checks if user has any of the specified roles
 * @param req Express request object
 * @param allowedRoles Array of roles to check for
 * @returns True if user has any of the specified roles
 */
export const hasAnyRole = (req: Request, allowedRoles: string[]): boolean => {
  const roles = extractUserRoles(req);
  return roles.some(role => allowedRoles.includes(role));
};

/**
 * Logs user role information for debugging
 * @param req Express request object
 * @param context Optional context string for the log
 */
export const logUserRoleInfo = (req: Request, context: string = 'User role info'): void => {
  const userId = extractUserId(req);
  const roles = extractUserRoles(req);
  const primaryRole = extractPrimaryRole(req);
  
  logger.info('${context} - User ID: ${userId}, Roles: ${JSON.stringify(roles)}, Primary role: ${primaryRole}', { requestId: 'context-needed' }, { data: undefined });
  logger.info('Auth sources - req.user: ${!!(req as any).user}, req.opalUser: ${!!(req as any).opalUser}', { requestId: 'context-needed' }, { data: undefined });
};
