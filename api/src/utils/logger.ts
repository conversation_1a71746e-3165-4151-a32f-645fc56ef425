import { db } from '../db/index.js';
import { auditLogs } from '../db/schema/users.js';
import { logger } from '../utils/structuredLogger';
import winston from 'winston';

// Re-export logger for use by other modules
export { logger };
// Sensitive data patterns to redact from logs
const SENSITIVE_PATTERNS = [
  // SSN patterns
  /\b\d{3}-\d{2}-\d{4}\b/g,
  /\b\d{9}\b/g,
  // Email patterns
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b/g,
  // Phone number patterns
  /\b\(\d{3}\)\s?\d{3}-\d{4}\b/g,
  /\b\d{3}-\d{3}-\d{4}\b/g,
  // Credit card patterns
  /\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b/g,
  // Date of birth patterns
  /\b\d{2}\/\d{2}\/\d{4}\b/g,
  /\b\d{2}-\d{2}-\d{4}\b/g,
  // Medical record numbers (generic pattern)
  /\bMRN:?\s*\d+\b/gi,
  // Patient ID patterns
  /\bpatient[_-]?id:?\s*\w+\b/gi,
  /\bpatient:?\s*\w+\b/gi
];

// Function to redact sensitive data
function redactSensitiveData(data: string): string {
  if (!data) return data;
  let result = data;
  
  // Apply each pattern
  SENSITIVE_PATTERNS.forEach(pattern => {
    result = result.replace(pattern, '[REDACTED]');
  });
  
  return result;
}

// Custom format to redact sensitive information
const redactFormat = winston.format((info) => {
  // Redact message if it's a string
  if (typeof info.message === 'string') {
    info.message = redactSensitiveData(info.message);
  }
  
  // Handle details/metadata objects
  if (info.details) {
    // Don't modify original object, create a sanitized copy
    const sanitized = { ...info.details };
    
    // Redact potentially sensitive fields
    const sensitiveFields = ['password', 'token', 'accessToken', 'refreshToken', 'email', 'phone', 'address', 'dob', 'ssn', 'medicalRecord'];
    sensitiveFields.forEach(field => {
      if (field in sanitized) {
        (sanitized as any)[field] = '[REDACTED]';
      }
    });
    
    // Stringify and redact any remaining patterns
    if (Object.keys(sanitized).length > 0) {
      const jsonString = JSON.stringify(sanitized);
      const redactedString = redactSensitiveData(jsonString);
      info.details = JSON.parse(redactedString);
    }
  }
  
  // Handle request bodies and query parameters
  if (info.body) info.body = '[REDACTED]';
  if (info.query) info.query = '[REDACTED]';
  
  return info;
})();

// Legacy winston logger removed - using structured logger instead

// HIPAA audit logging is now handled directly through database writes
// in the logAuditEvent function below

// Log HIPAA-compliant audit events to database
export function logAuditEvent(
  userId: string | null,
  action: string,
  resource: string,
  details?: Record<string, any>
) {
  // Make this non-blocking by using Promise without await
  Promise.resolve().then(async () => {
    try {
      // Insert into audit_logs table
      await db.insert(auditLogs).values({
        userId: userId || null,
        action,
        resource,
        resourceId: details?.resourceId,
        ipAddress: details?.ip || 'unknown',
        userAgent: details?.userAgent || 'unknown',
        metadata: details ? JSON.stringify(details) : null,
        timestamp: new Date(),
      });
    } catch (error) {
      // Silently fail with console log
      logger.error('Failed to write audit log to database:', error);
    }
  });
}
