import { Request } from 'express';
import { logger, LogContext, SecurityEventType } from './structuredLogger';
import { SECURITY_EVENTS, AUDIT_ACTIONS } from '../config/logging';

/**
 * Security Event Logger
 * Specialized logging for security events and audit trails
 * Always logs regardless of log level for compliance
 */
export class SecurityLogger {
  /**
   * Log authentication events
   */
  static logAuthSuccess(req: Request, userId: string, method: string = 'password') {
    const context: LogContext = {
      userId,
      sessionId: (req as any).sessionId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
    };

    logger.security(
      SecurityEventType.AUTH_SUCCESS,
      SECURITY_EVENTS.AUTH_SUCCESS,
      context,
      {
        authMethod: method,
        timestamp: new Date().toISOString(),
      }
    );
  }

  static logAuthFailure(req: Request, email?: string, reason?: string) {
    const context: LogContext = {
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
    };

    logger.security(
      SecurityEventType.AUTH_FAILURE,
      SECURITY_EVENTS.AUTH_FAILURE,
      context,
      {
        email: email ? email.substring(0, 3) + '***' : 'unknown', // Partial email for security
        reason,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * Log permission denied events
   */
  static logPermissionDenied(req: Request, resource: string, action: string, userId?: string) {
    const context: LogContext = {
      userId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      resource,
      action,
    };

    logger.security(
      SecurityEventType.PERMISSION_DENIED,
      SECURITY_EVENTS.PERMISSION_DENIED,
      context,
      {
        attemptedResource: resource,
        attemptedAction: action,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * Log suspicious activity
   */
  static logSuspiciousActivity(req: Request, description: string, userId?: string, severity: 'low' | 'medium' | 'high' = 'medium') {
    const context: LogContext = {
      userId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
    };

    // Use ERROR level for high severity suspicious activity
    const logMethod = severity === 'high' ? 'error' : 'warn';
    
    logger[logMethod](
      `SUSPICIOUS_ACTIVITY: ${description}`,
      severity === 'high' ? new Error(description) : undefined,
      context,
      {
        severity,
        description,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * Log data access for HIPAA compliance
   */
  static logDataAccess(req: Request, resourceType: string, resourceId: string, userId: string) {
    const context: LogContext = {
      userId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      resource: resourceType,
    };

    logger.security(
      SecurityEventType.DATA_ACCESS,
      SECURITY_EVENTS.DATA_ACCESS,
      context,
      {
        resourceType,
        resourceId,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * Log data modifications for audit trail
   */
  static logDataModification(req: Request, resourceType: string, resourceId: string, action: string, userId: string, changes?: any) {
    const context: LogContext = {
      userId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      resource: resourceType,
      action,
    };

    logger.security(
      SecurityEventType.DATA_MODIFICATION,
      SECURITY_EVENTS.DATA_MODIFICATION,
      context,
      {
        resourceType,
        resourceId,
        action,
        changes: changes ? sanitizeChanges(changes) : undefined,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * Log account security events
   */
  static logAccountLocked(userId: string, reason: string, ip?: string) {
    const context: LogContext = {
      userId,
      ip,
    };

    logger.security(
      SecurityEventType.ACCOUNT_LOCKED,
      SECURITY_EVENTS.ACCOUNT_LOCKED,
      context,
      {
        reason,
        timestamp: new Date().toISOString(),
      }
    );
  }

  static logPasswordReset(req: Request, userId: string, stage: 'requested' | 'completed') {
    const context: LogContext = {
      userId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
    };

    logger.security(
      SecurityEventType.PASSWORD_RESET,
      SECURITY_EVENTS.PASSWORD_RESET,
      context,
      {
        stage,
        timestamp: new Date().toISOString(),
      }
    );
  }
}

/**
 * Audit Logger
 * For compliance and business audit trails
 */
export class AuditLogger {
  /**
   * Log CRUD operations on sensitive resources
   */
  static logResourceOperation(
    req: Request,
    action: keyof typeof AUDIT_ACTIONS,
    resource: string,
    resourceId: string,
    userId: string,
    details?: any
  ) {
    const context: LogContext = {
      userId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      resource,
      action: AUDIT_ACTIONS[action],
    };

    logger.audit(
      AUDIT_ACTIONS[action],
      resource,
      context,
      {
        resourceId,
        details: details ? sanitizeAuditDetails(details) : undefined,
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * Log administrative actions
   */
  static logAdminAction(req: Request, action: string, target: string, userId: string, details?: any) {
    const context: LogContext = {
      userId,
      requestId: req.id,
      ip: req.ip || req.socket?.remoteAddress,
      userAgent: req.headers['user-agent'],
      endpoint: req.path,
      method: req.method,
      action,
    };

    logger.audit(
      action,
      `admin_${target}`,
      context,
      {
        adminAction: action,
        target,
        details: details ? sanitizeAuditDetails(details) : undefined,
        timestamp: new Date().toISOString(),
      }
    );
  }
}

/**
 * Sanitize sensitive data from changes object
 */
function sanitizeChanges(changes: any): any {
  const sensitiveFields = ['password', 'passwordHash', 'token', 'secret', 'apiKey'];
  const sanitized = { ...changes };
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

/**
 * Sanitize audit details
 */
function sanitizeAuditDetails(details: any): any {
  if (typeof details !== 'object' || details === null) {
    return details;
  }
  
  return sanitizeChanges(details);
}
