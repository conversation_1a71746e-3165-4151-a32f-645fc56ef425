import winston from 'winston';
import { Request } from 'express';
import { getLoggingConfig } from '../config/logging';

// Log levels following your requirements
export enum LogLevel {
  DEBUG = 'debug',   // Developer debugging during development
  INFO = 'info',     // Production debugging with context
  WARN = 'warn',     // Issues that need tickets/attention
  ERROR = 'error'    // Critical issues that wake up the CTO
}

// Context interface for structured logging
export interface LogContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  userAgent?: string;
  ip?: string;
  endpoint?: string;
  method?: string;
  caseId?: string;
  organizationId?: string;
  action?: string;
  resource?: string;
  duration?: number;
  statusCode?: number;
  service?: string;
  // Allow additional properties for flexibility
  [key: string]: any;
}

// Security event types
export enum SecurityEventType {
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILURE = 'auth_failure',
  PERMISSION_DENIED = 'permission_denied',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  ACCOUNT_LOCKED = 'account_locked',
  PASSWORD_RESET = 'password_reset',
  PRIVILEGE_ESCALATION = 'privilege_escalation'
}

class StructuredLogger {
  private logger: winston.Logger;
  private isProduction: boolean;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';

    // Get environment-specific logging configuration
    const config = getLoggingConfig();
    const logLevel = config.level;
    
    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          return JSON.stringify({
            timestamp,
            level: level.toUpperCase(),
            message,
            ...meta
          });
        })
      ),
      defaultMeta: {
        service: 'continuia-api',
        environment: process.env.NODE_ENV || 'development'
      },
      transports: [
        // Console transport for development
        new winston.transports.Console({
          format: this.isProduction 
            ? winston.format.json()
            : winston.format.combine(
                winston.format.colorize(),
                winston.format.simple()
              )
        }),
        
        // File transports for production
        ...(this.isProduction ? [
          new winston.transports.File({ 
            filename: 'logs/error.log', 
            level: 'error',
            maxsize: 10485760, // 10MB
            maxFiles: 5
          }),
          new winston.transports.File({ 
            filename: 'logs/combined.log',
            maxsize: 10485760, // 10MB
            maxFiles: 10
          })
        ] : [])
      ]
    });
  }

  /**
   * DEBUG: Developer debugging during development
   * Only logged in development mode
   */
  debug(message: string, context?: Partial<LogContext>, meta?: any) {
    if (!this.isProduction) {
      this.logger.debug(message, { context, ...meta });
    }
  }

  /**
   * INFO: Production debugging with context
   * Must include user/session/request context for traceability
   * Use for: API calls, business logic flow, data operations
   */
  info(message: string, context?: Partial<LogContext>, meta?: any) {
    // If no context provided, create minimal context for server startup logs
    const logContext = context || { service: 'continuia-api' };

    // Validate that we have enough context for production debugging (skip for startup logs)
    if (this.isProduction && context && !context.userId && !context.sessionId && !context.requestId) {
      this.logger.warn('INFO log missing context - this log will be hard to trace in production', {
        message,
        context: logContext,
        ...meta
      });
    }

    this.logger.info(message, { context: logContext, ...meta });
  }

  /**
   * WARN: Issues that need tickets/attention
   * Use for: Recoverable errors, deprecated API usage, rate limiting, validation failures
   */
  warn(message: string, context?: Partial<LogContext>, meta?: any) {
    this.logger.warn(message, { context, ...meta });
  }

  /**
   * ERROR: Critical issues that wake up the CTO at 2AM
   * Use for: System failures, data corruption, security breaches, payment failures
   */
  error(message: string, error?: Error, context?: Partial<LogContext>, meta?: any) {
    this.logger.error(message, { 
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : undefined,
      context, 
      ...meta 
    });
  }

  /**
   * Security event logging for audit trails
   */
  security(eventType: SecurityEventType, message: string, context: LogContext, meta?: any) {
    this.logger.info(`SECURITY_EVENT: ${message}`, {
      securityEvent: eventType,
      context,
      ...meta
    });
  }

  /**
   * Audit logging for compliance
   */
  audit(action: string, resource: string, context: LogContext, meta?: any) {
    this.logger.info(`AUDIT: ${action} on ${resource}`, {
      auditEvent: true,
      action,
      resource,
      context,
      ...meta
    });
  }

  /**
   * Performance logging
   */
  performance(operation: string, duration: number, context: LogContext, meta?: any) {
    const level = duration > 5000 ? 'warn' : 'info'; // Warn if operation takes > 5 seconds
    
    this.logger.log(level, `PERFORMANCE: ${operation} took ${duration}ms`, {
      performanceEvent: true,
      operation,
      duration,
      context,
      ...meta
    });
  }

  /**
   * Extract context from Express request
   */
  static extractRequestContext(req: Request): LogContext {
    return {
      requestId: req.headers['x-request-id'] as string || req.id,
      userId: (req as any).user?.id,
      sessionId: (req as any).sessionId,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.socket?.remoteAddress,
      endpoint: req.path,
      method: req.method
    };
  }

  /**
   * Create child logger with persistent context
   */
  child(context: Partial<LogContext>) {
    return {
      debug: (message: string, meta?: any) => this.debug(message, context, meta),
      info: (message: string, additionalContext?: Partial<LogContext>, meta?: any) => 
        this.info(message, { ...context, ...additionalContext } as LogContext, meta),
      warn: (message: string, additionalContext?: Partial<LogContext>, meta?: any) => 
        this.warn(message, { ...context, ...additionalContext }, meta),
      error: (message: string, error?: Error, additionalContext?: Partial<LogContext>, meta?: any) => 
        this.error(message, error, { ...context, ...additionalContext }, meta),
      security: (eventType: SecurityEventType, message: string, additionalContext?: Partial<LogContext>, meta?: any) =>
        this.security(eventType, message, { ...context, ...additionalContext } as LogContext, meta),
      audit: (action: string, resource: string, additionalContext?: Partial<LogContext>, meta?: any) =>
        this.audit(action, resource, { ...context, ...additionalContext } as LogContext, meta),
      performance: (operation: string, duration: number, additionalContext?: Partial<LogContext>, meta?: any) =>
        this.performance(operation, duration, { ...context, ...additionalContext } as LogContext, meta)
    };
  }
}

// Export singleton instance
export const logger = new StructuredLogger();
export { StructuredLogger };
