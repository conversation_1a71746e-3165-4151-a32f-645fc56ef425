import express from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import { config } from 'dotenv';
import { createServer } from 'http';
import { YjsCollaborativeNotesServer } from './services/websocket-server';
import { sql } from './db/index.js';
import { logger } from './utils/structuredLogger';

import { requestLoggingMiddleware, errorLoggingMiddleware } from './middleware/requestLogging';
import { errorHandler } from './middleware/errorHandler.js';

// Enhanced security middleware imports
import { 
  generalRateLimiter, 
  authRateLimiter, 
  uploadRateLimiter, 
  burstProtection,
  roleBasedRateLimiter 
} from './middleware/rateLimiting.js';
import { 
  enhancedSecurityHeaders, 
  customSecurityHeaders, 
  enhancedCorsOptions, 
  requestSanitizer, 
  ddosProtection 
} from './middleware/securityHeaders.js';
import { 
  requestLogger, 
  anomalyDetection, 
  performanceMonitor 
} from './middleware/requestMonitoring.js';
// OPAL-style authentication imports
import { PolicyEngine } from './services/policyEngine.js';

// Route imports
import { authRoutes } from './routes/auth';
import { socialAuthRoutes } from './routes/social-auth';
import { userRoutes } from './routes/users';
import { healthRoutes } from './routes/health';
import { casesRoutes } from './routes/cases';
import { caseNotesRoutes } from './routes/case-notes';
import { appointmentsRoutes } from './routes/appointments';
import { opinionsRoutes } from './routes/opinions';
import { storageRoutes } from './routes/storage';
import { adminRoutes } from './routes/admin';
import profileRoutes from './routes/profile';
import legalComplianceRoutes from './routes/legal-compliance';
import consentCheckRoutes from './routes/consent-check';
import { dashboardRoutes } from './routes/dashboard';
import auditRoutes from './routes/audit';
import noteTypesRoutes from './routes/note-types';
import caseDiscussionsRoutes from './routes/case-discussions';
import { doctorCredentialsRoutes } from './routes/doctor-credentials';
import specializationsRoutes from './routes/specializations';
import { unifiedNotesRoutes } from './routes/unified-notes';
import { medicalTerminologyRoutes } from './routes/medical-terminology';

// Load environment variables
config();

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy (required for Traefik/reverse proxy)
// Trust only the first proxy (Traefik) for security
app.set('trust proxy', 1);

// Enhanced Security Middleware Stack
// Order is important for security effectiveness

// 1. DDoS and request sanitization (first line of defense)
app.use(ddosProtection);
app.use(requestSanitizer);

// 2. Burst protection (very short window, low limits)
app.use(burstProtection);

// 3. Enhanced security headers
app.use(enhancedSecurityHeaders);
app.use(customSecurityHeaders);

// 4. Enhanced CORS configuration
app.use(cors(enhancedCorsOptions));

// 5. Request monitoring and logging
app.use(requestLoggingMiddleware); // Structured logging with context
app.use(requestLogger);
app.use(anomalyDetection);
app.use(performanceMonitor);

// 5.5 User role extraction middleware removed from global scope
// The opalAuthMiddleware already handles user context properly
// userRoleMiddleware should only be used on specific routes if needed

// 6. Cookie parsing middleware (for HTTP-only cookies)
app.use(cookieParser());

// 7. General rate limiting (applies to all requests)
app.use(generalRateLimiter);

// Body parsing middleware - exclude file upload routes from JSON parsing
app.use((req, res, next) => {
  // Skip JSON parsing for file upload routes
  if (req.path.includes('/storage/') && req.method === 'POST') {
    return next();
  }
  express.json({ limit: '10mb' })(req, res, next);
});
app.use((req, res, next) => {
  // Skip URL-encoded parsing for file upload routes
  if (req.path.includes('/storage/') && req.method === 'POST') {
    return next();
  }
  express.urlencoded({ extended: true, limit: '10mb' })(req, res, next);
});

// Enhanced route-specific rate limiting
// Apply specific rate limits to different route types
app.use('/api/auth', authRateLimiter);
app.use('/api/storage', uploadRateLimiter);

// Role-based rate limiting for authenticated routes (applied after auth middleware)
// This will be applied in individual route files where auth is required

// API Routes - No /api prefix needed since we're on api.continuia.ai subdomain
app.use('/health', healthRoutes);
app.use('/auth', authRoutes);
app.use('/social-auth', socialAuthRoutes);
app.use('/users', userRoutes);
app.use('/profile', profileRoutes);
app.use('/cases', casesRoutes);
app.use('/cases', caseNotesRoutes);
app.use('/appointments', appointmentsRoutes);
// Removed redundant /documents route - using /storage/documents instead
app.use('/opinions', opinionsRoutes);
app.use('/storage', storageRoutes);
app.use('/case-notes', caseNotesRoutes);
app.use('/note-types', noteTypesRoutes);
app.use('/legal-compliance', legalComplianceRoutes);
app.use('/user-legal-agreements', consentCheckRoutes);
app.use('/dashboard', dashboardRoutes);
app.use('/audit', auditRoutes);
app.use('/admin', adminRoutes); // Add admin routes
app.use('/', caseDiscussionsRoutes); // Add case discussions routes
app.use('/doctor', doctorCredentialsRoutes); // Add doctor credentials routes
app.use('/specializations', specializationsRoutes); // Add specializations routes
app.use('/unified-notes', unifiedNotesRoutes); // Add unified notes routes
app.use('/medical', medicalTerminologyRoutes); // Add medical terminology routes

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Continuia Healthcare Platform API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
  });
});

// Debug middleware to log all requests
app.use('*', (req, res, next) => {
  console.log('🔍 Express request:', {
    method: req.method,
    url: req.originalUrl,
    headers: req.headers,
    isWebSocket: req.headers.upgrade === 'websocket'
  });
  next();
});

// 404 handler
app.use('*', (req, res) => {
  console.log('🔍 404 handler reached for:', req.originalUrl);
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
  });
});

// Global error handler with structured logging
app.use(errorLoggingMiddleware);
app.use(errorHandler);

// Start server
async function startServer() {
  try {
    // Check database connection
    logger.info('🔄 Checking database connection...');
    await sql`SELECT 1`;
    logger.info('✅ Database connection established');

    // Initialize OPAL Policy Engine with database data
    const policyEngine = PolicyEngine.getInstance();
    await policyEngine.initialize();
    logger.info('✅ OPAL Policy Engine initialized');

    logger.info('Starting server...');

    // Create HTTP server
    const server = createServer(app);

    // Add debugging for HTTP upgrade events
    server.on('upgrade', (request, socket, head) => {
      console.log('🔍 HTTP upgrade event triggered:', {
        url: request.url,
        headers: request.headers
      });
    });

    // Initialize yJS WebSocket server for collaborative medical notes
    const wsServer = new YjsCollaborativeNotesServer(server);

    server.listen(PORT, () => {
      logger.info(`🚀 Continuia API server running on port ${PORT}`);
      logger.info(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
      logger.info(`🔗 API URL: http://localhost:${PORT}`);
      logger.info(`📊 Health check: http://localhost:${PORT}/health`);
      logger.info(`🔐 OPAL-style authorization enabled`);
      logger.info(`🔗 WebSocket server running on ws://localhost:${PORT}/ws`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();
