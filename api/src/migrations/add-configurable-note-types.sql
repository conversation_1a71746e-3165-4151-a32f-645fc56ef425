-- Migration: Add configurable note types system
-- This enables adding new note types (like raccha.ai Notes) via database/config only

-- Create note_types configuration table
CREATE TABLE IF NOT EXISTS note_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Basic Configuration
  key VARCHAR(50) NOT NULL UNIQUE, -- e.g., 'clinical_notes', 'raccha_ai_notes'
  name VARCHAR(100) NOT NULL, -- e.g., 'Clinical Notes', 'Raccha.AI Notes'
  description TEXT, -- e.g., 'Private clinical observations and notes'
  
  -- UI Configuration
  icon VARCHAR(50) DEFAULT 'FileText', -- Lucide icon name
  color VARCHAR(50) DEFAULT 'blue', -- Tailwind color scheme
  category VARCHAR(50) DEFAULT 'clinical', -- clinical, ai, administrative, etc.
  
  -- Access Control
  allowed_roles JSONB NOT NULL DEFAULT '["doctor", "admin"]', -- JSON array of roles
  requires_doctor <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT false, -- Must have doctor assigned to case
  requires_permission VARCHAR(100), -- Optional specific permission
  
  -- Behavior Configuration
  auto_save BOOLEAN DEFAULT true,
  auto_save_delay INTEGER DEFAULT 2000, -- milliseconds
  rich_text BOOLEAN DEFAULT true,
  show_doctor_info BOOLEAN DEFAULT true,
  show_in_sidebar BOOLEAN DEFAULT false, -- Show in right sidebar vs main content
  
  -- Template Configuration
  placeholder TEXT,
  template JSONB, -- Optional structured template/schema
  
  -- AI/Automation Configuration
  ai_enabled BOOLEAN DEFAULT false, -- Can AI agents write to this note type
  ai_model VARCHAR(100), -- e.g., 'raccha.ai', 'gpt-4'
  ai_prompt TEXT, -- System prompt for AI generation
  
  -- Display Configuration
  sort_order INTEGER DEFAULT 0, -- Order in UI
  is_active BOOLEAN DEFAULT true,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID -- Who configured this note type
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_note_types_key ON note_types(key);
CREATE INDEX IF NOT EXISTS idx_note_types_category ON note_types(category);
CREATE INDEX IF NOT EXISTS idx_note_types_active ON note_types(is_active);
CREATE INDEX IF NOT EXISTS idx_note_types_sort_order ON note_types(sort_order);
CREATE INDEX IF NOT EXISTS idx_note_types_allowed_roles ON note_types USING gin(allowed_roles);

-- Update case_notes table to reference configurable note types
ALTER TABLE case_notes DROP COLUMN IF EXISTS note_type;
ALTER TABLE case_notes ADD COLUMN IF NOT EXISTS note_type_id UUID REFERENCES note_types(id);

-- Create index for note_type_id
CREATE INDEX IF NOT EXISTS idx_case_notes_note_type_id ON case_notes(note_type_id);

-- Insert default note types
INSERT INTO note_types (key, name, description, icon, color, category, allowed_roles, requires_doctor, show_in_sidebar, placeholder, sort_order) VALUES
  ('clinical_notes', 'Clinical Notes', 'Private clinical observations and notes', 'Stethoscope', 'blue', 'clinical', '["doctor", "admin"]', false, true, 'Add your clinical observations and notes here...', 1),
  ('second_opinion', 'Second Opinion', 'Specialist second opinion notes', 'Users', 'green', 'clinical', '["doctor", "admin"]', true, false, 'Provide your second opinion analysis...', 2),
  ('consultation', 'Consultation Notes', 'Consultation session notes', 'MessageSquare', 'purple', 'clinical', '["doctor", "admin"]', true, false, 'Document consultation discussion and recommendations...', 3),
  ('progress_note', 'Progress Notes', 'Patient progress tracking', 'TrendingUp', 'orange', 'clinical', '["doctor", "admin"]', false, false, 'Track patient progress and treatment response...', 4),
  ('raccha_ai_notes', 'Raccha.AI Notes', 'AI-generated clinical insights and recommendations', 'Brain', 'indigo', 'ai', '["doctor", "admin", "agent"]', false, false, 'AI-generated insights will appear here...', 10),
  ('administrative_notes', 'Administrative Notes', 'Administrative and billing notes', 'Clipboard', 'gray', 'administrative', '["admin", "agent"]', false, false, 'Administrative notes and billing information...', 20)
ON CONFLICT (key) DO NOTHING;

-- Update existing case_notes to use clinical_notes as default
UPDATE case_notes 
SET note_type_id = (SELECT id FROM note_types WHERE key = 'clinical_notes')
WHERE note_type_id IS NULL;

-- Make note_type_id NOT NULL after setting defaults
ALTER TABLE case_notes ALTER COLUMN note_type_id SET NOT NULL;

-- Add trigger to update updated_at timestamp for note_types
CREATE OR REPLACE FUNCTION update_note_types_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_note_types_updated_at
  BEFORE UPDATE ON note_types
  FOR EACH ROW
  EXECUTE FUNCTION update_note_types_updated_at();

-- Add comments for documentation
COMMENT ON TABLE note_types IS 'Configurable note types - add new types here to automatically generate UI';
COMMENT ON COLUMN note_types.key IS 'Unique identifier for note type (used in code)';
COMMENT ON COLUMN note_types.allowed_roles IS 'JSON array of roles that can access this note type';
COMMENT ON COLUMN note_types.ai_enabled IS 'Whether AI agents can write to this note type';
COMMENT ON COLUMN note_types.show_in_sidebar IS 'Whether to show in right sidebar (true) or main content area (false)';
COMMENT ON COLUMN note_types.template IS 'Optional JSONB template/schema for structured notes';
