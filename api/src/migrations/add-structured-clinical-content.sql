-- Migration: Add structured clinical content storage
-- This migration adds JSONB columns for structured clinical data while preserving existing text fields for backward compatibility

-- Add structured content columns to cases table
ALTER TABLE cases ADD COLUMN IF NOT EXISTS structured_content JSONB DEFAULT '{}';

-- Create case_doctors table for multi-doctor case assignments
CREATE TABLE IF NOT EXISTS case_doctors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  case_id UUID NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  doctor_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Preserve record when doctor deleted
  role VARCHAR(20) NOT NULL DEFAULT 'collaborating', -- 'primary', 'consulting', 'second_opinion', 'reviewing', 'collaborating'
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  assigned_by UUID REFERENCES users(id) ON DELETE SET NULL, -- Preserve who assigned
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(case_id, doctor_id) -- Prevent duplicate assignments
);

-- Create case_notes table for all clinical notes (doctor notes, opinions, etc.)
CREATE TABLE IF NOT EXISTS case_notes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  case_id UUID NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  author_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Who created the note - preserve when user deleted
  case_doctor_id UUID REFERENCES case_doctors(id) ON DELETE SET NULL, -- Which doctor assignment this note relates to (if any)
  note_type VARCHAR(50) NOT NULL, -- 'clinical_notes', 'second_opinion', 'consultation', 'progress_note'
  structured_content JSONB NOT NULL DEFAULT '{}',
  raw_content TEXT, -- Full Lexical editor state for rich formatting
  version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_case_doctors_case_id ON case_doctors(case_id);
CREATE INDEX IF NOT EXISTS idx_case_doctors_doctor_id ON case_doctors(doctor_id);
CREATE INDEX IF NOT EXISTS idx_case_doctors_role ON case_doctors(role);
CREATE INDEX IF NOT EXISTS idx_case_doctors_active ON case_doctors(is_active);

CREATE INDEX IF NOT EXISTS idx_case_notes_case_id ON case_notes(case_id);
CREATE INDEX IF NOT EXISTS idx_case_notes_author_id ON case_notes(author_id);
CREATE INDEX IF NOT EXISTS idx_case_notes_case_doctor_id ON case_notes(case_doctor_id);
CREATE INDEX IF NOT EXISTS idx_case_notes_type ON case_notes(note_type);
CREATE INDEX IF NOT EXISTS idx_case_notes_active ON case_notes(is_active);
CREATE GIN INDEX IF NOT EXISTS idx_case_notes_structured ON case_notes USING gin(structured_content);
CREATE GIN INDEX IF NOT EXISTS idx_cases_structured ON cases USING gin(structured_content);

-- Migrate existing case data to structured format
UPDATE cases 
SET structured_content = jsonb_build_object(
  'symptoms', COALESCE(symptoms, ''),
  'medicalHistory', COALESCE(medical_history, ''),
  'currentMedications', COALESCE(current_medications, ''),
  'caseDescription', COALESCE(description, ''),
  'lastUpdated', NOW()::text
)
WHERE structured_content = '{}' OR structured_content IS NULL;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_case_notes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_case_notes_updated_at
  BEFORE UPDATE ON case_notes
  FOR EACH ROW
  EXECUTE FUNCTION update_case_notes_updated_at();

-- Add comments for documentation
COMMENT ON TABLE case_notes IS 'Stores all clinical notes with structured content and rich formatting';
COMMENT ON COLUMN case_notes.structured_content IS 'JSONB containing structured clinical data (sections, fields, metadata)';
COMMENT ON COLUMN case_notes.raw_content IS 'Full Lexical editor state preserving rich formatting and structure';
COMMENT ON COLUMN case_notes.note_type IS 'Type of clinical note: clinical_notes, second_opinion, consultation, progress_note';
COMMENT ON COLUMN cases.structured_content IS 'JSONB containing structured case data (chief complaint, history, medications, description)';
