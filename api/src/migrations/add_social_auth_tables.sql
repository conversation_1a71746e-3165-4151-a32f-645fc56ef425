-- Migration: Add Social Authentication Tables
-- Created: 2025-01-16
-- Description: Adds tables for social logins, OTP verification, and updates users table

-- First, create the new enums
DO $$ BEGIN
    CREATE TYPE social_provider AS ENUM ('google', 'whatsapp', 'facebook', 'apple', 'microsoft');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update users table to support social logins
ALTER TABLE users 
    ALTER COLUMN password_hash DROP NOT NULL,
    ADD COLUMN IF NOT EXISTS profile_picture_url VARCHAR(500),
    ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20);

-- Create social_logins table
CREATE TABLE IF NOT EXISTS social_logins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    provider social_provider NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    provider_email VARCHAR(255),
    provider_data TEXT,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Ensure unique provider + provider_id combination
    UNIQUE(provider, provider_id)
);

-- Create otp_verifications table
CREATE TABLE IF NOT EXISTS otp_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identifier VARCHAR(255) NOT NULL,
    identifier_type VARCHAR(50) NOT NULL,
    otp VARCHAR(10) NOT NULL,
    purpose VARCHAR(50) NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT false,
    expires_at TIMESTAMP NOT NULL,
    attempts VARCHAR(10) NOT NULL DEFAULT '0',
    max_attempts VARCHAR(10) NOT NULL DEFAULT '3',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_social_logins_user_id ON social_logins(user_id);
CREATE INDEX IF NOT EXISTS idx_social_logins_provider ON social_logins(provider);
CREATE INDEX IF NOT EXISTS idx_social_logins_provider_id ON social_logins(provider_id);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_identifier ON otp_verifications(identifier);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_expires_at ON otp_verifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number);

-- Add updated_at trigger for social_logins
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_social_logins_updated_at 
    BEFORE UPDATE ON social_logins 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_otp_verifications_updated_at 
    BEFORE UPDATE ON otp_verifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Clean up expired OTP records (optional, can be run periodically)
-- DELETE FROM otp_verifications WHERE expires_at < NOW() - INTERVAL '1 day';

COMMENT ON TABLE social_logins IS 'Stores social login provider information for users';
COMMENT ON TABLE otp_verifications IS 'Stores OTP verification codes for phone/email verification';
COMMENT ON COLUMN users.profile_picture_url IS 'URL to user profile picture from social providers';
COMMENT ON COLUMN users.phone_number IS 'User phone number for WhatsApp authentication';