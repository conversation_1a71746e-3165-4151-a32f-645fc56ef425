-- Migration: Remove legacy case fields that have been moved to structured notes
-- These fields are now managed in the case_notes table with structured content

-- Remove legacy fields from cases table
ALTER TABLE cases DROP COLUMN IF EXISTS description;
ALTER TABLE cases DROP COLUMN IF EXISTS symptoms;
ALTER TABLE cases DROP COLUMN IF EXISTS medical_history;
ALTER TABLE cases DROP COLUMN IF EXISTS current_medications;

-- Add comments for documentation
COMMENT ON TABLE cases IS 'Cases table - clinical details now stored in case_notes table';
COMMENT ON COLUMN cases.structured_content IS 'JSONB containing case metadata - clinical details moved to case_notes';

-- Update any existing API code comments
-- Note: Frontend and API code will need to be updated to use case_notes instead of these fields
