-- Migration: Add Collaborative Notes Support
-- Description: Add CRDT document storage, canvas blocks, and collaboration features

-- Add new columns to case_notes table for collaborative editing
ALTER TABLE case_notes ADD COLUMN IF NOT EXISTS yjs_document BYTEA;
ALTER TABLE case_notes ADD COLUMN IF NOT EXISTS canvas_blocks J<PERSON><PERSON><PERSON> DEFAULT '[]';
ALTER TABLE case_notes ADD COLUMN IF NOT EXISTS medical_codes JSONB DEFAULT '{}';
ALTER TABLE case_notes ADD COLUMN IF NOT EXISTS active_editors JSONB DEFAULT '[]';
ALTER TABLE case_notes ADD COLUMN IF NOT EXISTS document_version INTEGER DEFAULT 1;
ALTER TABLE case_notes ADD COLUMN IF NOT EXISTS collaboration_enabled BOOLEAN DEFAULT true;

-- Create medical terminology cache table
CREATE TABLE IF NOT EXISTS medical_terminology_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  terminology_type VARCHAR(20) NOT NULL CHECK (terminology_type IN ('snomed', 'rxnav')),
  search_term VARCHAR(255) NOT NULL,
  results JSONB NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  
  -- Index for fast lookups
  UNIQUE(terminology_type, search_term)
);

-- Create collaboration sessions table
CREATE TABLE IF NOT EXISTS collaboration_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  case_id UUID NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  note_type_id UUID NOT NULL REFERENCES note_types(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  session_start TIMESTAMP DEFAULT NOW(),
  session_end TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  cursor_position JSONB,
  user_color VARCHAR(7), -- Hex color for user identification
  
  -- Ensure one active session per user per note
  UNIQUE(case_id, note_type_id, user_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_medical_cache_expires ON medical_terminology_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_collaboration_active ON collaboration_sessions(case_id, note_type_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_case_notes_collaboration ON case_notes(case_id, note_type_id) WHERE collaboration_enabled = true;

-- Add trigger to automatically end collaboration sessions
CREATE OR REPLACE FUNCTION end_collaboration_session()
RETURNS TRIGGER AS $$
BEGIN
  -- End session when is_active is set to false
  IF OLD.is_active = true AND NEW.is_active = false THEN
    NEW.session_end = NOW();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_end_collaboration_session
  BEFORE UPDATE ON collaboration_sessions
  FOR EACH ROW
  EXECUTE FUNCTION end_collaboration_session();

-- Add function to clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_medical_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM medical_terminology_cache WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Add function to get active collaborators for a note
CREATE OR REPLACE FUNCTION get_active_collaborators(p_case_id UUID, p_note_type_id UUID)
RETURNS TABLE(
  user_id UUID,
  user_name TEXT,
  user_color VARCHAR(7),
  cursor_position JSONB,
  session_start TIMESTAMP
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cs.user_id,
    COALESCE(u.first_name || ' ' || u.last_name, u.email) as user_name,
    cs.user_color,
    cs.cursor_position,
    cs.session_start
  FROM collaboration_sessions cs
  JOIN users u ON cs.user_id = u.id
  WHERE cs.case_id = p_case_id 
    AND cs.note_type_id = p_note_type_id 
    AND cs.is_active = true
  ORDER BY cs.session_start;
END;
$$ LANGUAGE plpgsql;

-- Update note_types table to support collaboration settings
ALTER TABLE note_types ADD COLUMN IF NOT EXISTS collaboration_enabled BOOLEAN DEFAULT true;
ALTER TABLE note_types ADD COLUMN IF NOT EXISTS max_collaborators INTEGER DEFAULT 10;
ALTER TABLE note_types ADD COLUMN IF NOT EXISTS canvas_enabled BOOLEAN DEFAULT true;
ALTER TABLE note_types ADD COLUMN IF NOT EXISTS medical_autocomplete BOOLEAN DEFAULT true;
