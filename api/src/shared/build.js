import { logger } from '../utils/structuredLogger';

const fs = require('fs');
const { execSync } = require('child_process');

// Compile TypeScript files to JavaScript
logger.debug('Compiling shared TypeScript files...');

// Compile permissions.ts to permissions.js
try {
  execSync('npx tsc permissions.ts --outDir . --target ES2022 --module ESNext --moduleResolution node --allowSyntheticDefaultImports --esModuleInterop --skipLibCheck --declaration false', { 
    cwd: './shared',
    stdio: 'inherit'
  });
  logger.debug('Successfully compiled permissions.ts to permissions.js');
} catch (error) {
  logger.error('Error compiling permissions.ts:', error.message);
  process.exit(1);
}

logger.debug('Build complete!');
