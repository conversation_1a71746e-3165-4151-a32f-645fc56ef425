// Shared permission logic for frontend and backend
// This ensures consistent authorization checks across the application

export class PermissionService {
    // Parse permission string into structured format
    static parsePermission(permission) {
        const [resource, actionWithModifier] = permission.split(':');
        if (!actionWithModifier) {
            return { resource, action: 'access' };
        }
        const [action, modifier] = actionWithModifier.includes('/')
            ? actionWithModifier.split('/')
            : [actionWithModifier, undefined];
        return { resource, action, modifier };
    }
    // Check if user has a specific role
    static hasRole(user, role) {
        return user.role === role;
    }
    // Check if user has any of the specified roles
    static hasAnyRole(user, roles) {
        return roles.includes(user.role);
    }
    // Check if user is the owner of a resource
    static isOwner(user, resource) {
        if (user.role === 'patient') {
            return resource.patientId === user.id;
        }
        else if (user.role === 'doctor') {
            // For doctor credentials, check if the doctorId matches the user id
            // For other resources, check if they are assigned to the case via case_doctors table
            if (resource && (resource.doctorId === user.id || resource.uploadedBy === user.id)) {
                return true;
            }
            // For list operations without specific resource, allow access
            if (!resource) {
                return true;
            }
            return false;
        }
        return false;
    }
    // Check if user is assigned to a resource
    static isAssigned(user, resource) {
        // For doctors, assignment is now checked via the case_doctors table
        // This method should be updated to check the database for multi-doctor assignments
        if (user.role === 'agent') {
            return resource.assignedAgentId === user.id;
        }
        return false;
    }
    // Check if a case is in draft status
    static isDraft(resource) {
        return resource.status === 'draft';
    }
    // Check if user can access a resource based on permission string
    static canAccessResource(user, permission, resource) {
        const { resource: resourceType, action, modifier } = this.parsePermission(permission);
        // Check if the permission exists in the matrix
        if (!PERMISSION_MATRIX[resourceType] || !PERMISSION_MATRIX[resourceType][action]) {
            return false;
        }
        const allowedRoles = PERMISSION_MATRIX[resourceType][action];
        // Check each allowed role
        for (const allowedRole of allowedRoles) {
            // Handle role with modifier (e.g., 'patient:own')
            if (allowedRole.includes(':')) {
                const [role, requiredModifier] = allowedRole.split(':');
                // Check if user has the required role
                if (user.role === role) {
                    // Handle the modifier
                    switch (requiredModifier) {
                        case 'own':
                            // For list operations without specific resource, we allow the request to proceed
                            // The route handler will filter results based on ownership
                            if (!resource) {
                                return true;
                            }
                            return this.isOwner(user, resource);
                        case 'assigned':
                            // For list operations without specific resource, we allow the request to proceed
                            // The route handler will filter results based on assignment
                            if (!resource) {
                                return true;
                            }
                            return this.isAssigned(user, resource);
                        case 'draft':
                            if (!resource) {
                                return false;
                            }
                            return this.isOwner(user, resource) && this.isDraft(resource);
                        default:
                            // Check if user has the specific role
                            return this.hasRole(user, role);
                    }
                }
            }
            else {
                // Handle simple role (e.g., 'admin')
                if (this.hasRole(user, allowedRole)) {
                    return true;
                }
            }
        }
        return false;
    }
}

// Export permission matrix for consistency between frontend and backend
export const PERMISSION_MATRIX = {
    cases: {
        create: ['patient'],
        read: ['patient:own', 'doctor:assigned', 'admin', 'agent'],
        update: ['patient:own:draft', 'doctor:assigned', 'admin'],
        delete: ['admin'],
        assign: ['admin', 'agent', 'doctor:assigned'],
        submit: ['patient:own'],
        complete: ['doctor:assigned', 'admin']
    },
    'case-notes': {
        create: ['doctor:assigned', 'admin'],
        read: ['patient:own', 'doctor:assigned', 'admin', 'agent'],
        update: ['doctor:own', 'admin'],
        delete: ['admin']
    },
    appointments: {
        create: ['patient', 'doctor', 'admin', 'agent'],
        read: ['patient:own', 'doctor:own', 'admin', 'agent'],
        update: ['patient:own', 'doctor:own', 'admin'],
        cancel: ['patient:own', 'doctor:own', 'admin'],
        delete: ['admin']
    },
    documents: {
        create: ['patient', 'doctor', 'admin'],
        read: ['patient:own', 'doctor:assigned', 'admin', 'agent'],
        update: ['patient:own', 'admin'],
        delete: ['patient:own', 'admin']
    },
    users: {
        create: ['admin'],
        read: ['patient:own', 'doctor:own', 'agent:own', 'admin'],
        update: ['patient:own', 'doctor:own', 'agent:own', 'admin'],
        delete: ['patient:own', 'doctor:own', 'agent:own', 'admin'],
        assign_role: ['admin'],
        impersonate: ['admin']
    },
    dashboard: {
        read: ['patient', 'doctor', 'agent', 'admin']
    },
    doctor_credentials: {
        create: ['doctor:own', 'admin'],
        read: ['doctor:own', 'admin'],
        update: ['doctor:own', 'admin'],
        delete: ['doctor:own', 'admin']
    }
};

// Default export for compatibility
export default {
    PermissionService,
    PERMISSION_MATRIX
};
