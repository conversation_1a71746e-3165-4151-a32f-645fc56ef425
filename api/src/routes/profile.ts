import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { users, userProfiles } from '../db/schema/users.js';
import { eq } from 'drizzle-orm';
import { opalAuthMiddleware } from '../middleware/opalAuth.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { canUserAccess } from '../utils/policyUtils.js';

const router = Router();

// Profile validation schema
const updateProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phoneNumber: z.string().optional(),
  dateOfBirth: z.string().datetime().optional(),
  gender: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  zipCode: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  emergencyContactRelationship: z.string().optional(),
  profilePictureUrl: z.union([z.string(), z.null()]).optional(),
  bio: z.string().optional(),
  // Medical information fields
  bloodType: z.string().optional(),
  insuranceProvider: z.string().optional(),
  insurancePolicyNumber: z.string().optional(),
  allergies: z.array(z.string()).optional(),
  medications: z.array(z.string()).optional(),
  medicalConditions: z.array(z.string()).optional(),
  // Preferences fields
  notificationEmail: z.boolean().optional(),
  notificationSms: z.boolean().optional(),
  notificationPush: z.boolean().optional(),
  privacyShareDataForResearch: z.boolean().optional(),
  privacyAllowMarketingCommunications: z.boolean().optional(),
});

// Get user profile
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;

  // Get user basic info and profile data
  const [result] = await db
    .select({
      // Basic user info
      id: users.id,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      role: users.role,
      isActive: users.isActive,
      isEmailVerified: users.isEmailVerified,
      lastLoginAt: users.lastLoginAt,
      createdAt: users.createdAt,
      // Extended profile info
      phoneNumber: userProfiles.phoneNumber,
      dateOfBirth: userProfiles.dateOfBirth,
      gender: userProfiles.gender,
      address: userProfiles.address,
      city: userProfiles.city,
      state: userProfiles.state,
      country: userProfiles.country,
      zipCode: userProfiles.zipCode,
      emergencyContactName: userProfiles.emergencyContactName,
      emergencyContactPhone: userProfiles.emergencyContactPhone,
      emergencyContactRelationship: userProfiles.emergencyContactRelationship,
      profilePictureUrl: userProfiles.profilePictureUrl,
      bio: userProfiles.bio,
      // Preferences fields
      notificationEmail: userProfiles.notificationEmail,
      notificationSms: userProfiles.notificationSms,
      notificationPush: userProfiles.notificationPush,
      privacyShareDataForResearch: userProfiles.privacyShareDataForResearch,
      privacyAllowMarketingCommunications: userProfiles.privacyAllowMarketingCommunications,
      // Medical information fields
      bloodType: userProfiles.bloodType,
      insuranceProvider: userProfiles.insuranceProvider,
      insurancePolicyNumber: userProfiles.insurancePolicyNumber,
      allergies: userProfiles.allergies,
      medications: userProfiles.medications,
      medicalConditions: userProfiles.medicalConditions,
      profileCreatedAt: userProfiles.createdAt,
      profileUpdatedAt: userProfiles.updatedAt,
    })
    .from(users)
    .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
    .where(eq(users.id, userId))
    .limit(1);

  if (!result) {
    throw new AppError('User not found', 404);
  }

  // Parse JSON array fields back to arrays for frontend
  const processedResult = {
    ...result,
    allergies: result.allergies ? JSON.parse(result.allergies) : [],
    medications: result.medications ? JSON.parse(result.medications) : [],
    medicalConditions: result.medicalConditions ? JSON.parse(result.medicalConditions) : [],
  };

  // Log audit event for profile access
  logAuditEvent(
    userId,
    'PROFILE_ACCESSED',
    'userProfiles',
    { endpoint: '/api/profile' }
  );

  res.json({
    message: 'Profile retrieved successfully',
    profile: processedResult,
  });
}));

// Update user profile
router.put('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const validatedData = updateProfileSchema.parse(req.body);

  // Separate user fields from profile fields
  const userFields: any = {};
  const profileFields: any = {};

  // User table fields
  if (validatedData.firstName) userFields.firstName = validatedData.firstName;
  if (validatedData.lastName) userFields.lastName = validatedData.lastName;

  // Profile table fields
  const profileFieldNames = [
    'phoneNumber', 'dateOfBirth', 'gender', 'address', 'city', 
    'state', 'country', 'zipCode', 'emergencyContactName', 
    'emergencyContactPhone', 'emergencyContactRelationship', 'bio', 
    'bloodType', 'insuranceProvider', 'insurancePolicyNumber', 
    'allergies', 'medications', 'medicalConditions',
    'notificationEmail', 'notificationSms', 'notificationPush',
    'privacyShareDataForResearch', 'privacyAllowMarketingCommunications',
    'profilePictureUrl'
  ];

  profileFieldNames.forEach(field => {
    if (validatedData[field as keyof typeof validatedData] !== undefined) {
      let value = validatedData[field as keyof typeof validatedData];
      // Convert dateOfBirth string to Date object
      if (field === 'dateOfBirth' && typeof value === 'string') {
        value = value; // Keep as string for database storage
      }
      // Convert array fields to JSON strings for storage
      else if (['allergies', 'medications', 'medicalConditions'].includes(field) && Array.isArray(value)) {
        value = JSON.stringify(value);
      }
      profileFields[field] = value;
    }
  });

  // Update user fields if any
  if (Object.keys(userFields).length > 0) {
    userFields.updatedAt = new Date();
    await db
      .update(users)
      .set(userFields)
      .where(eq(users.id, userId));
  }

  // Update or create profile if any profile fields
  if (Object.keys(profileFields).length > 0) {
    profileFields.updatedAt = new Date();

    // Check if profile exists
    const [existingProfile] = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, userId))
      .limit(1);

    if (existingProfile) {
      await db
        .update(userProfiles)
        .set(profileFields)
        .where(eq(userProfiles.userId, userId));
    } else {
      await db
        .insert(userProfiles)
        .values({
          userId,
          ...profileFields,
        });
    }
  }

  // Log audit event
  logAuditEvent(
    userId,
    'PROFILE_UPDATED',
    'userProfiles',
    { 
      endpoint: '/api/profile',
      updatedFields: Object.keys({ ...userFields, ...profileFields })
    }
  );

  res.json({
    message: 'Profile updated successfully',
  });
}));

export default router;
