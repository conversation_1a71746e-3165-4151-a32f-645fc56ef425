import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { noteTypes } from '../db/schema/note-types.js';
import { eq, and, desc } from 'drizzle-orm';
import { opalAuthMiddleware } from '../middleware/opalAuth.js';
import { asyncHandler } from '../middleware/errorHandler.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Schema for note type creation/update
const noteTypeSchema = z.object({
  key: z.string().min(1).max(50),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  icon: z.string().default('FileText'),
  color: z.string().default('blue'),
  category: z.string().default('clinical'),
  allowedRoles: z.array(z.string()).default(['doctor', 'admin']),
  requiresDoctor: z.boolean().default(false),
  requiresPermission: z.string().optional(),
  autoSave: z.boolean().default(true),
  autoSaveDelay: z.number().default(2000),
  richText: z.boolean().default(true),
  showDoctorInfo: z.boolean().default(true),
  showInSidebar: z.boolean().default(false),
  placeholder: z.string().optional(),
  template: z.any().optional(),
  aiEnabled: z.boolean().default(false),
  aiModel: z.string().optional(),
  aiPrompt: z.string().optional(),
  sortOrder: z.number().default(0),
  isActive: z.boolean().default(true),
});

// GET /note-types - Get all note types (filtered by user role)
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    
    // Get all active note types
    const allNoteTypes = await db
      .select()
      .from(noteTypes)
      .where(eq(noteTypes.isActive, true))
      .orderBy(desc(noteTypes.sortOrder));

    // Filter by user role - use OPAL user structure
    const userRoles = user.roles || [];
    const userRole = userRoles[0] || 'guest'; // Get primary role
    const filteredNoteTypes = allNoteTypes.filter(noteType => {
      const allowedRoles = Array.isArray(noteType.allowedRoles)
        ? noteType.allowedRoles
        : JSON.parse(noteType.allowedRoles as string);
      return allowedRoles.includes(userRole);
    });

    res.json({
      success: true,
      noteTypes: filteredNoteTypes,
      total: filteredNoteTypes.length
    });
  } catch (error) {
    logger.error('Error fetching note types:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch note types'
    });
  }
}));

// GET /note-types/:key - Get specific note type by key
router.get('/:key', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const { key } = req.params;
    const user = (req as any).user;

    const noteType = await db
      .select()
      .from(noteTypes)
      .where(and(
        eq(noteTypes.key, key),
        eq(noteTypes.isActive, true)
      ))
      .limit(1);

    if (noteType.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Note type not found'
      });
    }

    // Check if user has access to this note type - use OPAL user structure
    const allowedRoles = Array.isArray(noteType[0].allowedRoles)
      ? noteType[0].allowedRoles
      : JSON.parse(noteType[0].allowedRoles as string);
    
    const userRoles = user.roles || [];
    const userRole = userRoles[0] || 'guest'; // Get primary role
    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        error: 'Access denied to this note type'
      });
    }

    res.json({
      success: true,
      noteType: noteType[0]
    });
  } catch (error) {
    logger.error('Error fetching note type:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch note type'
    });
  }
}));

// POST /note-types - Create new note type (admin only)
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user;
  
  // Only admins can create note types - use OPAL user structure
  const userRoles = user.roles || [];
  if (!userRoles.includes('admin')) {
    return res.status(403).json({
      success: false,
      error: 'Only administrators can create note types'
    });
  }

  const noteTypeData = req.body;

  // Check if key already exists
  const existing = await db
    .select()
    .from(noteTypes)
    .where(eq(noteTypes.key, noteTypeData.key))
    .limit(1);

  if (existing.length > 0) {
    return res.status(400).json({
      success: false,
      error: 'Note type with this key already exists'
    });
  }

  // Create note type
  const newNoteType = await db
    .insert(noteTypes)
    .values({
      ...noteTypeData,
      allowedRoles: JSON.stringify(noteTypeData.allowedRoles),
      template: noteTypeData.template ? JSON.stringify(noteTypeData.template) : null,
      createdBy: user.id,
    })
    .returning();

  res.status(201).json({
    success: true,
    noteType: newNoteType[0],
    message: 'Note type created successfully'
  });
}));

// PUT /note-types/:id - Update note type (admin only)
router.put('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const { id } = req.params;
  
  // Only admins can update note types - use OPAL user structure
  const userRoles = user.roles || [];
  if (!userRoles.includes('admin')) {
    return res.status(403).json({
      success: false,
      error: 'Only administrators can update note types'
    });
  }

  const updateData = req.body;

  // Check if note type exists
  const existing = await db
    .select()
    .from(noteTypes)
    .where(eq(noteTypes.id, id))
    .limit(1);

  if (existing.length === 0) {
    return res.status(404).json({
      success: false,
      error: 'Note type not found'
    });
  }

  // Update note type
  const updatedNoteType = await db
    .update(noteTypes)
    .set({
      ...updateData,
      allowedRoles: updateData.allowedRoles ? JSON.stringify(updateData.allowedRoles) : undefined,
      template: updateData.template ? JSON.stringify(updateData.template) : undefined,
    })
    .where(eq(noteTypes.id, id))
    .returning();

  res.json({
    success: true,
    noteType: updatedNoteType[0],
    message: 'Note type updated successfully'
  });
}));

// DELETE /note-types/:id - Deactivate note type (admin only)
router.delete('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    const { id } = req.params;
    
    // Only admins can deactivate note types - use OPAL user structure
    const userRoles = user.roles || [];
    if (!userRoles.includes('admin')) {
      return res.status(403).json({
        success: false,
        error: 'Only administrators can deactivate note types'
      });
    }

    // Check if note type exists
    const existing = await db
      .select()
      .from(noteTypes)
      .where(eq(noteTypes.id, id))
      .limit(1);

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Note type not found'
      });
    }

    // Deactivate instead of hard delete to preserve data integrity
    await db
      .update(noteTypes)
      .set({ isActive: false })
      .where(eq(noteTypes.id, id));

    res.json({
      success: true,
      message: 'Note type deactivated successfully'
    });
  } catch (error) {
    logger.error('Error deactivating note type:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to deactivate note type'
    });
  }
}));

export default router;
