import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { appointments, users, cases } from '../db/schema/index.js';
import { eq, and, gte, lte, desc, leftJoin } from 'drizzle-orm';
import { opalAuthMiddleware, requireAppointmentRead, requireAppointmentWrite, requireAppointmentDelete, requireResourceAccess } from '../middleware/opalAuth.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';

const router = Router();

// Validation schemas
const createAppointmentSchema = z.object({
  doctorId: z.string().uuid('Invalid doctor ID'),
  caseId: z.string().uuid('Invalid case ID').optional(),
  appointmentType: z.enum(['consultation', 'follow_up', 'review', 'emergency']).default('consultation'),
  scheduledAt: z.string().datetime('Invalid date format'),
  duration: z.number().min(15).max(180).default(30), // Duration in minutes
  notes: z.string().optional(),
  meetingLink: z.string().url().optional(),
});

const updateAppointmentSchema = z.object({
  scheduledAt: z.string().datetime().optional(),
  duration: z.number().min(15).max(180).optional(),
  notes: z.string().optional(),
  meetingLink: z.string().url().optional(),
  status: z.enum(['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show']).optional(),
});

// Create a new appointment
router.post('/', opalAuthMiddleware, requireAppointmentWrite, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  
  // Only patients can create appointments
  if (!userRoles.includes('patient')) {
    throw new AppError('Only patients can create appointments', 403);
  }

  const validatedData = createAppointmentSchema.parse(req.body);

  // Verify doctor exists and is active
  const [doctor] = await db
    .select()
    .from(users)
    .where(and(
      eq(users.id, validatedData.doctorId),
      eq(users.isActive, true)
    ))
    .limit(1);

  if (!doctor) {
    throw new AppError('Doctor not found or inactive', 404);
  }

  // Check for scheduling conflicts
  const appointmentDate = new Date(validatedData.scheduledAt);
  const endTime = new Date(appointmentDate.getTime() + validatedData.duration * 60000);

  const conflictingAppointments = await db
    .select()
    .from(appointments)
    .where(and(
      eq(appointments.doctorId, validatedData.doctorId),
      gte(appointments.scheduledAt, appointmentDate),
      lte(appointments.scheduledAt, endTime),
      eq(appointments.status, 'scheduled')
    ));

  if (conflictingAppointments.length > 0) {
    throw new AppError('Doctor is not available at the requested time', 409);
  }

  const [newAppointment] = await db
    .insert(appointments)
    .values({
      patientId: userId,
      doctorId: validatedData.doctorId,
      caseId: validatedData.caseId,
      appointmentType: validatedData.appointmentType,
      scheduledAt: appointmentDate,
      duration: validatedData.duration,
      notes: validatedData.notes,
      meetingLink: validatedData.meetingLink,
      status: 'scheduled',
    })
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'APPOINTMENT_CREATED',
    'appointments',
    {
      appointmentId: newAppointment.id,
      doctorId: validatedData.doctorId,
      scheduledAt: appointmentDate.toISOString(),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.status(201).json({
    message: 'Appointment created successfully',
    appointment: newAppointment,
  });
}));

// Get all appointments for current user
router.get('/', opalAuthMiddleware, requireAppointmentRead, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || ['guest'];
  const { status, from, to } = req.query;

  let query = db.select({
    id: appointments.id,
    patientId: appointments.patientId,
    doctorId: appointments.doctorId,
    caseId: appointments.caseId,
    appointmentType: appointments.appointmentType,
    scheduledAt: appointments.scheduledAt,
    duration: appointments.duration,
    status: appointments.status,
    notes: appointments.notes,
    meetingLink: appointments.meetingLink,
    createdAt: appointments.createdAt,
    updatedAt: appointments.updatedAt,
    // Case information
    case: {
      id: cases.id,
      title: cases.title,
      status: cases.status,
      urgencyLevel: cases.urgencyLevel,
      specialtyRequired: cases.specialtyRequired,
      // Note: structuredContent removed - clinical data is in case_notes table
    },
  }).from(appointments)
    .leftJoin(cases, eq(appointments.caseId, cases.id));

  if (userRoles.includes('patient')) {
    query = query.where(eq(appointments.patientId, userId));
  } else if (userRoles.includes('doctor')) {
    query = query.where(eq(appointments.doctorId, userId));
  } else if (!userRoles.includes('agent') && !userRoles.includes('admin')) {
    throw new AppError('Unauthorized to view appointments', 403);
  }

  // Add status filter if provided
  if (status && typeof status === 'string') {
    query = query.where(eq(appointments.status, status as any));
  }

  // Add date range filters if provided
  if (from && typeof from === 'string') {
    query = query.where(gte(appointments.scheduledAt, new Date(from)));
  }
  if (to && typeof to === 'string') {
    query = query.where(lte(appointments.scheduledAt, new Date(to)));
  }

  const userAppointments = await query
    .orderBy(desc(appointments.scheduledAt));

  res.json({
    appointments: userAppointments,
    total: userAppointments.length,
  });
}));

// Get a specific appointment by ID
router.get('/:id', opalAuthMiddleware, requireAppointmentRead, asyncHandler(async (req: Request, res: Response) => {
  const appointmentId = req.params.id;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  const [appointment] = await db
    .select({
      id: appointments.id,
      patientId: appointments.patientId,
      doctorId: appointments.doctorId,
      caseId: appointments.caseId,
      appointmentType: appointments.appointmentType,
      scheduledAt: appointments.scheduledAt,
      duration: appointments.duration,
      status: appointments.status,
      notes: appointments.notes,
      meetingLink: appointments.meetingLink,
      createdAt: appointments.createdAt,
      updatedAt: appointments.updatedAt,
      // Case information
      case: {
        id: cases.id,
        title: cases.title,
        status: cases.status,
        urgencyLevel: cases.urgencyLevel,
        specialtyRequired: cases.specialtyRequired,
        // Note: structuredContent removed - clinical data is in case_notes table
      },
    })
    .from(appointments)
    .leftJoin(cases, eq(appointments.caseId, cases.id))
    .where(eq(appointments.id, appointmentId))
    .limit(1);

  if (!appointment) {
    throw new AppError('Appointment not found', 404);
  }

  // Check authorization
  const canAccess =
    userRoles.includes('admin') ||
    userRoles.includes('agent') ||
    (userRoles.includes('patient') && appointment.patientId === userId) ||
    (userRoles.includes('doctor') && appointment.doctorId === userId);
  if (!canAccess) {
    throw new AppError('Unauthorized to access this appointment', 403);
  }

  res.json({ appointment });
}));

// Update an appointment
router.put('/:id', opalAuthMiddleware, requireAppointmentWrite, asyncHandler(async (req: Request, res: Response) => {
  const appointmentId = req.params.id;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  const validatedData = updateAppointmentSchema.parse(req.body);
  const [existingAppointment] = await db
    .select()
    .from(appointments)
    .where(eq(appointments.id, appointmentId))
    .limit(1);

  if (!existingAppointment) {
    throw new AppError('Appointment not found', 404);
  }

  // Check authorization
  const canUpdate =
    userRoles.includes('admin') ||
    userRoles.includes('agent') ||
    (userRoles.includes('patient') && existingAppointment.patientId === userId) ||
    (userRoles.includes('doctor') && existingAppointment.doctorId === userId);
  if (!canUpdate) {
    throw new AppError('Unauthorized to update this appointment', 403);
  }

  // If rescheduling, check for conflicts
  if (validatedData.scheduledAt) {
    const newDate = new Date(validatedData.scheduledAt);
    const duration = validatedData.duration || existingAppointment.duration;
    const endTime = new Date(newDate.getTime() + duration * 60000);

    const conflictingAppointments = await db
      .select()
      .from(appointments)
      .where(and(
        eq(appointments.doctorId, existingAppointment.doctorId),
        gte(appointments.scheduledAt, newDate),
        lte(appointments.scheduledAt, endTime),
        eq(appointments.status, 'scheduled')
      ));

    if (conflictingAppointments.length > 0 && conflictingAppointments[0].id !== appointmentId) {
      throw new AppError('Doctor is not available at the requested time', 409);
    }
  }

  const [updatedAppointment] = await db
    .update(appointments)
    .set({
      ...validatedData,
      scheduledAt: validatedData.scheduledAt ? new Date(validatedData.scheduledAt) : undefined,
      updatedAt: new Date(),
    })
    .where(eq(appointments.id, appointmentId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'APPOINTMENT_UPDATED',
    'appointments',
    {
      appointmentId: updatedAppointment.id,
      updatedFields: Object.keys(validatedData),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Appointment updated successfully',
    appointment: updatedAppointment,
  });
}));

// Cancel an appointment
router.post('/:id/cancel', opalAuthMiddleware, requireResourceAccess('appointments', 'cancel'), asyncHandler(async (req: Request, res: Response) => {
  const appointmentId = req.params.id;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  const { reason } = req.body;
  const [existingAppointment] = await db
    .select()
    .from(appointments)
    .where(eq(appointments.id, appointmentId))
    .limit(1);

  if (!existingAppointment) {
    throw new AppError('Appointment not found', 404);
  }

  // Check authorization
  const canCancel =
    userRoles.includes('admin') ||
    userRoles.includes('agent') ||
    (userRoles.includes('patient') && existingAppointment.patientId === userId) ||
    (userRoles.includes('doctor') && existingAppointment.doctorId === userId);
  if (!canCancel) {
    throw new AppError('Unauthorized to cancel this appointment', 403);
  }

  if (existingAppointment.status === 'cancelled') {
    throw new AppError('Appointment is already cancelled', 400);
  }

  const [updatedAppointment] = await db
    .update(appointments)
    .set({
      status: 'cancelled',
      notes: reason ? `${existingAppointment.notes || ''}\nCancellation reason: ${reason}` : existingAppointment.notes,
      updatedAt: new Date(),
    })
    .where(eq(appointments.id, appointmentId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'APPOINTMENT_CANCELLED',
    'appointments',
    {
      appointmentId: updatedAppointment.id,
      reason,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Appointment cancelled successfully',
    appointment: updatedAppointment,
  });
}));

// Get available time slots for a doctor
router.get('/doctors/:doctorId/availability', opalAuthMiddleware, asyncHandler(async (req, res) => {
  const doctorId = req.params.doctorId;
  const { date, duration = 30 } = req.query;

  if (!date || typeof date !== 'string') {
    throw new AppError('Date parameter is required', 400);
  }

  // Verify doctor exists
  const [doctor] = await db
    .select()
    .from(users)
    .where(and(
      eq(users.id, doctorId),
      eq(users.isActive, true)
    ))
    .limit(1);

  if (!doctor) {
    throw new AppError('Doctor not found or inactive', 404);
  }

  const targetDate = new Date(date);
  const startOfDay = new Date(targetDate);
  startOfDay.setHours(9, 0, 0, 0); // 9 AM
  const endOfDay = new Date(targetDate);
  endOfDay.setHours(17, 0, 0, 0); // 5 PM

  // Get existing appointments for the day
  const existingAppointments = await db
    .select()
    .from(appointments)
    .where(and(
      eq(appointments.doctorId, doctorId),
      gte(appointments.scheduledAt, startOfDay),
      lte(appointments.scheduledAt, endOfDay),
      eq(appointments.status, 'scheduled')
    ))
    .orderBy(appointments.scheduledAt);

  // Generate available slots
  const slots = [];
  const slotDuration = parseInt(duration as string);
  
  for (let hour = 9; hour < 17; hour++) {
    for (let minute = 0; minute < 60; minute += slotDuration) {
      const slotStart = new Date(targetDate);
      slotStart.setHours(hour, minute, 0, 0);
      
      const slotEnd = new Date(slotStart.getTime() + slotDuration * 60000);
      
      // Check if slot conflicts with existing appointments
      const hasConflict = existingAppointments.some(apt => {
        const aptStart = new Date(apt.scheduledAt);
        const aptEnd = new Date(aptStart.getTime() + apt.duration * 60000);
        
        return (slotStart < aptEnd && slotEnd > aptStart);
      });

      if (!hasConflict && slotEnd <= endOfDay) {
        slots.push({
          startTime: slotStart.toISOString(),
          endTime: slotEnd.toISOString(),
          duration: slotDuration,
        });
      }
    }
  }

  res.json({
    doctorId,
    date: targetDate.toISOString().split('T')[0],
    availableSlots: slots,
  });
}));

export { router as appointmentsRoutes };
