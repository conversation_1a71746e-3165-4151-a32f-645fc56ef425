import { Router, Request, Response } from 'express';
import multer from 'multer';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { db, sql } from '../db/index';
import {
  doctorCredentials,
  doctorProfileUrls,
  credentialDocuments,
  credentialStatusEnum,
  credentialTypeEnum,
  verificationMethodEnum
} from '../db/schema/doctor-credentials';
import { users } from '../db/schema/users';
import { eq, and, desc } from 'drizzle-orm';
import { opalAuthMiddleware, requireResourceAccess } from '../middleware/opalAuth';
import { AppError, asyncHandler } from '../middleware/errorHandler';
import { logAuditEvent } from '../utils/logger';
import { storageService } from '../services/storageService';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Configure multer for memory storage (integrating with existing storage service)
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allowed file types for credential documents
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new AppError('Invalid file type. Only PDF, images, Word docs, and text files are allowed.', 400));
    }
  },
});

// Validation schemas
const createCredentialSchema = z.object({
  credentialType: z.enum(credentialTypeEnum.enumValues),
  credentialNumber: z.string().min(1, 'Credential number is required'),
  issuingAuthority: z.string().min(1, 'Issuing authority is required'),
  issuedDate: z.string().transform((str) => new Date(str)),
  expirationDate: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  notes: z.string().optional(),
  metadata: z.string().optional(),
});

const updateCredentialSchema = createCredentialSchema.partial();

const createProfileUrlSchema = z.object({
  urlType: z.string().min(1, 'URL type is required'),
  url: z.string().url('Invalid URL format'),
  displayName: z.string().optional(),
  sortOrder: z.number().optional().default(0),
});

const updateProfileUrlSchema = createProfileUrlSchema.partial();

// Get all credentials for a doctor
router.get('/credentials', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  // Doctors can only see their own credentials, admins can see all
  const doctorId = userRole === 'admin' ? (req.query.doctorId as string) || userId : userId;
  
  try {
    const credentials = await db
      .select()
      .from(doctorCredentials)
      .where(eq(doctorCredentials.doctorId, doctorId))
      .orderBy(desc(doctorCredentials.createdAt));

    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_CREDENTIALS_ACCESSED',
      'doctor_credentials',
      {
        doctorId,
        credentialCount: credentials.length,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      credentials,
      count: credentials.length
    });
  } catch (error) {
    logger.error('Error fetching doctor credentials:', error);
    throw new AppError('Failed to fetch credentials', 500);
  }
}));

// Create a new credential
router.post('/credentials', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'create'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  const validatedData = createCredentialSchema.parse(req.body);
  
  // Doctors can only create credentials for themselves, admins can create for any doctor
  const doctorId = userRole === 'admin' ? (req.body.doctorId || userId) : userId;
  
  try {
    const [newCredential] = await db
      .insert(doctorCredentials)
      .values({
        doctorId,
        ...validatedData,
        status: 'pending',
        verificationMethod: 'manual',
      })
      .returning();

    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_CREDENTIAL_CREATED',
      'doctor_credentials',
      {
        credentialId: newCredential.id,
        doctorId,
        credentialType: newCredential.credentialType,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.status(201).json({
      message: 'Credential created successfully',
      credential: newCredential,
    });
  } catch (error) {
    logger.error('Error creating credential:', error);
    throw new AppError('Failed to create credential', 500);
  }
}));

// Update a credential
router.put('/credentials/:credentialId', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'update'), asyncHandler(async (req: Request, res: Response) => {
  const { credentialId } = req.params;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  const validatedData = updateCredentialSchema.parse(req.body);
  
  try {
    // Check if credential exists and user has permission
    const [existingCredential] = await db
      .select()
      .from(doctorCredentials)
      .where(eq(doctorCredentials.id, credentialId))
      .limit(1);

    if (!existingCredential) {
      throw new AppError('Credential not found', 404);
    }

    // Only the doctor who owns the credential or admin can update it
    if (userRole !== 'admin' && existingCredential.doctorId !== userId) {
      throw new AppError('Access denied', 403);
    }

    // If a doctor (not admin) is updating their credential, revert status to pending
    const updateData: any = {
      ...validatedData,
      updatedAt: new Date(),
    };

    // Revert to pending status if doctor is making changes to verified credentials
    if (userRole !== 'admin' && existingCredential.status === 'verified') {
      updateData.status = 'pending';
      updateData.verifiedBy = null;
      updateData.verifiedAt = null;
      
      // Log the status reversion
      logAuditEvent(
        userId,
        'CREDENTIAL_STATUS_REVERTED_TO_PENDING',
        'doctor_credentials',
        {
          credentialId,
          doctorId: existingCredential.doctorId,
          previousStatus: existingCredential.status,
          reason: 'Doctor made changes to verified credential',
          changes: validatedData,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );
    }

    const [updatedCredential] = await db
      .update(doctorCredentials)
      .set(updateData)
      .where(eq(doctorCredentials.id, credentialId))
      .returning();

    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_CREDENTIAL_UPDATED',
      'doctor_credentials',
      {
        credentialId,
        doctorId: existingCredential.doctorId,
        changes: validatedData,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      message: 'Credential updated successfully',
      credential: updatedCredential,
    });
  } catch (error) {
    logger.error('Error updating credential:', error);
    throw new AppError('Failed to update credential', 500);
  }
}));

// Upload credential document
router.post('/credentials/:credentialId/documents', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'create'), upload.single('file'), asyncHandler(async (req: Request, res: Response) => {
  const { credentialId } = req.params;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  if (!req.file) {
    throw new AppError('No file uploaded', 400);
  }

  const { title, description } = req.body;
  
  if (!title) {
    throw new AppError('Document title is required', 400);
  }

  try {
    // Check if credential exists and user has permission
    const [credential] = await db
      .select()
      .from(doctorCredentials)
      .where(eq(doctorCredentials.id, credentialId))
      .limit(1);

    if (!credential) {
      throw new AppError('Credential not found', 404);
    }

    // Only the doctor who owns the credential or admin can upload documents
    if (userRole !== 'admin' && credential.doctorId !== userId) {
      throw new AppError('Access denied', 403);
    }

    // Upload file using existing storage service
    const fileData = await storageService.uploadFile({
      buffer: req.file.buffer,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
    }, userId);

    // Create document record
    const [newDocument] = await db
      .insert(credentialDocuments)
      .values({
        credentialId,
        title,
        description: description || '',
        fileName: fileData.fileName,
        originalFileName: fileData.originalFileName,
        filePath: fileData.filePath,
        fileSize: fileData.fileSize,
        mimeType: fileData.mimeType,
        uploadedBy: userId,
      })
      .returning();

    // Log audit event
    logAuditEvent(
      userId,
      'CREDENTIAL_DOCUMENT_UPLOADED',
      'doctor_credentials',
      {
        documentId: newDocument.id,
        credentialId,
        doctorId: credential.doctorId,
        fileName: fileData.originalFileName,
        fileSize: fileData.fileSize,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.status(201).json({
      message: 'Document uploaded successfully',
      document: newDocument,
    });
  } catch (error) {
    logger.error('Error uploading credential document:', error);
    throw new AppError('Failed to upload document', 500);
  }
}));

// Get credential documents
router.get('/credentials/:credentialId/documents', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const { credentialId } = req.params;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  try {
    // Check if credential exists and user has permission
    const [credential] = await db
      .select()
      .from(doctorCredentials)
      .where(eq(doctorCredentials.id, credentialId))
      .limit(1);

    if (!credential) {
      throw new AppError('Credential not found', 404);
    }

    // Only the doctor who owns the credential or admin can view documents
    if (userRole !== 'admin' && credential.doctorId !== userId) {
      throw new AppError('Access denied', 403);
    }

    const documents = await db
      .select()
      .from(credentialDocuments)
      .where(and(
        eq(credentialDocuments.credentialId, credentialId),
        eq(credentialDocuments.isDeleted, false)
      ))
      .orderBy(desc(credentialDocuments.createdAt));

    // Log audit event
    logAuditEvent(
      userId,
      'CREDENTIAL_DOCUMENTS_ACCESSED',
      'doctor_credentials',
      {
        credentialId,
        doctorId: credential.doctorId,
        documentCount: documents.length,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      documents,
      count: documents.length
    });
  } catch (error) {
    logger.error('Error fetching credential documents:', error);
    throw new AppError('Failed to fetch documents', 500);
  }
}));

// Stream credential document
router.get('/credentials/:credentialId/documents/:documentId/stream', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const { credentialId, documentId } = req.params;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  try {
    // Check if credential exists and user has permission
    const [credential] = await db
      .select()
      .from(doctorCredentials)
      .where(eq(doctorCredentials.id, credentialId))
      .limit(1);

    if (!credential) {
      throw new AppError('Credential not found', 404);
    }

    // Only the doctor who owns the credential or admin can view documents
    if (userRole !== 'admin' && credential.doctorId !== userId) {
      throw new AppError('Access denied', 403);
    }

    // Get document info
    const [document] = await db
      .select()
      .from(credentialDocuments)
      .where(and(
        eq(credentialDocuments.id, documentId),
        eq(credentialDocuments.credentialId, credentialId),
        eq(credentialDocuments.isDeleted, false)
      ))
      .limit(1);

    if (!document) {
      throw new AppError('Document not found', 404);
    }

    // Stream the file using storage service
    const fileStream = await storageService.getFile(document.filePath);
    
    // Set appropriate headers
    res.setHeader('Content-Type', document.mimeType);
    res.setHeader('Content-Length', document.fileSize);
    res.setHeader('Content-Disposition', `inline; filename="${document.originalFileName}"`);
    res.setHeader('Cache-Control', 'private, max-age=3600');
    
    // Log audit event
    logAuditEvent(
      userId,
      'CREDENTIAL_DOCUMENT_ACCESSED',
      'doctor_credentials',
      {
        documentId,
        credentialId,
        doctorId: credential.doctorId,
        fileName: document.originalFileName,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    // Pipe the file stream to response
    fileStream.pipe(res);
  } catch (error) {
    logger.error('Error streaming credential document:', error);
    throw new AppError('Failed to stream document', 500);
  }
}));

// Get profile URLs for a doctor
router.get('/profile-urls', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  // Doctors can only see their own profile URLs, admins can see all
  const doctorId = userRole === 'admin' ? (req.query.doctorId as string) || userId : userId;
  
  try {
    const profileUrls = await db
      .select()
      .from(doctorProfileUrls)
      .where(and(
        eq(doctorProfileUrls.doctorId, doctorId),
        eq(doctorProfileUrls.isActive, true)
      ))
      .orderBy(doctorProfileUrls.sortOrder, doctorProfileUrls.createdAt);

    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_PROFILE_URLS_ACCESSED',
      'doctor_credentials',
      {
        doctorId,
        urlCount: profileUrls.length,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      profileUrls,
      count: profileUrls.length
    });
  } catch (error) {
    logger.error('Error fetching profile URLs:', error);
    throw new AppError('Failed to fetch profile URLs', 500);
  }
}));

// Create a new profile URL
router.post('/profile-urls', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'create'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  const validatedData = createProfileUrlSchema.parse(req.body);
  
  // Doctors can only create profile URLs for themselves, admins can create for any doctor
  const doctorId = userRole === 'admin' ? (req.body.doctorId || userId) : userId;
  
  try {
    const [newProfileUrl] = await db
      .insert(doctorProfileUrls)
      .values({
        doctorId,
        ...validatedData,
      })
      .returning();

    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_PROFILE_URL_CREATED',
      'doctor_credentials',
      {
        profileUrlId: newProfileUrl.id,
        doctorId,
        urlType: newProfileUrl.urlType,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.status(201).json({
      message: 'Profile URL created successfully',
      profileUrl: newProfileUrl,
    });
  } catch (error) {
    logger.error('Error creating profile URL:', error);
    throw new AppError('Failed to create profile URL', 500);
  }
}));

// Update a profile URL
router.put('/profile-urls/:urlId', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'update'), asyncHandler(async (req: Request, res: Response) => {
  const { urlId } = req.params;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  const validatedData = updateProfileUrlSchema.parse(req.body);
  
  try {
    // Check if profile URL exists and user has permission
    const [existingUrl] = await db
      .select()
      .from(doctorProfileUrls)
      .where(eq(doctorProfileUrls.id, urlId))
      .limit(1);

    if (!existingUrl) {
      throw new AppError('Profile URL not found', 404);
    }

    // Only the doctor who owns the profile URL or admin can update it
    if (userRole !== 'admin' && existingUrl.doctorId !== userId) {
      throw new AppError('Access denied', 403);
    }

    const [updatedUrl] = await db
      .update(doctorProfileUrls)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(doctorProfileUrls.id, urlId))
      .returning();

    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_PROFILE_URL_UPDATED',
      'doctor_credentials',
      {
        profileUrlId: urlId,
        doctorId: existingUrl.doctorId,
        changes: validatedData,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      message: 'Profile URL updated successfully',
      profileUrl: updatedUrl,
    });
  } catch (error) {
    logger.error('Error updating profile URL:', error);
    throw new AppError('Failed to update profile URL', 500);
  }
}));

// Delete a profile URL
router.delete('/profile-urls/:urlId', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'delete'), asyncHandler(async (req: Request, res: Response) => {
  const { urlId } = req.params;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  try {
    // Check if profile URL exists and user has permission
    const [existingUrl] = await db
      .select()
      .from(doctorProfileUrls)
      .where(eq(doctorProfileUrls.id, urlId))
      .limit(1);

    if (!existingUrl) {
      throw new AppError('Profile URL not found', 404);
    }

    // Only the doctor who owns the profile URL or admin can delete it
    if (userRole !== 'admin' && existingUrl.doctorId !== userId) {
      throw new AppError('Access denied', 403);
    }

    // Soft delete by setting isActive to false
    await db
      .update(doctorProfileUrls)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(doctorProfileUrls.id, urlId));

    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_PROFILE_URL_DELETED',
      'doctor_credentials',
      {
        profileUrlId: urlId,
        doctorId: existingUrl.doctorId,
        urlType: existingUrl.urlType,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      message: 'Profile URL deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting profile URL:', error);
    throw new AppError('Failed to delete profile URL', 500);
  }
}));

// Admin endpoints for credential verification
// Get all pending credentials for admin review
router.get('/admin/credentials/pending', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'manage'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  // Only admins can access this endpoint
  if (userRole !== 'admin') {
    throw new AppError('Admin access required', 403);
  }
  
  try {
    const pendingCredentials = await db
      .select({
        id: doctorCredentials.id,
        doctorId: doctorCredentials.doctorId,
        credentialType: doctorCredentials.credentialType,
        credentialNumber: doctorCredentials.credentialNumber,
        issuingAuthority: doctorCredentials.issuingAuthority,
        issuedDate: doctorCredentials.issuedDate,
        expirationDate: doctorCredentials.expirationDate,
        status: doctorCredentials.status,
        verificationMethod: doctorCredentials.verificationMethod,
        notes: doctorCredentials.notes,
        metadata: doctorCredentials.metadata,
        createdAt: doctorCredentials.createdAt,
        updatedAt: doctorCredentials.updatedAt,
      })
      .from(doctorCredentials)
      .where(eq(doctorCredentials.status, 'pending'))
      .orderBy(desc(doctorCredentials.createdAt));

    // Get doctor information for each credential
    const credentialsWithDoctors = await Promise.all(
      pendingCredentials.map(async (credential) => {
        const [doctor] = await db
          .select({
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
          })
          .from(users)
          .where(eq(users.id, credential.doctorId))
          .limit(1);

        return {
          ...credential,
          doctor: doctor || null,
        };
      })
    );

    // Log audit event
    logAuditEvent(
      userId,
      'ADMIN_PENDING_CREDENTIALS_ACCESSED',
      'doctor_credentials',
      {
        credentialCount: pendingCredentials.length,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      credentials: credentialsWithDoctors,
      count: credentialsWithDoctors.length
    });
  } catch (error) {
    logger.error('Error fetching pending credentials:', error);
    throw new AppError('Failed to fetch pending credentials', 500);
  }
}));

// Verify/approve a credential
router.put('/admin/credentials/:credentialId/verify', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'manage'), asyncHandler(async (req: Request, res: Response) => {
  const { credentialId } = req.params;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  // Only admins can verify credentials
  if (userRole !== 'admin') {
    throw new AppError('Admin access required', 403);
  }

  const { status, notes } = req.body;
  
  // Validate status
  const validStatuses = ['verified', 'under_review', 'revoked'];
  if (!validStatuses.includes(status)) {
    throw new AppError('Invalid status. Must be one of: verified, under_review, revoked', 400);
  }

  try {
    // Check if credential exists
    const [existingCredential] = await db
      .select()
      .from(doctorCredentials)
      .where(eq(doctorCredentials.id, credentialId))
      .limit(1);

    if (!existingCredential) {
      throw new AppError('Credential not found', 404);
    }

    // Update credential status
    const [updatedCredential] = await db
      .update(doctorCredentials)
      .set({
        status: status as any,
        verifiedBy: userId,
        verifiedAt: new Date(),
        notes: notes || existingCredential.notes,
        updatedAt: new Date(),
      })
      .where(eq(doctorCredentials.id, credentialId))
      .returning();

    // Log audit event
    logAuditEvent(
      userId,
      'ADMIN_CREDENTIAL_VERIFIED',
      'doctor_credentials',
      {
        credentialId,
        doctorId: existingCredential.doctorId,
        oldStatus: existingCredential.status,
        newStatus: status,
        notes,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      message: 'Credential status updated successfully',
      credential: updatedCredential,
    });
  } catch (error) {
    logger.error('Error verifying credential:', error);
    throw new AppError('Failed to verify credential', 500);
  }
}));

// Get all credentials for admin overview
router.get('/admin/credentials', opalAuthMiddleware, requireResourceAccess('doctor_credentials', 'manage'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  // Only admins can access this endpoint
  if (userRole !== 'admin') {
    throw new AppError('Admin access required', 403);
  }

  const { status, doctorId, page = 1, limit = 50 } = req.query;
  const offset = (Number(page) - 1) * Number(limit);
  
  try {
    // Build where conditions
    const whereConditions = [];
    if (status) {
      whereConditions.push(eq(doctorCredentials.status, status as any));
    }
    if (doctorId) {
      whereConditions.push(eq(doctorCredentials.doctorId, doctorId as string));
    }

    let credentials;
    if (whereConditions.length > 0) {
      credentials = await db
        .select({
          id: doctorCredentials.id,
          doctorId: doctorCredentials.doctorId,
          credentialType: doctorCredentials.credentialType,
          credentialNumber: doctorCredentials.credentialNumber,
          issuingAuthority: doctorCredentials.issuingAuthority,
          issuedDate: doctorCredentials.issuedDate,
          expirationDate: doctorCredentials.expirationDate,
          status: doctorCredentials.status,
          verificationMethod: doctorCredentials.verificationMethod,
          verifiedBy: doctorCredentials.verifiedBy,
          verifiedAt: doctorCredentials.verifiedAt,
          notes: doctorCredentials.notes,
          createdAt: doctorCredentials.createdAt,
          updatedAt: doctorCredentials.updatedAt,
        })
        .from(doctorCredentials)
        .where(and(...whereConditions))
        .orderBy(desc(doctorCredentials.createdAt))
        .limit(Number(limit))
        .offset(offset);
    } else {
      credentials = await db
        .select({
          id: doctorCredentials.id,
          doctorId: doctorCredentials.doctorId,
          credentialType: doctorCredentials.credentialType,
          credentialNumber: doctorCredentials.credentialNumber,
          issuingAuthority: doctorCredentials.issuingAuthority,
          issuedDate: doctorCredentials.issuedDate,
          expirationDate: doctorCredentials.expirationDate,
          status: doctorCredentials.status,
          verificationMethod: doctorCredentials.verificationMethod,
          verifiedBy: doctorCredentials.verifiedBy,
          verifiedAt: doctorCredentials.verifiedAt,
          notes: doctorCredentials.notes,
          createdAt: doctorCredentials.createdAt,
          updatedAt: doctorCredentials.updatedAt,
        })
        .from(doctorCredentials)
        .orderBy(desc(doctorCredentials.createdAt))
        .limit(Number(limit))
        .offset(offset);
    }

    // Get doctor information for each credential
    const credentialsWithDoctors = await Promise.all(
      credentials.map(async (credential) => {
        const [doctor] = await db
          .select({
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
          })
          .from(users)
          .where(eq(users.id, credential.doctorId))
          .limit(1);

        let verifiedByUser = null;
        if (credential.verifiedBy) {
          const [verifier] = await db
            .select({
              id: users.id,
              firstName: users.firstName,
              lastName: users.lastName,
              email: users.email,
            })
            .from(users)
            .where(eq(users.id, credential.verifiedBy))
            .limit(1);
          verifiedByUser = verifier || null;
        }

        return {
          ...credential,
          doctor: doctor || null,
          verifiedByUser,
        };
      })
    );

    // Get total count for pagination
    let totalCount;
    if (whereConditions.length > 0) {
      const countResult = await db
        .select()
        .from(doctorCredentials)
        .where(and(...whereConditions));
      totalCount = countResult.length;
    } else {
      const countResult = await db
        .select()
        .from(doctorCredentials);
      totalCount = countResult.length;
    }

    // Log audit event
    logAuditEvent(
      userId,
      'ADMIN_CREDENTIALS_ACCESSED',
      'doctor_credentials',
      {
        filters: { status, doctorId },
        page: Number(page),
        limit: Number(limit),
        totalCount: totalCount,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      credentials: credentialsWithDoctors,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / Number(limit)),
      }
    });
  } catch (error) {
    logger.error('Error fetching admin credentials:', error);
    throw new AppError('Failed to fetch credentials', 500);
  }
}));

export { router as doctorCredentialsRoutes };