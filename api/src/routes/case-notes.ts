import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { caseNotes } from '../db/schema/case-notes.js';
import { cases } from '../db/schema/cases.js';
import { eq, and, desc } from 'drizzle-orm';
import { opalAuthMiddleware, requireResourceAccess } from '../middleware/opalAuth.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { CaseDoctorService } from '../services/caseDoctorService.js';
import { requireDoctorAcceptance } from '../middleware/doctorAcceptanceMiddleware.js';

const router = Router();

// Validation schemas
const createNoteSchema = z.object({
  noteType: z.enum(['clinical_notes', 'second_opinion', 'consultation', 'progress_note', 'discharge_summary']).default('clinical_notes'),
  structuredContent: z.record(z.any()).default({}),
  // rawContent field removed
});

const updateNoteSchema = z.object({
  structuredContent: z.record(z.any()).optional(),
  // rawContent field removed
});

// Get all notes for a case
router.get('/:caseId/notes', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.caseId;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  const userRole = userRoles && userRoles.length > 0 ? userRoles[0] : 'patient';
  const noteType = req.query.type;

  // Check if case exists  // Get case data to check permissions
  const [caseData] = await db
    .select({
      id: cases.id,
      patientId: cases.patientId,
    })
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  let canAccess = 
    userRoles.includes('admin') ||
    userRoles.includes('agent') ||
    (userRoles.includes('patient') && caseData.patientId === userId);
    
  // For doctors, check using multi-doctor assignment system
  if (userRoles.includes('doctor') && !canAccess) {
    canAccess = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
  }

  if (!canAccess) {
    throw new AppError('Unauthorized to access case notes', 403);
  }

  // Build query conditions
  const whereConditions = [
    eq(caseNotes.caseId, caseId),
    eq(caseNotes.isActive, true)
  ];

  // Add note type filter if specified
  if (noteType) {
    // For now, skip note type filtering until we have proper note type mapping
    // whereConditions.push(eq(caseNotes.noteTypeId, noteType as any));
  }

  // Execute query with all conditions
  const notes = await db
    .select()
    .from(caseNotes)
    .where(and(...whereConditions))
    .orderBy(desc(caseNotes.createdAt));

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_NOTES_ACCESSED',
    'case_notes',
    {
      caseId,
      noteType: noteType || 'all',
      notesCount: notes.length,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  // Simplify the notes format for consistency
  const simplifiedNotes = notes.map(note => {
    const structured = note.structuredContent as Record<string, any> || {};
    
    return {
      ...note,
      // Add simplified content extraction
      content: typeof structured.content === 'string' ? structured.content : '',
      doctorName: 'System User', // Will be populated by joins in future
    };
  });

  res.json({
    notes: simplifiedNotes,
    total: notes.length,
  });
}));

// Create a new note for a case
router.post('/:caseId/notes', opalAuthMiddleware, requireResourceAccess('case-notes', 'create'), requireDoctorAcceptance, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.caseId;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  
  // Only doctors can create notes
  if (!userRoles.includes('doctor') && !userRoles.includes('admin')) {
    throw new AppError('Only doctors can create clinical notes', 403);
  }

  const validatedData = createNoteSchema.parse(req.body);

  // Check if case exists and doctor has access
  const [caseData] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check if doctor is assigned to case using case_doctors table
  let isDoctorAssigned = false;
  if (userRoles.includes('doctor')) {
    isDoctorAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
  }
  
  // Check if doctor is assigned to case (or is admin)
  if (userRoles.includes('doctor') && !isDoctorAssigned) {
    throw new AppError('Only assigned doctors can create notes for this case', 403);
  }

  // Use the same clinical notes type ID as in other parts of the system
  const clinicalNotesTypeId = 'c6867d50-6dc7-443d-aa14-061ab153d685'; // Clinical Notes type
  
  const [newNote] = await db
    .insert(caseNotes)
    .values({
      caseId,
      doctorId: userId,
      noteTypeId: clinicalNotesTypeId, // Use noteTypeId instead of noteType
      structuredContent: validatedData.structuredContent,
      version: 1,
      isActive: true,
      // Initialize collaborative editing fields
      yjsDocument: null, // Will be populated when collaborative editing starts
      canvasBlocks: [],
      medicalCodes: {},
      activeEditors: [],
      documentVersion: 1,
      collaborationEnabled: true,
      // rawContent field removed
    })
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_NOTE_CREATED',
    'case_notes',
    {
      caseId,
      noteId: newNote.id,
      noteType: newNote.noteTypeId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.status(201).json({
    message: 'Note created successfully',
    note: newNote,
  });
}));

// Update a specific note
router.put('/:caseId/notes/:noteId', opalAuthMiddleware, requireDoctorAcceptance, asyncHandler(async (req: Request, res: Response) => {
  const noteId = req.params.noteId;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;

  const validatedData = updateNoteSchema.parse(req.body);

  // Get existing note
  const [existingNote] = await db
    .select()
    .from(caseNotes)
    .where(eq(caseNotes.id, noteId))
    .limit(1);

  if (!existingNote) {
    throw new AppError('Note not found', 404);
  }

  // Only the note creator or admin can update
  if (!userRoles.includes('admin') && existingNote.doctorId !== userId) {
    throw new AppError('Only the note creator can update this note', 403);
  }

  const [updatedNote] = await db
    .update(caseNotes)
    .set({
      ...validatedData,
      version: existingNote.version + 1,
      updatedAt: new Date(),
    })
    .where(eq(caseNotes.id, noteId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_NOTE_UPDATED',
    'case_notes',
    {
      noteId: updatedNote.id,
      caseId: updatedNote.caseId,
      version: updatedNote.version,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Note updated successfully',
    note: updatedNote,
  });
}));

// Get a specific note by ID
router.get('/:caseId/notes/:noteId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const noteId = req.params.noteId;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;

  const [note] = await db
    .select()
    .from(caseNotes)
    .where(eq(caseNotes.id, noteId))
    .limit(1);

  if (!note) {
    throw new AppError('Note not found', 404);
  }

  // Check case access permissions
  const [caseData] = await db
    .select({
      patientId: cases.patientId,
    })
    .from(cases)
    .where(eq(cases.id, note.caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Associated case not found', 404);
  }

  const canAccess =
    userRoles.includes('admin') ||
    userRoles.includes('agent') ||
    (userRoles.includes('patient') && caseData.patientId === userId) ||
    (userRoles.includes('doctor') && await CaseDoctorService.isDoctorAssignedToCase(userId, note.caseId));

  if (!canAccess) {
    throw new AppError('Unauthorized to access this note', 403);
  }

  res.json({
    note,
  });
}));

export { router as caseNotesRoutes };
