import { Router } from 'express';
import { db, sql } from '../db/index.js';
import { logger } from '../utils/logger.js';

const router = Router();

// Health check endpoint
router.get('/', async (req, res) => {
  let dbStatus = 'healthy';
  let dbError: string | undefined;

  // Database check
  try {
    await sql`SELECT 1`;
    dbStatus = 'healthy';
  } catch (error) {
    logger.error('Database health check failed:', error);
    dbStatus = 'unhealthy';
    dbError = error instanceof Error ? error.message : 'Unknown error';
  }

  try {
    const health = {
      status: dbStatus === 'healthy' ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: {
          status: dbStatus,
          ...(dbError && { error: dbError }),
        },
        memory: {
          used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
          total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
          unit: 'MB',
        },
      },
    };

    res.json(health);
  } catch (error) {
    logger.error('Health check failed:', error);
    
    const health = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: {
          status: 'unhealthy',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      },
    };

    res.status(503).json(health);
  }
});

// Readiness probe
router.get('/ready', async (req, res) => {
  try {
    await sql`SELECT 1`;
    res.json({ status: 'ready' });
  } catch (error) {
    res.status(503).json({ status: 'not ready', error: error instanceof Error ? error.message : 'Unknown error' });
  }
});

// Liveness probe
router.get('/live', (req, res) => {
  res.json({ status: 'alive' });
});

export { router as healthRoutes };
