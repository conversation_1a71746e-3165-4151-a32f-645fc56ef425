import { Router, Request, Response } from 'express';
import { opalAuthMiddleware } from '../middleware/opalAuth';
import { db } from '../db';
import { 
  legalComplianceTemplates, 
  legalComplianceVersions, 
  userLegalAgreements 
} from '../db/schema';
import { eq, desc, and, notInArray } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
const router = Router();

/**
 * Check what consents are required for a user at strategic points
 * Returns list of consents that need to be accepted
 */
router.post('/check-required', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { trigger } = req.body; // 'login', 'case_creation', etc.

    // Get consents that should be required based on user role and trigger
    const requiredConsents = await getRequiredConsents(userRole, trigger);
    
    // Get consents user has already accepted
    const acceptedConsents = await db
      .select({
        templateId: legalComplianceVersions.templateId,
      })
      .from(userLegalAgreements)
      .innerJoin(legalComplianceVersions, eq(userLegalAgreements.documentVersionId, legalComplianceVersions.id))
      .where(eq(userLegalAgreements.userId, userId));

    const acceptedTemplateIds = acceptedConsents.map(c => c.templateId);

    // Filter out already accepted consents
    const pendingConsents = requiredConsents.filter(
      consent => !acceptedTemplateIds.includes(consent.templateId)
    );

    res.json({
      success: true,
      data: {
        pendingConsents,
        hasRequired: pendingConsents.length > 0,
        trigger,
      },
    });
  } catch (error) {
    logger.error('Error checking required consents:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check required consents',
    });
  }
});

/**
 * Get full consent document for display
 */
router.get('/document/:templateId', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { templateId } = req.params;
    
    // Get template
    const [template] = await db
      .select()
      .from(legalComplianceTemplates)
      .where(eq(legalComplianceTemplates.id, templateId))
      .limit(1);

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
      });
    }

    // Get latest version
    const [latestVersion] = await db
      .select()
      .from(legalComplianceVersions)
      .where(eq(legalComplianceVersions.templateId, templateId))
      .orderBy(desc(legalComplianceVersions.version))
      .limit(1);

    if (!latestVersion) {
      return res.status(404).json({
        success: false,
        error: 'No versions found',
      });
    }

    res.json({
      success: true,
      data: {
        template,
        version: latestVersion,
      },
    });
  } catch (error) {
    logger.error('Error fetching consent document:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch document',
    });
  }
});

/**
 * Accept a consent (record in user_legal_agreements)
 */
router.post('/accept', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { templateId } = req.body;
    const userId = req.user.id;
    const ipAddress = req.ip;
    const userAgent = req.get('User-Agent');

    // Get latest version of template
    const [latestVersion] = await db
      .select()
      .from(legalComplianceVersions)
      .where(eq(legalComplianceVersions.templateId, templateId))
      .orderBy(desc(legalComplianceVersions.version))
      .limit(1);

    if (!latestVersion) {
      return res.status(404).json({
        success: false,
        error: 'Template version not found',
      });
    }

    // Record the acceptance
    await db
      .insert(userLegalAgreements)
      .values({
        userId,
        documentVersionId: latestVersion.id,
        ipAddress,
        userAgent,
      });

    res.json({
      success: true,
      message: 'Consent accepted successfully',
    });
  } catch (error) {
    logger.error('Error accepting consent:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to accept consent',
    });
  }
});

/**
 * Helper function to determine required consents based on role and trigger
 */
async function getRequiredConsents(userRole: string, trigger: string) {
  const consents = [];

  // Get all templates for mapping
  const templates = await db
    .select()
    .from(legalComplianceTemplates)
    .where(eq(legalComplianceTemplates.isActive, true));

  const templateMap = new Map(templates.map(t => [t.title, t]));

  // Define consent rules (you can move this to database later)
  const consentRules = [
    {
      title: 'Continuia Patient Terms of Service (TOS)',
      triggers: ['login'],
      roles: ['patient', 'doctor', 'admin'], // All users
      priority: 'high',
    },
    {
      title: 'Physician Onboarding Packet',
      triggers: ['login', 'role_assignment'],
      roles: ['doctor'],
      priority: 'high',
    },
    {
      title: 'Continuia Patient Consent Form',
      triggers: ['case_creation'],
      roles: ['patient'],
      priority: 'high',
    },
  ];

  // Filter rules based on trigger and role
  for (const rule of consentRules) {
    if (rule.triggers.includes(trigger) && rule.roles.includes(userRole)) {
      const template = templateMap.get(rule.title);
      if (template) {
        consents.push({
          templateId: template.id,
          title: template.title,
          description: template.description,
          priority: rule.priority,
        });
      }
    }
  }

  return consents;
}

export default router;
