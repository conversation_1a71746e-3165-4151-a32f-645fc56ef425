import { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';
import { Client } from 'minio';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../db/index.js';
import { medicalDocuments, cases } from '../db/schema/cases.js';
import { caseDoctors } from '../db/schema/case-doctors.js';
import { credentialDocuments, doctorCredentials } from '../db/schema/doctor-credentials.js';
import { users } from '../db/schema/users.js';
import { eq, and, isNotNull, inArray, sql } from 'drizzle-orm';
import { opalAuthMiddleware, requireResourceAccess } from '../middleware/opalAuth.js';
import { requireDoctorAcceptance } from '../middleware/doctorAcceptanceMiddleware.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// MinIO client configuration
const minioClient = new Client({
  endPoint: process.env.MINIO_ENDPOINT || 'minio',
  port: parseInt(process.env.MINIO_PORT || '9000'),
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ROOT_USER || 'minioadmin', // Default development value
  secretKey: process.env.MINIO_ROOT_PASSWORD || 'minioadmin', // Default development value
});

// Log MinIO configuration for debugging
logger.debug('Debug output', undefined, { data: JSON.stringify({
  message: 'MinIO configuration',
  endPoint: process.env.MINIO_ENDPOINT || 'minio',
  port: parseInt(process.env.MINIO_PORT || '9000'),
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ROOT_USER ? '***' : 'default', // Don't log actual credentials
  secretKey: process.env.MINIO_ROOT_PASSWORD ? '***' : 'default' // Don't log actual credentials
}) });

// Ensure buckets exist
const BUCKET_NAME = 'medical-documents';
const PROFILES_BUCKET = 'profiles';

const ensureBucket = async () => {
  try {
    logger.debug('Checking MinIO connection and buckets...');
    
    // Test MinIO connection first
    await minioClient.listBuckets();
    logger.debug('MinIO connection successful');
    
    // Ensure medical documents bucket
    const bucketExists = await minioClient.bucketExists(BUCKET_NAME);
    if (!bucketExists) {
      logger.debug(`Creating bucket: ${BUCKET_NAME}`);
      await minioClient.makeBucket(BUCKET_NAME, 'us-east-1');
      logger.debug(`Bucket created: ${BUCKET_NAME}`);
    } else {
      logger.debug(`Bucket exists: ${BUCKET_NAME}`);
    }
    
    // Ensure profiles bucket
    const profilesBucketExists = await minioClient.bucketExists(PROFILES_BUCKET);
    if (!profilesBucketExists) {
      logger.debug(`Creating bucket: ${PROFILES_BUCKET}`);
      await minioClient.makeBucket(PROFILES_BUCKET, 'us-east-1');
      logger.debug(`Bucket created: ${PROFILES_BUCKET}`);
    } else {
      logger.debug(`Bucket exists: ${PROFILES_BUCKET}`);
    }
    
    logger.debug('MinIO buckets initialized successfully');
  } catch (error) {
    logger.error('Error ensuring MinIO buckets:', error);
    logger.error('MinIO connection details:', new Error({
      endpoint: process.env.MINIO_ENDPOINT || 'minio',
      port: process.env.MINIO_PORT || '9000',
      useSSL: process.env.MINIO_USE_SSL === 'true'
    }));
  }
};

// Initialize bucket on startup with retry
const initializeMinIO = async () => {
  let retries = 5;
  while (retries > 0) {
    try {
      await ensureBucket();
      break;
    } catch (error) {
      retries--;
      logger.debug('Debug output', undefined, { data: `MinIO initialization failed, retrying... (${retries} attempts left)` });
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      }
    }
  }
};

initializeMinIO();

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check if this is a profile photo upload
    if (req.path === '/profile-photo') {
      // Only allow images for profile photos
      const allowedImageTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp'
      ];
      
      if (allowedImageTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new AppError('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed for profile photos.', 400));
      }
    } else {
      // Allowed file types for medical documents
      const allowedTypes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
      ];

      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new AppError('Invalid file type. Only PDF, images, Word docs, and text files are allowed.', 400));
      }
    }
  },
});

// Validation schemas
const uploadMetadataSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  documentType: z.enum(['lab_report', 'imaging', 'prescription', 'medical_history', 'insurance', 'other']).default('other'),
  caseId: z.string().uuid().optional(),
});

// Upload document endpoint
router.post('/upload', opalAuthMiddleware, requireResourceAccess('documents', 'create'), requireDoctorAcceptance, upload.single('file'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  
  if (!req.file) {
    throw new AppError('No file uploaded', 400);
  }

  const metadata = uploadMetadataSchema.parse(req.body);
  
  // Generate unique filename
  const fileExtension = req.file.originalname.split('.').pop();
  const fileName = `${uuidv4()}.${fileExtension}`;
  const filePath = `users/${userId}/${fileName}`;

  try {
    // Upload to MinIO
    await minioClient.putObject(
      BUCKET_NAME,
      filePath,
      req.file.buffer,
      req.file.size,
      {
        'Content-Type': req.file.mimetype,
        'Original-Name': req.file.originalname,
        'Uploaded-By': userId,
        'Upload-Date': new Date().toISOString(),
        'Title': metadata.title,
        'Document-Type': metadata.documentType,
        ...(metadata.caseId && { 'Case-Id': metadata.caseId }),
      }
    );

    // Create database record for ALL uploaded documents (both with and without caseId)
    const [documentRecord] = await db
      .insert(medicalDocuments)
      .values({
        id: uuidv4(),
        title: metadata.title,
        description: metadata.description || '',
        documentType: metadata.documentType,
        caseId: metadata.caseId || null, // Allow null for standalone documents
        uploadedBy: userId,
        filePath: filePath,
        fileName: fileName,
        originalFileName: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
      })
      .returning();

    // Log audit event
    logAuditEvent(
      userId,
      metadata.caseId ? 'MEDICAL_DOCUMENT_UPLOADED' : 'FILE_UPLOADED',
      'storage',
      {
        ...(documentRecord && { documentId: documentRecord.id }),
        fileName: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        storagePath: filePath,
        caseId: metadata.caseId,
        uploadType: metadata.caseId ? 'medical_document' : 'general_file',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.status(201).json({
      message: 'Document uploaded successfully',
      document: documentRecord,
    });
  } catch (error) {
    logger.error('Storage upload error:', error);
    throw new AppError('Failed to upload file to storage', 500);
  }
}));

// Profile photo upload endpoint
router.post('/profile-photo', opalAuthMiddleware, upload.single('photo'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  
  if (!req.file) {
    throw new AppError('No photo uploaded', 400);
  }

  // Get file extension
  const fileExtension = req.file.originalname.split('.').pop()?.toLowerCase();
  const fileName = `${userId}.${fileExtension}`; // Use user UUID as filename

  try {
    // Delete existing profile photo if it exists
    try {
      const existingFiles = [];
      const stream = minioClient.listObjects(PROFILES_BUCKET, userId, false);
      
      for await (const obj of stream) {
        if (obj.name && obj.name.startsWith(userId)) {
          existingFiles.push(obj.name);
        }
      }
      
      // Delete all existing profile photos for this user
      for (const file of existingFiles) {
        await minioClient.removeObject(PROFILES_BUCKET, file);
      }
    } catch (deleteError) {
      // Ignore errors when deleting existing files (might not exist)
    }

    // Upload new profile photo to profiles bucket
    await minioClient.putObject(
      PROFILES_BUCKET,
      fileName,
      req.file.buffer,
      req.file.size,
      {
        'Content-Type': req.file.mimetype,
        'Original-Name': req.file.originalname,
        'Uploaded-By': userId,
        'Upload-Date': new Date().toISOString(),
        'Photo-Type': 'profile',
      }
    );

    // Log audit event
    logAuditEvent(
      userId,
      'PROFILE_PHOTO_UPLOADED',
      'storage',
      {
        fileName: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        storagePath: fileName,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.status(201).json({
      message: 'Profile photo uploaded successfully',
      photo: {
        fileName,
        originalName: req.file.originalname,
        size: req.file.size,
        mimeType: req.file.mimetype,
        uploadedAt: new Date().toISOString(),
        url: `/api/storage/profile-photo/${userId}`, // URL to access the photo
      },
    });
  } catch (error) {
    logger.error('Profile photo upload error:', error);
    throw new AppError('Failed to upload profile photo', 500);
  }
}));

// Profile photo retrieval endpoint (public access for browser caching with referrer check)
router.get('/profile-photo/:userId', asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  
  // Check referrer for same-origin requests (security measure)
  const referrer = req.get('Referer') || req.get('Referrer');
  const allowedOrigins = ['http://localhost', 'https://localhost', process.env.FRONTEND_URL || 'http://localhost:5173'].filter(Boolean);
  
  if (referrer) {
    const referrerOrigin = new URL(referrer).origin;
    const isAllowedOrigin = allowedOrigins.some(origin => referrerOrigin.startsWith(origin));
    
    if (!isAllowedOrigin) {
      throw new AppError('Access denied - invalid referrer', 403);
    }
  }
  
  // Set browser caching headers for profile images (cache for 1 hour)
  res.set({
    'Cache-Control': 'public, max-age=3600, must-revalidate',
    'ETag': `"profile-${userId}-${Date.now()}"`,
    'Last-Modified': new Date().toUTCString(),
    'Access-Control-Allow-Origin': process.env.FRONTEND_URL || 'http://localhost:5173'
  });

  try {
    // Find profile photo for the user
    let profilePhotoPath = null;
    const listStream = minioClient.listObjects(PROFILES_BUCKET, userId, false);
    
    for await (const obj of listStream) {
      if (obj.name && obj.name.startsWith(userId)) {
        profilePhotoPath = obj.name;
        break;
      }
    }

    if (!profilePhotoPath) {
      throw new AppError('Profile photo not found', 404);
    }

    // Get file info
    const stat = await minioClient.statObject(PROFILES_BUCKET, profilePhotoPath);
    
    // Set response headers
    res.setHeader('Content-Type', stat.metaData['content-type'] || 'image/jpeg');
    res.setHeader('Content-Length', stat.size);
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day

    // Stream the file
    const fileStream = await minioClient.getObject(PROFILES_BUCKET, profilePhotoPath);

    // Log audit event (public access with referrer info)
    logAuditEvent(
      'public',
      'PROFILE_PHOTO_ACCESSED',
      'storage',
      {
        targetUserId: userId,
        filePath: profilePhotoPath,
        fileSize: stat.size,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        referrer: req.get('Referer') || req.get('Referrer') || 'none',
        accessType: 'public'
      }
    );

    // Pipe the stream to response
    fileStream.pipe(res);
  } catch (error) {
    logger.error('MinIO profile photo access error:', error);
    if (error.code === 'NoSuchKey') {
      throw new AppError('Profile photo not found', 404);
    }
    throw new AppError('Failed to access profile photo', 500);
  }
}));

// Helper function to find file in storage
async function findFileInStorage(fileId: string, userId: string, userRole: string): Promise<string | null> {
  try {
    // First, try user's own directory
    const userPrefix = `users/${userId}/`;
    const stream = minioClient.listObjects(BUCKET_NAME, userPrefix, true);
    
    for await (const obj of stream) {
      if (obj.name && obj.name.includes(fileId)) {
        return obj.name;
      }
    }

    // If admin or agent, they can access other users' files
    if (userRole === 'admin' || userRole === 'agent') {
      const allStream = minioClient.listObjects(BUCKET_NAME, 'users/', true);
      
      for await (const obj of allStream) {
        if (obj.name && obj.name.includes(fileId)) {
          return obj.name;
        }
      }
    }

    return null;
  } catch (error) {
    logger.error('Error finding file in storage:', error);
    return null;
  }
}

// Custom authentication middleware for document streaming that supports query parameter tokens
const documentStreamAuthMiddleware = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  let token: string | undefined;

  // Try to get token from Authorization header first (Bearer token)
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7);
  }
  // If no Bearer token, try to get from HTTP-only cookie
  else if (req.cookies && req.cookies.auth_token) {
    token = req.cookies.auth_token;
  }
  // If no cookie, try to get from query parameter (for react-doc-viewer compatibility)
  else if (req.query.token && typeof req.query.token === 'string') {
    token = req.query.token;
  }
  
  if (!token) {
    throw new AppError('Access token required', 401);
  }

  if (!process.env.JWT_SECRET) {
    throw new AppError('JWT_SECRET not configured', 500);
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;
    
    // Get user from database
    const [user] = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
        isActive: users.isActive,
      })
      .from(users)
      .where(eq(users.id, decoded.userId))
      .limit(1);

    if (!user) {
      throw new AppError('User not found', 401);
    }

    if (!user.isActive) {
      throw new AppError('Account is deactivated', 401);
    }

    // Set up user context (similar to opalAuthMiddleware)
    (req as any).user = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      roles: [user.role],
      isActive: user.isActive,
      sessionId: decoded.sessionId,
    };

    // Set up policy context for OPAL evaluation (required by requireResourceAccess)
    (req as any).policyContext = {
      user: {
        id: user.id,
        email: user.email,
        roles: [user.role],
        firstName: user.firstName,
        lastName: user.lastName,
      },
      action: '', // Will be set by requireResourceAccess middleware
      environment: {
        time: new Date(),
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        method: req.method,
      },
      request: {
        body: req.body,
        params: req.params,
        query: req.query,
      }
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new AppError('Invalid token', 401);
    } else if (error instanceof jwt.TokenExpiredError) {
      throw new AppError('Token expired', 401);
    }
    throw error;
  }
});

// Secure document streaming endpoint with HIPAA-compliant access control
router.get('/documents/:documentId/stream', documentStreamAuthMiddleware, requireResourceAccess('documents', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const { documentId } = req.params;
  const userId = (req as any).user.id;
  
  // Extract user role properly from roles array
  const user = (req as any).user;
  const userRoles = user.roles || [];
  const userRole = userRoles[0] || 'patient';

  try {
    // Try to get document from medical documents first
    let document = await db
      .select()
      .from(medicalDocuments)
      .where(eq(medicalDocuments.id, documentId))
      .limit(1);

    let isCredentialDocument = false;
    let credentialDocument = null;

    // If not found in medical documents, try credential documents
    if (document.length === 0) {
      const credentialDocs = await db
        .select({
          id: credentialDocuments.id,
          title: credentialDocuments.title,
          description: credentialDocuments.description,
          originalFileName: credentialDocuments.originalFileName,
          fileName: credentialDocuments.fileName,
          fileSize: credentialDocuments.fileSize,
          mimeType: credentialDocuments.mimeType,
          uploadedBy: credentialDocuments.uploadedBy,
          createdAt: credentialDocuments.createdAt,
          updatedAt: credentialDocuments.updatedAt,
          filePath: credentialDocuments.filePath,
          caseId: sql<string | null>`NULL`.as('caseId'),
          credentialId: credentialDocuments.credentialId,
          documentType: sql<'other'>`'other'`.as('documentType'),
          isDeleted: sql<boolean>`false`.as('isDeleted')
        })
        .from(credentialDocuments)
        .innerJoin(doctorCredentials, eq(credentialDocuments.credentialId, doctorCredentials.id))
        .where(and(
          eq(credentialDocuments.id, documentId),
          eq(credentialDocuments.isDeleted, false)
        ))
        .limit(1);

      if (credentialDocs.length > 0) {
        document = credentialDocs;
        isCredentialDocument = true;
        credentialDocument = credentialDocs[0];
      }
    }

    if (document.length === 0) {
      throw new AppError('Document not found', 404);
    }

    const doc = document[0];

    // STRICT HIPAA-COMPLIANT ACCESS CONTROL
    let hasAccess = false;

    if (isCredentialDocument && credentialDocument) {
      // For credential documents, only the doctor who owns the credential can access
      if (userRole === 'doctor') {
        // Check if this doctor owns the credential
        const [credential] = await db
          .select({ doctorId: doctorCredentials.doctorId })
          .from(doctorCredentials)
          .where(eq(doctorCredentials.id, credentialDocument.credentialId))
          .limit(1);
        
        hasAccess = credential && credential.doctorId === userId;
      } else if (userRole === 'admin') {
        // Admins can access credential documents
        hasAccess = true;
      }
    } else {
      // Regular medical document access control - align with permission matrix
      if (userRole === 'patient') {
        // Patients can access documents they uploaded OR documents for their own cases
        if (doc.caseId) {
          // Check if this document belongs to a case owned by this patient
          const [caseRecord] = await db
            .select({ patientId: cases.patientId })
            .from(cases)
            .where(eq(cases.id, doc.caseId))
            .limit(1);
          
          hasAccess = (caseRecord && caseRecord.patientId === userId) || doc.uploadedBy === userId;
        } else {
          // For standalone documents (no caseId), patients can only access their own
          hasAccess = doc.uploadedBy === userId;
        }
      } else if (userRole === 'doctor') {
        // Doctors can access documents for cases they are assigned to (doctor:assigned)
        // This includes documents uploaded by patients or other doctors for those cases
        if (doc.caseId) {
          // Verify doctor is assigned to the case
          const [doctorAssignment] = await db
            .select({ doctorId: caseDoctors.doctorId })
            .from(caseDoctors)
            .where(and(
              eq(caseDoctors.caseId, doc.caseId),
              eq(caseDoctors.doctorId, userId),
              eq(caseDoctors.isActive, true)
            ))
            .limit(1);
          
          hasAccess = !!doctorAssignment;
        } else {
          // For standalone documents (caseId: null), doctors can only access their own
          hasAccess = doc.uploadedBy === userId;
        }
      } else if (userRole === 'admin' || userRole === 'agent') {
        // Admins and agents can access case-attached documents for investigation
        // For standalone documents, they can access any (admin privilege)
        hasAccess = true;
      }
    }

    if (!hasAccess) {
      throw new AppError('Access denied - insufficient permissions to view this document', 403);
    }

    // Log audit event with security context
    logAuditEvent(
      userId,
      'DOCUMENT_STREAMED',
      'storage',
      {
        documentId: doc.id,
        fileName: doc.originalFileName,
        caseId: doc.caseId,
        securityContext: 'HIPAA_COMPLIANT_STREAM_ACCESS',
        userRole: userRole,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    // Set appropriate headers for the document
    res.setHeader('Content-Type', doc.mimeType);
    res.setHeader('Content-Disposition', `inline; filename="${doc.originalFileName}"`);
    res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Stream the file directly from MinIO through the API
    try {
      const fileStream = await minioClient.getObject(BUCKET_NAME, doc.filePath);
      
      // Handle stream errors
      fileStream.on('error', (streamError) => {
        logger.error('MinIO stream error:', new Error(streamError));
        if (!res.headersSent) {
          res.status(500).json({ error: 'Failed to stream document' });
        }
      });
      
      fileStream.pipe(res);
    } catch (minioError) {
      logger.error('MinIO getObject error:', new Error(minioError));
      if (minioError.code === 'NoSuchKey' || minioError.code === 'NotFound') {
        throw new AppError('Document file not found in storage', 404);
      }
      throw new AppError(`Failed to access document: ${minioError.message}`, 500);
    }

  } catch (error) {
    logger.error('Document streaming error:', error);
    if (error instanceof AppError) {
      throw error;
    }
    if (error.code === 'NoSuchKey' || error.code === 'NotFound') {
      throw new AppError('Document file not found in storage', 404);
    }
    throw new AppError('Failed to stream document', 500);
  }
}));

// Document metadata endpoint with HIPAA-compliant access control
router.get('/documents/:documentId/info', opalAuthMiddleware, requireResourceAccess('documents', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const { documentId } = req.params;
  const userId = (req as any).user.id;
  
  // Extract user role properly from roles array
  const user = (req as any).user;
  const userRoles = user.roles || [];
  const userRole = userRoles[0] || 'patient';

  try {
    // Get document metadata from database
    const [document] = await db
      .select()
      .from(medicalDocuments)
      .where(eq(medicalDocuments.id, documentId))
      .limit(1);

    if (!document) {
      throw new AppError('Document not found', 404);
    }

    // HIPAA-COMPLIANT ACCESS CONTROL (aligned with permission matrix)
    let hasAccess = false;

    if (userRole === 'patient') {
      // Patients can access documents they uploaded OR documents for their own cases
      if (document.caseId) {
        // Check if this document belongs to a case owned by this patient
        const [caseRecord] = await db
          .select({ patientId: cases.patientId })
          .from(cases)
          .where(eq(cases.id, document.caseId))
          .limit(1);
        
        hasAccess = (caseRecord && caseRecord.patientId === userId) || document.uploadedBy === userId;
      } else {
        // For standalone documents (no caseId), patients can only access their own
        hasAccess = document.uploadedBy === userId;
      }
    } else if (userRole === 'doctor') {
      // Doctors can access documents for cases they are assigned to (doctor:assigned)
      // This includes documents uploaded by patients or other doctors for those cases
      if (document.caseId) {
        // Verify doctor is assigned to the case
        const [doctorAssignment] = await db
          .select({ doctorId: caseDoctors.doctorId })
          .from(caseDoctors)
          .where(and(
            eq(caseDoctors.caseId, document.caseId),
            eq(caseDoctors.doctorId, userId),
            eq(caseDoctors.isActive, true)
          ))
          .limit(1);
        
        hasAccess = !!doctorAssignment;
      } else {
        // For standalone documents (caseId: null), doctors can only access their own
        hasAccess = document.uploadedBy === userId;
      }
    } else if (userRole === 'admin' || userRole === 'agent') {
      // Admins and agents can access case-attached documents for investigation
      // For standalone documents, they can access any (admin privilege)
      hasAccess = true;
    }

    if (!hasAccess) {
      throw new AppError('Access denied - insufficient permissions to view this document', 403);
    }

    // Log audit event for HIPAA compliance - document metadata access must be tracked
    logAuditEvent(
      userId,
      'DOCUMENT_PREVIEWED',
      'storage',
      {
        documentId: document.id,
        fileName: document.originalFileName,
        caseId: document.caseId,
        accessType: 'metadata',
        securityContext: 'HIPAA_COMPLIANT_INFO_ACCESS',
        userRole: userRole,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    // Return document metadata for preview UI
    res.json({
      document: {
        id: document.id,
        title: document.title,
        fileName: document.originalFileName,
        mimeType: document.mimeType,
        fileSize: document.fileSize,
        streamUrl: `/api/storage/documents/${document.id}/stream`,
        uploadedAt: document.createdAt
      }
    });
  } catch (error) {
    logger.error('Error retrieving document info:', error);
    throw new AppError('Failed to get document info', 500);
  }
}));

// Document deletion endpoint
router.delete('/documents/:documentId', opalAuthMiddleware, requireResourceAccess('documents', 'delete'), requireDoctorAcceptance, asyncHandler(async (req: Request, res: Response) => {
  const { documentId } = req.params;
  const userId = (req as any).user.id;
  
  // Extract user role properly from roles array
  const user = (req as any).user;
  const userRoles = user.roles || [];
  const userRole = userRoles[0] || 'patient';

  try {
    // Get document metadata from database
    const [document] = await db
      .select()
      .from(medicalDocuments)
      .where(eq(medicalDocuments.id, documentId))
      .limit(1);

    if (!document) {
      throw new AppError('Document not found', 404);
    }

    // Permission check is already done by the middleware
    // For patients, they can only delete documents they uploaded (patient:own)
    // For admins, they can delete any document
    // The middleware already verified this, so we just need to ensure the document exists
    
    // Additional security check: patients can only delete their own documents
    if (userRole === 'patient' && document.uploadedBy !== userId) {
      throw new AppError('Access denied - you can only delete documents you uploaded', 403);
    }

    // Delete from MinIO storage
    await minioClient.removeObject(BUCKET_NAME, document.filePath);

    // Delete from database
    await db
      .delete(medicalDocuments)
      .where(eq(medicalDocuments.id, documentId));

    // Log audit event
    logAuditEvent(
      userId,
      'DOCUMENT_DELETED',
      'storage',
      {
        documentId: document.id,
        fileName: document.originalFileName,
        caseId: document.caseId,
        filePath: document.filePath,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      success: true,
      message: 'Document deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting document:', error);
    if (error.code === 'NoSuchKey') {
      // File doesn't exist in storage, but we should still remove from DB
      await db
        .delete(medicalDocuments)
        .where(eq(medicalDocuments.id, documentId));
      
      res.json({
        success: true,
        message: 'Document deleted successfully (file was already missing from storage)'
      });
    } else {
      throw new AppError('Failed to delete document', 500);
    }
  }
}));

// Note: Removed duplicate /documents route - using the comprehensive one below that gets all documents

// Get user's documents with strict HIPAA-compliant access control
router.get('/documents', opalAuthMiddleware, requireResourceAccess('documents', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const { caseId } = req.query;
  
  // Extract user role properly from roles array
  const user = (req as any).user;
  const userRoles = user.roles || [];
  const userRole = userRoles[0] || 'patient';

  try {
    // Build where conditions with strict HIPAA compliance
    let whereConditions: any = undefined;

    if (caseId && typeof caseId === 'string') {
      // When filtering by caseId, verify user has access to that specific case
      if (userRole === 'patient') {
        // Patients can only see documents from their own cases
        const [caseRecord] = await db
          .select({ patientId: cases.patientId })
          .from(cases)
          .where(eq(cases.id, caseId))
          .limit(1);
        
        if (!caseRecord || caseRecord.patientId !== userId) {
          throw new AppError('Access denied - you do not have permission to view documents for this case', 403);
        }
        
        // Show ALL documents for this specific case (including those uploaded by doctors)
        // This is the patient's own case, so they should see all related documents
        whereConditions = eq(medicalDocuments.caseId, caseId);
      } else if (userRole === 'doctor') {
        // Doctors can see ALL documents for cases they're assigned to
        // First verify doctor is assigned to this case using caseDoctors junction table
        const [doctorAssignment] = await db
          .select({ doctorId: caseDoctors.doctorId })
          .from(caseDoctors)
          .where(and(
            eq(caseDoctors.caseId, caseId),
            eq(caseDoctors.doctorId, userId),
            eq(caseDoctors.isActive, true)
          ))
          .limit(1);
        
        if (!doctorAssignment) {
          throw new AppError('Access denied - you are not assigned to this case', 403);
        }
        
        // Show ALL documents for this case (not just ones uploaded by this doctor)
        whereConditions = eq(medicalDocuments.caseId, caseId);
      } else if (userRole === 'admin' || userRole === 'agent') {
        // Admins/agents can see documents for a specific case only (not all documents)
        // NEVER show documents with caseId: null (these are private patient documents)
        whereConditions = and(
          eq(medicalDocuments.caseId, caseId),
          // Explicitly exclude null caseId documents
          isNotNull(medicalDocuments.caseId)
        );
      }
    } else {
      // No case filtering - STRICT role-based filtering
      // CRITICAL: No one can see "all documents" across patients - this would be a HIPAA violation
      
      if (userRole === 'patient') {
        // Patients can only see their own uploaded documents (including standalone documents)
        whereConditions = eq(medicalDocuments.uploadedBy, userId);
      } else if (userRole === 'doctor') {
        // Doctors can see their own credential documents when no caseId is provided
        // For medical documents, they must specify a caseId to view documents for that specific case
        whereConditions = eq(medicalDocuments.uploadedBy, userId);
      } else if (userRole === 'admin' || userRole === 'agent') {
        // Admins/agents should NOT see all documents without context
        // They must specify a caseId to view documents for that specific case
        throw new AppError('Access denied - must specify a caseId to view documents', 403);
      }
    }

    // Execute query with strict where conditions for medical documents
    const medicalDocs = await db
      .select({
        id: medicalDocuments.id,
        title: medicalDocuments.title,
        description: medicalDocuments.description,
        originalFileName: medicalDocuments.originalFileName,
        fileName: medicalDocuments.fileName,
        fileSize: medicalDocuments.fileSize,
        mimeType: medicalDocuments.mimeType,
        documentType: medicalDocuments.documentType,
        uploadedBy: medicalDocuments.uploadedBy,
        createdAt: medicalDocuments.createdAt,
        updatedAt: medicalDocuments.updatedAt,
        caseId: medicalDocuments.caseId,
        credentialId: sql<string | null>`NULL`.as('credentialId'),
        documentSource: sql<string>`'medical'`.as('documentSource')
      })
      .from(medicalDocuments)
      .where(whereConditions)
      .orderBy(medicalDocuments.createdAt);

    // Also fetch credential documents for the user (only for patients and doctors viewing their own credentials)
    let credentialDocs: any[] = [];
    if (userRole === 'patient' || userRole === 'doctor') {
      // Only show credential documents to the doctor who owns them (no caseId filtering for credentials)
      if (!caseId) {
        credentialDocs = await db
          .select({
            id: credentialDocuments.id,
            title: credentialDocuments.title,
            description: credentialDocuments.description,
            originalFileName: credentialDocuments.originalFileName,
            fileName: credentialDocuments.fileName,
            fileSize: credentialDocuments.fileSize,
            mimeType: credentialDocuments.mimeType,
            documentType: sql<string>`'certificate'`.as('documentType'), // Map to certificate type
            uploadedBy: credentialDocuments.uploadedBy,
            createdAt: credentialDocuments.createdAt,
            updatedAt: credentialDocuments.updatedAt,
            caseId: sql<string | null>`NULL`.as('caseId'),
            credentialId: credentialDocuments.credentialId,
            documentSource: sql<string>`'credential'`.as('documentSource')
          })
          .from(credentialDocuments)
          .innerJoin(doctorCredentials, eq(credentialDocuments.credentialId, doctorCredentials.id))
          .where(and(
            eq(doctorCredentials.doctorId, userId),
            eq(credentialDocuments.isDeleted, false)
          ))
          .orderBy(credentialDocuments.createdAt);
      }
    }

    // Combine both document types
    const documents = [...medicalDocs, ...credentialDocs].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    // Log audit event with security context
    logAuditEvent(
      userId,
      'DOCUMENTS_ACCESSED',
      'storage',
      {
        documentCount: documents.length,
        accessType: caseId ? 'case_documents_list' : 'user_documents_list',
        caseId: caseId || null,
        securityContext: 'HIPAA_COMPLIANT_ACCESS',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      documents,
      count: documents.length
    });
  } catch (error) {
    logger.error('Error fetching documents:', error);
    throw new AppError('Failed to fetch documents', 500);
  }
}));

export { router as storageRoutes };
