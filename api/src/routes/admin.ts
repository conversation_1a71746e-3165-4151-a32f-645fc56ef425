import { Router, Request, Response } from 'express';
import { db } from '../db/index.js';
import { users, userProfiles, auditLogs } from '../db/schema/users.js';
import { cases } from '../db/schema/cases.js';
import { doctorCredentials } from '../db/schema/doctor-credentials.js';
import { eq, sql, or, like, and, desc } from 'drizzle-orm';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../middleware/opalAuth.js';
import { logAuditEvent } from '../utils/logger.js';
import { PolicyEngine } from '../services/policyEngine.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Middleware to ensure user is an admin
const adminMiddleware = [opalAuthMiddleware, (req: Request, res: Response, next: any) => {
  const user = (req as any).user;
  
  if (!user || user.roles[0] !== 'admin') {
    throw new AppError('Unauthorized: Admin access required', 403);
  }
  
  next();
}];

// MOVED: User management routes have been moved to users.ts
// GET /users - Get all users (admin only)
// This route has been moved to follow the /users/<action> pattern
// Use GET /users instead

// MOVED: User management routes have been moved to users.ts
// GET /users/:id - Get user by ID (admin only)
// This route has been moved to follow the /users/<action> pattern
// Use GET /users/:id instead

// MOVED: User management routes have been moved to users.ts
// PUT /users/:id - Update user (admin only)
// This route has been moved to follow the /users/<action> pattern
// Use PUT /users/:id instead

// MOVED: User management routes have been moved to users.ts
// DELETE /users/:id - Delete user (admin only)
// This route has been moved to follow the /users/<action> pattern
// Use DELETE /users/:id instead

// This route has been moved to users.ts to follow the /users/<userId>/impersonate pattern
// Keeping this comment as a reference for the migration

// MOVED: User management routes have been moved to users.ts
// POST /users/:id/assign-role - Assign role to user (admin only)
// This route has been moved to follow the /users/<action> pattern
// Use POST /users/:id/assign-role instead

// Admin Doctor Credentials Management Routes

// GET /api/admin/credentials - Get all doctor credentials for admin review
router.get('/credentials', ...adminMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const userId = user.id;
  
  // Check authorization using OPAL Policy Engine
  const policyEngine = PolicyEngine.getInstance();
  const hasAccess = await policyEngine.evaluate({
    user,
    resource: { type: 'doctor_credentials' },
    action: 'read',
    environment: {
      time: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
  });
  
  if (!hasAccess.allow) {
    throw new AppError('Unauthorized to access credentials', 403);
  }

  const { status, doctorId, page = 1, limit = 50 } = req.query;
  const offset = (Number(page) - 1) * Number(limit);
  
  try {
    // Build where conditions
    const whereConditions = [];
    if (status) {
      // Handle comma-separated status values for multi-select filtering
      const statusValues = (status as string).split(',').map(s => s.trim());
      if (statusValues.length === 1) {
        whereConditions.push(eq(doctorCredentials.status, statusValues[0] as any));
      } else {
        // Use OR condition for multiple status values
        const statusConditions = statusValues.map(s => eq(doctorCredentials.status, s as any));
        whereConditions.push(or(...statusConditions));
      }
    }
    if (doctorId) {
      whereConditions.push(eq(doctorCredentials.doctorId, doctorId as string));
    }

    let credentials;
    if (whereConditions.length > 0) {
      credentials = await db
        .select({
          id: doctorCredentials.id,
          doctorId: doctorCredentials.doctorId,
          credentialType: doctorCredentials.credentialType,
          credentialNumber: doctorCredentials.credentialNumber,
          issuingAuthority: doctorCredentials.issuingAuthority,
          issuedDate: doctorCredentials.issuedDate,
          expirationDate: doctorCredentials.expirationDate,
          status: doctorCredentials.status,
          verificationMethod: doctorCredentials.verificationMethod,
          verifiedBy: doctorCredentials.verifiedBy,
          verifiedAt: doctorCredentials.verifiedAt,
          notes: doctorCredentials.notes,
          createdAt: doctorCredentials.createdAt,
          updatedAt: doctorCredentials.updatedAt,
        })
        .from(doctorCredentials)
        .where(and(...whereConditions))
        .orderBy(desc(doctorCredentials.createdAt))
        .limit(Number(limit))
        .offset(offset);
    } else {
      credentials = await db
        .select({
          id: doctorCredentials.id,
          doctorId: doctorCredentials.doctorId,
          credentialType: doctorCredentials.credentialType,
          credentialNumber: doctorCredentials.credentialNumber,
          issuingAuthority: doctorCredentials.issuingAuthority,
          issuedDate: doctorCredentials.issuedDate,
          expirationDate: doctorCredentials.expirationDate,
          status: doctorCredentials.status,
          verificationMethod: doctorCredentials.verificationMethod,
          verifiedBy: doctorCredentials.verifiedBy,
          verifiedAt: doctorCredentials.verifiedAt,
          notes: doctorCredentials.notes,
          createdAt: doctorCredentials.createdAt,
          updatedAt: doctorCredentials.updatedAt,
        })
        .from(doctorCredentials)
        .orderBy(desc(doctorCredentials.createdAt))
        .limit(Number(limit))
        .offset(offset);
    }

    // Get doctor information for each credential
    const credentialsWithDoctors = await Promise.all(
      credentials.map(async (credential) => {
        const [doctor] = await db
          .select({
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
          })
          .from(users)
          .where(eq(users.id, credential.doctorId))
          .limit(1);

        let verifiedByUser = null;
        if (credential.verifiedBy) {
          const [verifier] = await db
            .select({
              id: users.id,
              firstName: users.firstName,
              lastName: users.lastName,
              email: users.email,
            })
            .from(users)
            .where(eq(users.id, credential.verifiedBy))
            .limit(1);
          verifiedByUser = verifier || null;
        }

        return {
          ...credential,
          doctor: doctor || null,
          verifiedByUser,
        };
      })
    );

    // Get total count for pagination
    let totalCount;
    if (whereConditions.length > 0) {
      const countResult = await db
        .select()
        .from(doctorCredentials)
        .where(and(...whereConditions));
      totalCount = countResult.length;
    } else {
      const countResult = await db
        .select()
        .from(doctorCredentials);
      totalCount = countResult.length;
    }

    // Log audit event
    logAuditEvent(
      userId,
      'ADMIN_CREDENTIALS_ACCESSED',
      'doctor_credentials',
      {
        filters: { status, doctorId },
        page: Number(page),
        limit: Number(limit),
        totalCount: totalCount,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      credentials: credentialsWithDoctors,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / Number(limit)),
      }
    });
  } catch (error) {
    logger.error('Error fetching admin credentials:', error);
    throw new AppError('Failed to fetch credentials', 500);
  }
}));

// PUT /api/admin/credentials/:id/verify - Verify a doctor credential
router.put('/credentials/:id/verify', ...adminMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const userId = user.id;
  const credentialId = req.params.id;
  const { verificationMethod, notes } = req.body;

  // Check authorization using OPAL Policy Engine
  const policyEngine = PolicyEngine.getInstance();
  const hasAccess = await policyEngine.evaluate({
    user,
    resource: { type: 'doctor_credentials' },
    action: 'verify',
    environment: {
      time: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
  });
  
  if (!hasAccess.allow) {
    throw new AppError('Unauthorized to verify credentials', 403);
  }

  try {
    // Update credential status to verified
    const [updatedCredential] = await db
      .update(doctorCredentials)
      .set({
        status: 'verified',
        verificationMethod: verificationMethod || 'manual',
        verifiedBy: userId,
        verifiedAt: new Date(),
        notes: notes || null,
        updatedAt: new Date(),
      })
      .where(eq(doctorCredentials.id, credentialId))
      .returning();

    if (!updatedCredential) {
      throw new AppError('Credential not found', 404);
    }

    // Log audit event
    logAuditEvent(
      userId,
      'CREDENTIAL_VERIFIED',
      'doctor_credentials',
      {
        credentialId,
        verificationMethod,
        notes,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      success: true,
      message: 'Credential verified successfully',
      credential: updatedCredential,
    });
  } catch (error) {
    logger.error('Error verifying credential:', error);
    throw new AppError('Failed to verify credential', 500);
  }
}));

// PUT /api/admin/credentials/:id/reject - Reject a doctor credential
router.put('/credentials/:id/reject', ...adminMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user;
  const userId = user.id;
  const credentialId = req.params.id;
  const { rejectionReason } = req.body;

  // Check authorization using OPAL Policy Engine
  const policyEngine = PolicyEngine.getInstance();
  const hasAccess = await policyEngine.evaluate({
    user,
    resource: { type: 'doctor_credentials' },
    action: 'reject',
    environment: {
      time: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
  });
  
  if (!hasAccess.allow) {
    throw new AppError('Unauthorized to reject credentials', 403);
  }

  if (!rejectionReason) {
    throw new AppError('Rejection reason is required', 400);
  }

  try {
    // Update credential status to revoked
    const [updatedCredential] = await db
      .update(doctorCredentials)
      .set({
        status: 'revoked',
        verifiedBy: userId,
        verifiedAt: new Date(),
        notes: rejectionReason,
        updatedAt: new Date(),
      })
      .where(eq(doctorCredentials.id, credentialId))
      .returning();

    if (!updatedCredential) {
      throw new AppError('Credential not found', 404);
    }

    // Log audit event
    logAuditEvent(
      userId,
      'CREDENTIAL_REJECTED',
      'doctor_credentials',
      {
        credentialId,
        rejectionReason,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      success: true,
      message: 'Credential rejected successfully',
      credential: updatedCredential,
    });
  } catch (error) {
    logger.error('Error rejecting credential:', error);
    throw new AppError('Failed to reject credential', 500);
  }
}));

// GET /api/admin/pending-profile-changes - Get all pending profile changes
router.get('/pending-profile-changes', ...adminMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req as any).user;
  
  try {
    // Get all audit logs for pending profile changes
    const pendingChanges = await db
      .select()
      .from(auditLogs)
      .where(eq(auditLogs.action, 'PROFILE_UPDATE_PENDING_APPROVAL'))
      .orderBy(desc(auditLogs.timestamp));

    // Parse metadata and get user info for each pending change
    const pendingChangesWithUsers = await Promise.all(
      pendingChanges.map(async (change) => {
        let metadata = {};
        try {
          metadata = JSON.parse(change.metadata || '{}');
        } catch (e) {
          logger.warn('Failed to parse audit log metadata:', undefined, { data: e });
        }

        // Get user info
        const [userInfo] = await db
          .select({
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
            role: users.role,
          })
          .from(users)
          .where(eq(users.id, change.userId!))
          .limit(1);

        return {
          id: change.id,
          userId: change.userId,
          user: userInfo || null,
          pendingFields: (metadata as any).pendingFields || [],
          pendingUserFields: (metadata as any).pendingUserFields || {},
          pendingProfileFields: (metadata as any).pendingProfileFields || {},
          reason: (metadata as any).reason || '',
          requestedAt: change.timestamp,
          ipAddress: change.ipAddress,
          userAgent: change.userAgent,
        };
      })
    );

    // Log audit event
    logAuditEvent(
      user.id,
      'ADMIN_PENDING_PROFILE_CHANGES_ACCESSED',
      'users',
      {
        count: pendingChangesWithUsers.length,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      pendingChanges: pendingChangesWithUsers,
      count: pendingChangesWithUsers.length,
    });
  } catch (error) {
    logger.error('Error fetching pending profile changes:', error);
    throw new AppError('Failed to fetch pending profile changes', 500);
  }
}));

// PUT /api/admin/pending-profile-changes/:auditLogId/approve - Approve pending profile changes
router.put('/pending-profile-changes/:auditLogId/approve', ...adminMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { auditLogId } = req.params;
  const { notes } = req.body;
  const adminUser = (req as any).user;
  
  try {
    // Get the pending change audit log
    const [auditLog] = await db
      .select()
      .from(auditLogs)
      .where(and(
        eq(auditLogs.id, auditLogId),
        eq(auditLogs.action, 'PROFILE_UPDATE_PENDING_APPROVAL')
      ))
      .limit(1);

    if (!auditLog) {
      throw new AppError('Pending profile change not found', 404);
    }

    // Parse the pending changes
    let metadata = {};
    try {
      metadata = JSON.parse(auditLog.metadata || '{}');
    } catch (e) {
      throw new AppError('Invalid pending change data', 400);
    }

    const pendingUserFields = (metadata as any).pendingUserFields || {};
    const pendingProfileFields = (metadata as any).pendingProfileFields || {};

    // Apply the pending changes
    if (Object.keys(pendingUserFields).length > 0) {
      await db
        .update(users)
        .set({
          ...pendingUserFields,
          updatedAt: new Date(),
        })
        .where(eq(users.id, auditLog.userId!));
    }

    if (Object.keys(pendingProfileFields).length > 0) {
      // Check if profile exists
      const [existingProfile] = await db
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.userId, auditLog.userId!))
        .limit(1);

      if (existingProfile) {
        // Update existing profile
        await db
          .update(userProfiles)
          .set({
            ...pendingProfileFields,
            updatedAt: new Date(),
          })
          .where(eq(userProfiles.userId, auditLog.userId!));
      } else {
        // Create new profile
        await db
          .insert(userProfiles)
          .values({
            userId: auditLog.userId!,
            ...pendingProfileFields,
          });
      }
    }

    // Log approval audit event
    logAuditEvent(
      adminUser.id,
      'ADMIN_PROFILE_CHANGES_APPROVED',
      'users',
      {
        targetUserId: auditLog.userId,
        approvedFields: Object.keys({ ...pendingUserFields, ...pendingProfileFields }),
        originalAuditLogId: auditLogId,
        notes,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    // Mark the original audit log as processed by updating its metadata
    const updatedMetadata = {
      ...metadata,
      status: 'approved',
      approvedBy: adminUser.id,
      approvedAt: new Date().toISOString(),
      approvalNotes: notes,
    };

    await db
      .update(auditLogs)
      .set({
        metadata: JSON.stringify(updatedMetadata),
      })
      .where(eq(auditLogs.id, auditLogId));

    res.json({
      message: 'Profile changes approved and applied successfully',
      approvedFields: Object.keys({ ...pendingUserFields, ...pendingProfileFields }),
    });
  } catch (error) {
    logger.error('Error approving profile changes:', error);
    throw new AppError('Failed to approve profile changes', 500);
  }
}));

// PUT /api/admin/pending-profile-changes/:auditLogId/reject - Reject pending profile changes
router.put('/pending-profile-changes/:auditLogId/reject', ...adminMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { auditLogId } = req.params;
  const { rejectionReason } = req.body;
  const adminUser = (req as any).user;
  
  if (!rejectionReason || !rejectionReason.trim()) {
    throw new AppError('Rejection reason is required', 400);
  }

  try {
    // Get the pending change audit log
    const [auditLog] = await db
      .select()
      .from(auditLogs)
      .where(and(
        eq(auditLogs.id, auditLogId),
        eq(auditLogs.action, 'PROFILE_UPDATE_PENDING_APPROVAL')
      ))
      .limit(1);

    if (!auditLog) {
      throw new AppError('Pending profile change not found', 404);
    }

    // Parse the pending changes
    let metadata = {};
    try {
      metadata = JSON.parse(auditLog.metadata || '{}');
    } catch (e) {
      throw new AppError('Invalid pending change data', 400);
    }

    // Log rejection audit event
    logAuditEvent(
      adminUser.id,
      'ADMIN_PROFILE_CHANGES_REJECTED',
      'users',
      {
        targetUserId: auditLog.userId,
        rejectedFields: (metadata as any).pendingFields || [],
        originalAuditLogId: auditLogId,
        rejectionReason,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    // Mark the original audit log as processed by updating its metadata
    const updatedMetadata = {
      ...metadata,
      status: 'rejected',
      rejectedBy: adminUser.id,
      rejectedAt: new Date().toISOString(),
      rejectionReason,
    };

    await db
      .update(auditLogs)
      .set({
        metadata: JSON.stringify(updatedMetadata),
      })
      .where(eq(auditLogs.id, auditLogId));

    res.json({
      message: 'Profile changes rejected successfully',
      rejectionReason,
    });
  } catch (error) {
    logger.error('Error rejecting profile changes:', error);
    throw new AppError('Failed to reject profile changes', 500);
  }
}));

export { router as adminRoutes };
