import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { users, userProfiles } from '../db/schema/users.js';
import { doctorCredentials } from '../db/schema/doctor-credentials.js';
import { eq, and, like, sql, or, count, gt } from 'drizzle-orm';
import { opalAuthMiddleware, requireResourceAccess } from '../middleware/opalAuth.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { canUserAccess } from '../utils/policyUtils.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Doctor case assignment eligibility calculation
// Simplified: Assume all active doctors are eligible since credentials are verified by admin before approval
const calculateDoctorEligibility = async (doctorId: string) => {
  try {
    // Get doctor's basic info to verify they exist and are active
    const [doctor] = await db
      .select({
        id: users.id,
        isActive: users.isActive,
        role: users.role
      })
      .from(users)
      .where(and(
        eq(users.id, doctorId),
        eq(users.role, 'doctor')
      ))
      .limit(1);

    if (!doctor) {
      return {
        eligibilityStatus: 'not_eligible' as const,
        eligibilityReason: 'Doctor not found',
        credentialsSummary: {
          total: 0,
          verified: 0,
          pending: 0,
          expired: 0,
          hasMedicalLicense: false
        }
      };
    }

    // If doctor is active, they are eligible (credentials assumed to be pre-verified by admin)
    if (doctor.isActive) {
      return {
        eligibilityStatus: 'eligible' as const,
        eligibilityReason: 'Doctor is active and approved by admin',
        credentialsSummary: {
          total: 1, // Simplified - assume basic credentials exist
          verified: 1,
          pending: 0,
          expired: 0,
          hasMedicalLicense: true // Assume verified by admin
        }
      };
    } else {
      return {
        eligibilityStatus: 'not_eligible' as const,
        eligibilityReason: 'Doctor account is inactive',
        credentialsSummary: {
          total: 0,
          verified: 0,
          pending: 0,
          expired: 0,
          hasMedicalLicense: false
        }
      };
    }
  } catch (error) {
    logger.error('Error calculating doctor eligibility:', error);
    return {
      eligibilityStatus: 'not_eligible' as const,
      eligibilityReason: 'Error calculating eligibility',
      credentialsSummary: {
        total: 0,
        verified: 0,
        pending: 0,
        expired: 0,
        hasMedicalLicense: false
      }
    };
  }
};

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phoneNumber: z.string().optional(),
  dateOfBirth: z.string().datetime().optional(),
  gender: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  zipCode: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  bio: z.string().optional(),
});

// Get current user profile
router.get('/me', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;

  // Get user with profile data
  const [user] = await db
    .select({
      id: users.id,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      isActive: users.isActive,
      isEmailVerified: users.isEmailVerified,
      lastLoginAt: users.lastLoginAt,
      createdAt: users.createdAt,
      profile: {
        phoneNumber: userProfiles.phoneNumber,
        dateOfBirth: userProfiles.dateOfBirth,
        gender: userProfiles.gender,
        address: userProfiles.address,
        city: userProfiles.city,
        state: userProfiles.state,
        country: userProfiles.country,
        zipCode: userProfiles.zipCode,
        emergencyContactName: userProfiles.emergencyContactName,
        emergencyContactPhone: userProfiles.emergencyContactPhone,
        profilePictureUrl: userProfiles.profilePictureUrl,
        bio: userProfiles.bio,
      },
    })
    .from(users)
    .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
    .where(eq(users.id, userId))
    .limit(1);

  if (!user) {
    throw new AppError('User not found', 404);
  }
  

  // Viewing user profile is routine - no audit logging needed

  res.json({
    user: {
      ...user,
      profile: user.profile,
    },
  });
}));

// Update user profile
router.put('/me', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [(req as any).user.role];
  const validatedData = updateProfileSchema.parse(req.body);
  

  // Separate user fields from profile fields
  const userFields: any = {};
  const profileFields: any = {};

  if (validatedData.firstName) userFields.firstName = validatedData.firstName;
  if (validatedData.lastName) userFields.lastName = validatedData.lastName;

  const profileFieldNames = [
    'phoneNumber', 'dateOfBirth', 'gender', 'address', 'city',
    'state', 'country', 'zipCode', 'emergencyContactName',
    'emergencyContactPhone', 'bio'
  ];

  profileFieldNames.forEach(field => {
    if (validatedData[field as keyof typeof validatedData] !== undefined) {
      let value = validatedData[field as keyof typeof validatedData];
      // Handle dateOfBirth properly to avoid serialization issues
      if (field === 'dateOfBirth') {
        if (typeof value === 'string') {
          // Ensure we have a valid date string format
          try {
            // Validate the date format by attempting to create a Date object
            const testDate = new Date(value);
            if (isNaN(testDate.getTime())) {
              // Invalid date string, skip this field
              logger.warn('Invalid date format for dateOfBirth: ${value}');
              return; // Skip this field
            }
            // Keep as string for database storage
          } catch (err) {
            logger.warn('Error processing dateOfBirth: ${err}');
            return; // Skip this field
          }
        } else if (value !== null && value !== undefined) {
          // If it's not a string and not null/undefined, it's an invalid type
          logger.warn('Invalid type for dateOfBirth: ${typeof value}');
          return; // Skip this field
        }
      }
      profileFields[field] = value;
    }
  });

  // Check if user is a doctor and if critical fields (like bio/specialization) are being updated
  const isDoctorUpdatingCriticalFields = userRoles.includes('doctor') &&
    (profileFields.bio !== undefined || userFields.firstName !== undefined || userFields.lastName !== undefined);

  if (isDoctorUpdatingCriticalFields) {
    // For doctors updating critical fields, require admin approval
    // Store pending changes instead of applying them immediately
    
    // Check if profile exists
    const [existingProfile] = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, userId))
      .limit(1);

    // For now, store pending changes in audit logs for admin review
    // This is a temporary solution until we create a proper pending_changes table
    logAuditEvent(
      userId,
      'PROFILE_UPDATE_PENDING_APPROVAL',
      'users',
      {
        pendingFields: Object.keys({ ...userFields, ...profileFields }),
        pendingUserFields: userFields,
        pendingProfileFields: profileFields,
        reason: 'Doctor profile changes require admin approval',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      message: 'Profile changes submitted for admin approval. Changes will be applied once approved.',
      status: 'pending_approval'
    });
    return;
  }

  // For non-doctors or non-critical fields, apply changes immediately
  // Update user fields if any
  if (Object.keys(userFields).length > 0) {
    userFields.updatedAt = new Date();
    await db
      .update(users)
      .set(userFields)
      .where(eq(users.id, userId));
  }

  // Update or create profile if any profile fields
  if (Object.keys(profileFields).length > 0) {
    profileFields.updatedAt = new Date();

    // Check if profile exists
    const [existingProfile] = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, userId))
      .limit(1);

    if (existingProfile) {
      // Update existing profile
      await db
        .update(userProfiles)
        .set(profileFields)
        .where(eq(userProfiles.userId, userId));
    } else {
      // Create new profile
      await db
        .insert(userProfiles)
        .values({
          userId,
          ...profileFields,
        });
    }
  }

  // Log audit event
  logAuditEvent(
    userId,
    'PROFILE_UPDATED',
    'users',
    {
      updatedFields: Object.keys({ ...userFields, ...profileFields }),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Profile updated successfully',
  });
}));

// Delete user account (soft delete)
router.delete('/me', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;

  // Soft delete by setting isActive to false
  await db
    .update(users)
    .set({ 
      isActive: false,
      updatedAt: new Date(),
    })
    .where(eq(users.id, userId));

  // Log audit event
  logAuditEvent(
    userId,
    'ACCOUNT_DELETED',
    'users',
    {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Account deactivated successfully',
  });
}));

// Get all doctors (admin/agent only)
router.get('/doctors', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userRoles = (req as any).user.roles || [(req as any).user.role];
  
  // Only admins, agents, and doctors can access the list of all doctors
  if (!userRoles.includes('admin') && !userRoles.includes('agent') && !userRoles.includes('doctor')) {
    throw new AppError('Unauthorized access', 403);
  }

  // Get all active doctors with their profiles and specializations
  const doctors = await db
    .select({
      id: users.id,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      createdAt: users.createdAt,
      profile: {
        phoneNumber: userProfiles.phoneNumber,
        bio: userProfiles.bio,
        profilePictureUrl: userProfiles.profilePictureUrl,
      },
    })
    .from(users)
    .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
    .where(
      and(
        eq(users.isActive, true),
        eq(users.role, 'doctor')
      )
    )
    .orderBy(users.lastName, users.firstName);

  // Get specializations for each doctor
  const doctorsWithSpecializations = await Promise.all(
    doctors.map(async (doctor) => {
      try {
        const specializations = await db.execute(sql`
          SELECT
            dsa.id as assignment_id,
            dsa.is_primary,
            dsa.board_certified,
            dsa.years_experience,
            ds.id as specialization_id,
            ds.name as specialization_name,
            ds.code as specialization_code,
            sc.name as category_name
          FROM doctor_specialization_assignments dsa
          JOIN doctor_specializations ds ON dsa.specialization_id = ds.id
          LEFT JOIN specialization_categories sc ON ds.category_id = sc.id
          WHERE dsa.doctor_id = ${doctor.id} AND dsa.is_active = true
          ORDER BY dsa.is_primary DESC, ds.name
        `);

        return {
          ...doctor,
          specializations: specializations || []
        };
      } catch (error) {
        logger.error('Error fetching specializations for doctor ${doctor.id}:', error);
        return {
          ...doctor,
          specializations: []
        };
      }
    })
  );

  // Log audit event
  logAuditEvent(
    (req as any).user.id,
    'DOCTORS_LIST_ACCESSED',
    'users',
    {
      count: doctorsWithSpecializations.length,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    doctors: doctorsWithSpecializations,
    count: doctorsWithSpecializations.length,
  });
}));

// Get all users (admin only)
router.get('/', opalAuthMiddleware, requireResourceAccess('users', 'read'), asyncHandler(async (req: Request, res: Response) => {
  // Extract query parameters for filtering
  const { role, status, search, page = '1', limit = '50' } = req.query;
  const pageNum = parseInt(page as string, 10);
  const limitNum = parseInt(limit as string, 10);
  const offset = (pageNum - 1) * limitNum;
  
  // Build query with filters
  const conditions = [];
  
  // Role filtering - support filtering by role
  if (role) {
    // Handle comma-separated role values for multi-select filtering
    const roleValues = (role as string).split(',').map(r => r.trim());
    if (roleValues.length === 1) {
      conditions.push(eq(users.role, roleValues[0] as any));
    } else {
      // Use OR condition for multiple role values
      const roleConditions = roleValues.map(r => eq(users.role, r as any));
      conditions.push(or(...roleConditions));
    }
  }
  
  if (status === 'active') {
    conditions.push(eq(users.isActive, true));
  } else if (status === 'inactive') {
    conditions.push(eq(users.isActive, false));
  }
  
  if (search) {
    const searchTerm = `%${search}%`;
    conditions.push(
      or(
        like(users.email, searchTerm),
        like(users.firstName, searchTerm),
        like(users.lastName, searchTerm)
      )
    );
  }
  
  // Build base query with conditions
  let results;
  if (conditions.length > 0) {
    if (conditions.length === 1) {
      results = await db
        .select({
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          role: users.role,
          isActive: users.isActive,
          isEmailVerified: users.isEmailVerified,
          lastLoginAt: users.lastLoginAt,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        })
        .from(users)
        .where(conditions[0])
        .limit(limitNum)
        .offset(offset)
        .orderBy(users.createdAt);
    } else {
      // Apply multiple conditions with AND
      let combinedCondition = conditions[0];
      for (let i = 1; i < conditions.length; i++) {
        combinedCondition = and(combinedCondition, conditions[i]);
      }
      results = await db
        .select({
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          role: users.role,
          isActive: users.isActive,
          isEmailVerified: users.isEmailVerified,
          lastLoginAt: users.lastLoginAt,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        })
        .from(users)
        .where(combinedCondition)
        .limit(limitNum)
        .offset(offset)
        .orderBy(users.createdAt);
    }
  } else {
    results = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
        isActive: users.isActive,
        isEmailVerified: users.isEmailVerified,
        lastLoginAt: users.lastLoginAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .limit(limitNum)
      .offset(offset)
      .orderBy(users.createdAt);
  }
  
  // Count total users for pagination with same conditions
  let countResult;
  if (conditions.length > 0) {
    if (conditions.length === 1) {
      [countResult] = await db
        .select({ count: sql`count(*)` })
        .from(users)
        .where(conditions[0]);
    } else {
      let combinedCondition = conditions[0];
      for (let i = 1; i < conditions.length; i++) {
        combinedCondition = and(combinedCondition, conditions[i]);
      }
      [countResult] = await db
        .select({ count: sql`count(*)` })
        .from(users)
        .where(combinedCondition);
    }
  } else {
    [countResult] = await db
      .select({ count: sql`count(*)` })
      .from(users);
  }
  const count = Number(countResult.count);
  
  // Admin viewing user list is routine - no audit logging needed
  
  // For doctors, calculate eligibility status
  const resultsWithEligibility = await Promise.all(
    results.map(async (user) => {
      if (user.role === 'doctor') {
        const eligibility = await calculateDoctorEligibility(user.id);
        return {
          ...user,
          caseAssignmentEligibility: eligibility
        };
      }
      return user;
    })
  );

  res.json({
    data: resultsWithEligibility,
    meta: {
      total: Number(count),
      page: pageNum,
      limit: limitNum,
      pages: Math.ceil(Number(count) / limitNum),
    },
  });
}));

// Get user by ID (admin only)
router.get('/:id', opalAuthMiddleware, requireResourceAccess('users', 'read'), asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const [user] = await db
    .select({
      id: users.id,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      isActive: users.isActive,
      isEmailVerified: users.isEmailVerified,
      lastLoginAt: users.lastLoginAt,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt,
      profile: {
        phoneNumber: userProfiles.phoneNumber,
        dateOfBirth: userProfiles.dateOfBirth,
        gender: userProfiles.gender,
        address: userProfiles.address,
        city: userProfiles.city,
        state: userProfiles.state,
        country: userProfiles.country,
        zipCode: userProfiles.zipCode,
        emergencyContactName: userProfiles.emergencyContactName,
        emergencyContactPhone: userProfiles.emergencyContactPhone,
        profilePictureUrl: userProfiles.profilePictureUrl,
        bio: userProfiles.bio,
      },
    })
    .from(users)
    .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
    .where(eq(users.id, id))
    .limit(1);
  
  if (!user) {
    throw new AppError('User not found', 404);
  }
  
  // Log audit event
  logAuditEvent(
    (req as any).user.id,
    'ADMIN_USER_DETAIL_ACCESSED',
    'users',
    {
      targetUserId: id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    data: user,
  });
}));

// Update user (admin only)
router.put('/:id', opalAuthMiddleware, requireResourceAccess('users', 'update'), asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { firstName, lastName, role, isActive } = req.body;
  
  // Check if user exists
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);
  
  if (!existingUser) {
    throw new AppError('User not found', 404);
  }
  
  // Update user
  await db
    .update(users)
    .set({
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(role && { role }),
      ...(isActive !== undefined && { isActive }),
      updatedAt: new Date(),
    })
    .where(eq(users.id, id));
  
  // Log audit event
  logAuditEvent(
    (req as any).user.id,
    'ADMIN_USER_UPDATED',
    'users',
    {
      targetUserId: id,
      updatedFields: Object.keys(req.body),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'User updated successfully',
  });
}));

// Delete user (admin only)
router.delete('/:id', opalAuthMiddleware, requireResourceAccess('users', 'delete'), asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  // Check if user exists
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);
  
  if (!existingUser) {
    throw new AppError('User not found', 404);
  }
  
  // Soft delete user
  await db
    .update(users)
    .set({
      isActive: false,
      updatedAt: new Date(),
    })
    .where(eq(users.id, id));
  
  // Log audit event
  logAuditEvent(
    (req as any).user.id,
    'ADMIN_USER_DELETED',
    'users',
    {
      targetUserId: id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'User deleted successfully',
  });
}));

// Update user status (admin only)
router.put('/:id/status', opalAuthMiddleware, requireResourceAccess('users', 'update'), asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status } = req.body;
  
  // Validate status
  if (status !== 'active' && status !== 'suspended') {
    throw new AppError('Invalid status value', 400);
  }
  
  // Check if user exists
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);
  
  if (!existingUser) {
    throw new AppError('User not found', 404);
  }
  
  // Update user status
  await db
    .update(users)
    .set({
      isActive: status === 'active',
      updatedAt: new Date(),
    })
    .where(eq(users.id, id));
  
  // Log audit event
  logAuditEvent(
    (req as any).user.id,
    'ADMIN_USER_STATUS_UPDATED',
    'users',
    {
      targetUserId: id,
      newStatus: status,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: `User ${status === 'active' ? 'activated' : 'suspended'} successfully`,
  });
}));

// Note: Verification status endpoint removed as it's not in the current schema
// If verification status is needed in the future, add the field to the users schema first

// Assign role to user (admin only)
router.post('/:id/assign-role', opalAuthMiddleware, requireResourceAccess('users', 'assign'), asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { role } = req.body;
  
  // Validate role
  if (!['admin', 'patient', 'doctor', 'agent'].includes(role)) {
    throw new AppError('Invalid role value', 400);
  }
  
  // Check if user exists
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);
  
  if (!existingUser) {
    throw new AppError('User not found', 404);
  }
  
  // For now, we'll just log the role assignment since we don't have a roles table yet
  // In the future, this would insert into a user_roles table
  logger.info('Role ${role} assigned to user ${id} by admin ${(req as any).user.id}', { requestId: 'context-needed' }, { data: undefined });
  
  // Log audit event
  logAuditEvent(
    (req as any).user.id,
    'ADMIN_USER_ROLE_ASSIGNED',
    'users',
    {
      targetUserId: id,
      newRole: role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: `User role updated to ${role} successfully`,
  });
}));

// User impersonation (admin only)
router.post('/:id/impersonate', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const adminUser = (req as any).user;
  const userRoles = (req as any).user.roles || [(req as any).user.role];
  
  // Only admins can impersonate users
  if (!userRoles.includes('admin')) {
    throw new AppError('Unauthorized: Admin access required', 403);
  }
  
  // Check if target user exists
  const [targetUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);
  
  if (!targetUser) {
    throw new AppError('User not found', 404);
  }
  
  // Generate impersonation token
  const jwt = await import('jsonwebtoken');
  const token = jwt.default.sign(
    {
      userId: targetUser.id,
      email: targetUser.email,
      roles: ['patient'], // Default to patient role for now
      isImpersonation: true,
      impersonatedBy: adminUser.id
    },
    process.env.JWT_SECRET || 'continuia-secret-key',
    { expiresIn: '8h' }
  );
  
  // Log audit event
  logAuditEvent(
    adminUser.id,
    'ADMIN_USER_IMPERSONATED',
    'users',
    {
      targetUserId: id,
      targetUserRole: targetUser.role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    success: true,
    token,
    user: {
      id: targetUser.id,
      email: targetUser.email,
      firstName: targetUser.firstName,
      lastName: targetUser.lastName,
      roles: ['patient'] // Default to patient role for now
    }
  });
}));

export { router as userRoutes };
