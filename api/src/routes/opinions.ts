import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { medicalOpinions, cases, users } from '../db/schema/index.js';
import { eq, and, ilike, desc, ne } from 'drizzle-orm';
import { opalAuthMiddleware, requireResourceAccess } from '../middleware/opalAuth.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { CaseDoctorService } from '../services/caseDoctorService.js';

const router = Router();

// Validation schemas
const createOpinionSchema = z.object({
  caseId: z.string().uuid('Invalid case ID'),
  diagnosis: z.string().min(10, 'Diagnosis must be at least 10 characters'),
  recommendations: z.string().min(10, 'Recommendations must be at least 10 characters'),
  treatmentPlan: z.string().optional(),
  followUpInstructions: z.string().optional(),
  urgencyLevel: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  confidenceLevel: z.enum(['low', 'medium', 'high']).default('medium'),
  additionalTests: z.string().optional(),
  referralSpecialty: z.string().optional(),
  notes: z.string().optional(),
});

const updateOpinionSchema = z.object({
  diagnosis: z.string().min(10).optional(),
  recommendations: z.string().min(10).optional(),
  treatmentPlan: z.string().optional(),
  followUpInstructions: z.string().optional(),
  urgencyLevel: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  confidenceLevel: z.enum(['low', 'medium', 'high']).optional(),
  additionalTests: z.string().optional(),
  referralSpecialty: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(['draft', 'submitted', 'reviewed', 'approved']).optional(),
});

// Create a medical opinion
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  
  // Only doctors can create medical opinions
  if (userRole !== 'doctor') {
    throw new AppError('Only doctors can create medical opinions', 403);
  }

  const validatedData = createOpinionSchema.parse(req.body);

  // Verify case exists and doctor is assigned
  const [caseData] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, validatedData.caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check if doctor is assigned to this case using multi-doctor assignment system
  const isAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, validatedData.caseId);
  if (!isAssigned) {
    throw new AppError('You are not assigned to this case', 403);
  }

  // Check if opinion already exists for this case by this doctor
  const [existingOpinion] = await db
    .select()
    .from(medicalOpinions)
    .where(and(
      eq(medicalOpinions.caseId, validatedData.caseId),
      eq(medicalOpinions.doctorId, userId)
    ))
    .limit(1);

  if (existingOpinion) {
    throw new AppError('You have already provided an opinion for this case', 409);
  }

  const [newOpinion] = await db
    .insert(medicalOpinions)
    .values({
      caseId: validatedData.caseId,
      doctorId: userId,
      diagnosis: validatedData.diagnosis,
      recommendations: validatedData.recommendations,
      treatmentPlan: validatedData.treatmentPlan,
      followUpInstructions: validatedData.followUpInstructions,
      urgencyLevel: validatedData.urgencyLevel,
      confidenceLevel: validatedData.confidenceLevel,
      additionalTests: validatedData.additionalTests,
      referralSpecialty: validatedData.referralSpecialty,
      notes: validatedData.notes,
      status: 'draft',
    })
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'OPINION_CREATED',
    'medical_opinions',
    {
      opinionId: newOpinion.id,
      caseId: validatedData.caseId,
      urgencyLevel: newOpinion.urgencyLevel,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.status(201).json({
    message: 'Medical opinion created successfully',
    opinion: newOpinion,
  });
}));

// Get all opinions for current user
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  const { caseId, status } = req.query;

  let query = db.select().from(medicalOpinions);

  if (userRole === 'patient') {
    // Patients can see opinions for their cases
    query = query
      .leftJoin(cases, eq(medicalOpinions.caseId, cases.id))
      .where(eq(cases.patientId, userId)) as any;
  } else if (userRole === 'doctor') {
    // Doctors can see opinions they created
    query = query.where(eq(medicalOpinions.doctorId, userId)) as any;
  } else if (userRole !== 'agent' && userRole !== 'admin') {
    throw new AppError('Unauthorized to view opinions', 403);
  }

  // Add filters
  if (caseId && typeof caseId === 'string') {
    query = (query as any).where(eq(medicalOpinions.caseId, caseId));
  }

  if (status && typeof status === 'string') {
    query = (query as any).where(eq(medicalOpinions.status, status as any));
  }

  // Patients should not see draft opinions
  if (userRole === 'patient') {
    query = (query as any).where(ne(medicalOpinions.status, 'draft' as any));
  }

  const opinions = await (query as any).orderBy(desc(medicalOpinions.createdAt));

  res.json({
    opinions,
    total: opinions.length,
  });
}));

// Get a specific opinion by ID
router.get('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const opinionId = req.params.id;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';

  const [opinion] = await db
    .select()
    .from(medicalOpinions)
    .where(eq(medicalOpinions.id, opinionId))
    .limit(1);

  if (!opinion) {
    throw new AppError('Opinion not found', 404);
  }

  // Check authorization
  let canAccess = false;

  if (userRole === 'admin' || userRole === 'agent') {
    canAccess = true;
  } else if (userRole === 'doctor') {
    canAccess = opinion.doctorId === userId;
  } else if (userRole === 'patient') {
    // Check if patient owns the case
    const [caseData] = await db
      .select()
      .from(cases)
      .where(eq(cases.id, opinion.caseId))
      .limit(1);
    
    canAccess = caseData?.patientId === userId;
  }

  if (!canAccess) {
    throw new AppError('Unauthorized to access this opinion', 403);
  }

  // Additional visibility rule: patients cannot access draft opinions
  if (userRole === 'patient' && opinion.status === 'draft') {
    throw new AppError('Opinion not available', 403);
  }

  // Log audit event
  logAuditEvent(
    userId,
    'OPINION_ACCESSED',
    'medical_opinions',
    {
      opinionId: opinion.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({ opinion });
}));

// Update a medical opinion
router.put('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const opinionId = req.params.id;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';
  const validatedData = updateOpinionSchema.parse(req.body);

  const [existingOpinion] = await db
    .select()
    .from(medicalOpinions)
    .where(eq(medicalOpinions.id, opinionId))
    .limit(1);

  if (!existingOpinion) {
    throw new AppError('Opinion not found', 404);
  }

  // Check authorization - only the doctor who created it, agents, and admins can update
  const canUpdate =
    userRole === 'admin' ||
    userRole === 'agent' ||
    (userRole === 'doctor' && existingOpinion.doctorId === userId);

  if (!canUpdate) {
    throw new AppError('Unauthorized to update this opinion', 403);
  }

  // Don't allow updates to submitted/approved opinions unless admin/agent
  if (existingOpinion.status === 'approved' && userRole === 'doctor') {
    throw new AppError('Cannot update approved opinions', 403);
  }

  const [updatedOpinion] = await db
    .update(medicalOpinions)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(eq(medicalOpinions.id, opinionId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'OPINION_UPDATED',
    'medical_opinions',
    {
      opinionId: updatedOpinion.id,
      updatedFields: Object.keys(validatedData),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Opinion updated successfully',
    opinion: updatedOpinion,
  });
}));

// Submit an opinion for review
router.post('/:id/submit', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const opinionId = req.params.id;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';

  const [existingOpinion] = await db
    .select()
    .from(medicalOpinions)
    .where(eq(medicalOpinions.id, opinionId))
    .limit(1);

  if (!existingOpinion) {
    throw new AppError('Opinion not found', 404);
  }

  // Only the doctor who created it can submit
  if (userRole !== 'doctor' || existingOpinion.doctorId !== userId) {
    throw new AppError('Unauthorized to submit this opinion', 403);
  }

  if (existingOpinion.status !== 'draft') {
    throw new AppError('Only draft opinions can be submitted', 400);
  }

  const [updatedOpinion] = await db
    .update(medicalOpinions)
    .set({
      status: 'submitted',
      submittedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(medicalOpinions.id, opinionId))
    .returning();

  // Update case status to completed
  await db
    .update(cases)
    .set({
      status: 'completed',
      completedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(cases.id, existingOpinion.caseId));

  // Log audit event
  logAuditEvent(
    userId,
    'OPINION_SUBMITTED',
    'medical_opinions',
    {
      opinionId: updatedOpinion.id,
      caseId: existingOpinion.caseId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Opinion submitted successfully',
    opinion: updatedOpinion,
  });
}));

// Approve an opinion (agent/admin only)
router.post('/:id/approve', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const opinionId = req.params.id;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';

  if (userRole !== 'agent' && userRole !== 'admin') {
    throw new AppError('Only agents and admins can approve opinions', 403);
  }

  const [existingOpinion] = await db
    .select()
    .from(medicalOpinions)
    .where(eq(medicalOpinions.id, opinionId))
    .limit(1);

  if (!existingOpinion) {
    throw new AppError('Opinion not found', 404);
  }

  if (existingOpinion.status !== 'submitted') {
    throw new AppError('Only submitted opinions can be approved', 400);
  }

  const [updatedOpinion] = await db
    .update(medicalOpinions)
    .set({
      status: 'approved',
      approvedBy: userId,
      approvedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(medicalOpinions.id, opinionId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'OPINION_APPROVED',
    'medical_opinions',
    {
      opinionId: updatedOpinion.id,
      caseId: existingOpinion.caseId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Opinion approved successfully',
    opinion: updatedOpinion,
  });
}));

// Get opinions by case ID
router.get('/case/:caseId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.caseId;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles || [];
  const userRole = userRoles[0] || 'patient';

  // Verify case exists and user has access
  const [caseData] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check if doctor is assigned to case using case_doctors table
  let isDoctorAssigned = false;
  if (userRole === 'doctor') {
    isDoctorAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
  }
  
  // Check authorization
  const canAccess =
    userRole === 'admin' ||
    userRole === 'agent' ||
    (userRole === 'patient' && caseData.patientId === userId) ||
    (userRole === 'doctor' && isDoctorAssigned);

  if (!canAccess) {
    throw new AppError('Unauthorized to access opinions for this case', 403);
  }

  let opinionsQuery: any = db
    .select()
    .from(medicalOpinions)
    .where(eq(medicalOpinions.caseId, caseId));

  // Patients should not see draft opinions
  if (userRole === 'patient') {
    opinionsQuery = (opinionsQuery as any).where(ne(medicalOpinions.status, 'draft' as any));
  }

  const opinions = await (opinionsQuery as any).orderBy(desc(medicalOpinions.createdAt));

  res.json({
    caseId,
    opinions,
    total: opinions.length,
  });
}));

export { router as opinionsRoutes };
