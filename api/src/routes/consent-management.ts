import { Router, Request, Response } from 'express';
import { opalAuthMiddleware } from '../middleware/opalAuth';
import { consentManagementService } from '../services/consentManagementService';
import { db } from '../db';
import { legalComplianceTemplates, legalComplianceVersions } from '../db/schema';
import { eq, desc } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
const router = Router();

/**
 * Get pending consents for the current user
 */
router.get('/pending', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const pendingConsents = await consentManagementService.getUserPendingConsents(userId);
    
    res.json({
      success: true,
      data: {
        consents: pendingConsents,
        hasBlocking: pendingConsents.some(c => c.isRequired),
      },
    });
  } catch (error) {
    logger.error('Error fetching pending consents:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch pending consents',
    });
  }
});

/**
 * Check if user has blocking consents (for middleware/login checks)
 */
router.get('/blocking-check', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const hasBlocking = await consentManagementService.hasBlockingConsents(userId);
    
    res.json({
      success: true,
      data: { hasBlocking },
    });
  } catch (error) {
    logger.error('Error checking blocking consents:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check blocking consents',
    });
  }
});

/**
 * Get full consent document for review
 */
router.get('/templates/:templateId', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { templateId } = req.params;
    
    // Get template info
    const [template] = await db
      .select()
      .from(legalComplianceTemplates)
      .where(eq(legalComplianceTemplates.id, templateId))
      .limit(1);

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
      });
    }

    // Get latest version
    const [latestVersion] = await db
      .select()
      .from(legalComplianceVersions)
      .where(eq(legalComplianceVersions.templateId, templateId))
      .orderBy(desc(legalComplianceVersions.version))
      .limit(1);

    if (!latestVersion) {
      return res.status(404).json({
        success: false,
        error: 'No versions found for template',
      });
    }

    res.json({
      success: true,
      data: {
        template,
        version: latestVersion,
      },
    });
  } catch (error) {
    logger.error('Error fetching consent document:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch consent document',
    });
  }
});

/**
 * Accept a consent requirement
 */
router.post('/accept/:requirementId', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { requirementId } = req.params;
    const userId = req.user.id;
    const ipAddress = req.ip;
    const userAgent = req.get('User-Agent');

    await consentManagementService.acceptConsent(
      userId,
      requirementId,
      ipAddress,
      userAgent
    );

    res.json({
      success: true,
      message: 'Consent accepted successfully',
    });
  } catch (error) {
    logger.error('Error accepting consent:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to accept consent',
    });
  }
});

/**
 * Trigger consent assignment (for testing/manual assignment)
 */
router.post('/assign', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { triggerType, context } = req.body;
    
    await consentManagementService.assignConsents(triggerType, {
      ...context,
      userId: req.user.id,
      userRole: req.user.role,
    });

    res.json({
      success: true,
      message: 'Consents assigned successfully',
    });
  } catch (error) {
    logger.error('Error assigning consents:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to assign consents',
    });
  }
});

/**
 * Initialize predefined consent rules (admin only)
 */
router.post('/initialize-rules', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required',
      });
    }

    await consentManagementService.createPredefinedRules();

    res.json({
      success: true,
      message: 'Predefined consent rules created successfully',
    });
  } catch (error) {
    logger.error('Error initializing consent rules:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initialize consent rules',
    });
  }
});

export default router;
