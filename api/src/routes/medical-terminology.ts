import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { medicalTerminologyService } from '../services/medical-terminology';
import { opalAuthMiddleware } from '../middleware/opalAuth';
import { asyncHand<PERSON> } from '../utils/async-handler';
import { AppError } from '../utils/app-error';
import { RateLimiterMemory } from 'rate-limiter-flexible';

const router = Router();

// Rate limiter for medical API calls (100 requests per minute per user)
const rateLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => (req as any).user?.id || req.ip,
  points: 100,
  duration: 60,
});

// Rate limiting middleware
const rateLimitMiddleware = async (req: Request, res: Response, next: Function) => {
  try {
    const key = (req as any).user?.id || req.ip;
    await rateLimiter.consume(key);
    next();
  } catch (rejRes) {
    res.status(429).json({
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.round(rejRes.msBeforeNext / 1000) || 1,
    });
  }
};

// Validation schemas
const searchSchema = z.object({
  term: z.string().min(2).max(100),
  limit: z.number().min(1).max(50).optional().default(20),
});

const conceptSchema = z.object({
  id: z.string().min(1),
});

/**
 * Search SNOMED CT concepts
 * GET /api/medical/snomed/search?term=diabetes&limit=20
 */
router.get('/snomed/search', 
  opalAuthMiddleware, 
  rateLimitMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { term, limit } = searchSchema.parse(req.query);
    
    const results = await medicalTerminologyService.searchSnomed(term, limit);
    
    res.json({
      success: true,
      data: {
        term,
        results,
        count: results.length,
        system: 'snomed'
      }
    });
  })
);

/**
 * Search RxNAV drugs
 * GET /api/medical/rxnav/search?term=aspirin&limit=20
 */
router.get('/rxnav/search', 
  opalAuthMiddleware, 
  rateLimitMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { term, limit } = searchSchema.parse(req.query);
    
    const results = await medicalTerminologyService.searchRxNav(term, limit);
    
    res.json({
      success: true,
      data: {
        term,
        results,
        count: results.length,
        system: 'rxnav'
      }
    });
  })
);

/**
 * Combined search across both terminologies
 * GET /api/medical/search?term=diabetes&limit=20
 */
router.get('/search', 
  opalAuthMiddleware, 
  rateLimitMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { term, limit } = searchSchema.parse(req.query);
    
    const results = await medicalTerminologyService.searchMedicalTerms(term, limit);
    
    res.json({
      success: true,
      data: {
        term,
        snomed: results.snomed,
        rxnav: results.rxnav,
        totalCount: results.snomed.length + results.rxnav.length
      }
    });
  })
);

/**
 * Get SNOMED concept details
 * GET /api/medical/snomed/concept/73211009
 */
router.get('/snomed/concept/:id', 
  opalAuthMiddleware, 
  rateLimitMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = conceptSchema.parse(req.params);
    
    const concept = await medicalTerminologyService.getSnomedConcept(id);
    
    if (!concept) {
      throw new AppError('SNOMED concept not found', 404);
    }
    
    res.json({
      success: true,
      data: concept
    });
  })
);

/**
 * Get RxNAV drug details
 * GET /api/medical/rxnav/drug/1191
 */
router.get('/rxnav/drug/:id', 
  opalAuthMiddleware, 
  rateLimitMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = conceptSchema.parse(req.params);
    
    const drug = await medicalTerminologyService.getRxNavDrug(id);
    
    if (!drug) {
      throw new AppError('RxNAV drug not found', 404);
    }
    
    res.json({
      success: true,
      data: drug
    });
  })
);

/**
 * Get medical code details (unified endpoint)
 * GET /api/medical/code/snomed:73211009 or GET /api/medical/code/rxnav:1191
 */
router.get('/code/:code', 
  opalAuthMiddleware, 
  rateLimitMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const code = req.params.code;
    
    if (!code.includes(':')) {
      throw new AppError('Invalid code format. Use system:code (e.g., snomed:73211009)', 400);
    }
    
    const [system, id] = code.split(':', 2);
    
    let result;
    if (system === 'snomed') {
      result = await medicalTerminologyService.getSnomedConcept(id);
    } else if (system === 'rxnav') {
      result = await medicalTerminologyService.getRxNavDrug(id);
    } else {
      throw new AppError('Unsupported terminology system. Use "snomed" or "rxnav"', 400);
    }
    
    if (!result) {
      throw new AppError(`${system.toUpperCase()} code not found`, 404);
    }
    
    res.json({
      success: true,
      data: {
        system,
        id,
        details: result
      }
    });
  })
);

/**
 * Health check endpoint
 * GET /api/medical/health
 */
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  // Test connectivity to external APIs
  const snomedTest = medicalTerminologyService.searchSnomed('test', 1);
  const rxnavTest = medicalTerminologyService.searchRxNav('test', 1);
  
  const [snomedResult, rxnavResult] = await Promise.allSettled([snomedTest, rxnavTest]);
  
  res.json({
    success: true,
    data: {
      snomed: {
        status: snomedResult.status === 'fulfilled' ? 'healthy' : 'error',
        error: snomedResult.status === 'rejected' ? snomedResult.reason?.message : null
      },
      rxnav: {
        status: rxnavResult.status === 'fulfilled' ? 'healthy' : 'error',
        error: rxnavResult.status === 'rejected' ? rxnavResult.reason?.message : null
      },
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * Clear cache endpoint (admin only)
 * POST /api/medical/cache/clear
 */
router.post('/cache/clear', 
  opalAuthMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const userRoles = (req as any).user.roles;
    
    if (!userRoles.includes('admin')) {
      throw new AppError('Admin access required', 403);
    }
    
    await medicalTerminologyService.cleanupExpiredCache();
    
    res.json({
      success: true,
      message: 'Cache cleared successfully'
    });
  })
);

export { router as medicalTerminologyRoutes };
