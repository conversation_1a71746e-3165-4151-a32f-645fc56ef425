import { Router, Request, Response } from 'express';
import { db } from '../db/index.js';
import { opalAuthMiddleware, requireResourceAccess } from '../middleware/opalAuth.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { sql, eq, and, like, or } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Interface definitions
interface SpecializationCategory {
  id: string;
  name: string;
  description?: string;
  parent_category_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface DoctorSpecialization {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category_id?: string;
  requires_board_certification: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  category?: SpecializationCategory;
}

interface DoctorSpecializationAssignment {
  id: string;
  doctor_id: string;
  specialization_id: string;
  is_primary: boolean;
  board_certified: boolean;
  certification_date?: string;
  certification_body?: string;
  years_experience: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  specialization?: DoctorSpecialization;
}

// GET /api/specializations/categories - Get all specialization categories
router.get('/categories', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const result = await db.execute(sql`
      SELECT 
        sc.*,
        parent.name as parent_category_name
      FROM specialization_categories sc
      LEFT JOIN specialization_categories parent ON sc.parent_category_id = parent.id
      WHERE sc.is_active = true
      ORDER BY sc.name
    `);

    res.json(result);
  } catch (error) {
    logger.error('Error fetching specialization categories:', error);
    throw new AppError('Failed to fetch specialization categories', 500);
  }
}));

// GET /api/specializations - Get all specializations with optional filtering
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const { category_id, requires_certification, search } = req.query;
    
    let query = sql`
      SELECT 
        ds.*,
        sc.name as category_name,
        sc.description as category_description
      FROM doctor_specializations ds
      LEFT JOIN specialization_categories sc ON ds.category_id = sc.id
      WHERE ds.is_active = true
    `;
    
    const conditions = [];

    if (category_id) {
      conditions.push(sql`ds.category_id = ${category_id}`);
    }

    if (requires_certification !== undefined) {
      conditions.push(sql`ds.requires_board_certification = ${requires_certification === 'true'}`);
    }

    if (search) {
      const searchTerm = `%${search}%`;
      conditions.push(sql`(ds.name ILIKE ${searchTerm} OR ds.description ILIKE ${searchTerm} OR ds.code ILIKE ${searchTerm})`);
    }

    if (conditions.length > 0) {
      query = sql`${query} AND ${sql.join(conditions, sql` AND `)}`;
    }

    query = sql`${query} ORDER BY ds.name`;

    const result = await db.execute(query);
    res.json(result);
  } catch (error) {
    logger.error('Error fetching specializations:', error);
    throw new AppError('Failed to fetch specializations', 500);
  }
}));

// GET /api/specializations/doctor/:doctorId - Get specializations for a specific doctor
router.get('/doctor/:doctorId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const { doctorId } = req.params;
    
    const result = await db.execute(sql`
      SELECT 
        dsa.*,
        ds.name as specialization_name,
        ds.code as specialization_code,
        ds.description as specialization_description,
        ds.requires_board_certification,
        sc.name as category_name,
        sc.description as category_description
      FROM doctor_specialization_assignments dsa
      JOIN doctor_specializations ds ON dsa.specialization_id = ds.id
      LEFT JOIN specialization_categories sc ON ds.category_id = sc.id
      WHERE dsa.doctor_id = ${doctorId} AND dsa.is_active = true
      ORDER BY dsa.is_primary DESC, ds.name
    `);

    res.json(result);
  } catch (error) {
    logger.error('Error fetching doctor specializations:', error);
    throw new AppError('Failed to fetch doctor specializations', 500);
  }
}));

// POST /api/specializations/doctor/:doctorId - Assign specialization to doctor
router.post('/doctor/:doctorId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const { doctorId } = req.params;
    const { 
      specialization_id, 
      is_primary = false, 
      board_certified = false, 
      certification_date, 
      certification_body, 
      years_experience = 0 
    } = req.body;

    const user = (req as any).user;
    const userRoles = user.roles || [user.role];

    // Check if user is admin or the doctor themselves
    if (userRoles.includes('admin') || user.id === doctorId) {
      // If setting as primary, unset other primary specializations
      if (is_primary) {
        await db.execute(sql`
          UPDATE doctor_specialization_assignments 
          SET is_primary = false, updated_at = CURRENT_TIMESTAMP
          WHERE doctor_id = ${doctorId} AND is_primary = true
        `);
      }

      // Insert new specialization assignment
      const result = await db.execute(sql`
        INSERT INTO doctor_specialization_assignments 
        (doctor_id, specialization_id, is_primary, board_certified, certification_date, certification_body, years_experience)
        VALUES (${doctorId}, ${specialization_id}, ${is_primary}, ${board_certified}, ${certification_date}, ${certification_body}, ${years_experience})
        RETURNING *
      `);

      // Audit log
      logAuditEvent(
        user.id,
        'SPECIALIZATION_ASSIGNED',
        'doctor_specialization',
        {
          doctor_id: doctorId,
          specialization_id,
          is_primary,
          board_certified,
          assigned_by: user.id,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );

      res.status(201).json(result[0]);
    } else {
      throw new AppError('Unauthorized to assign specializations', 403);
    }
  } catch (error) {
    logger.error('Error assigning specialization:', error);
    if (error.message?.includes('duplicate key') || error.message?.includes('unique constraint')) {
      throw new AppError('Doctor already has this specialization assigned', 409);
    } else {
      throw new AppError('Failed to assign specialization', 500);
    }
  }
}));

// PUT /api/specializations/doctor/:doctorId/:assignmentId - Update doctor specialization assignment
router.put('/doctor/:doctorId/:assignmentId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const { doctorId, assignmentId } = req.params;
    const { 
      is_primary, 
      board_certified, 
      certification_date, 
      certification_body, 
      years_experience 
    } = req.body;

    const user = (req as any).user;
    const userRoles = user.roles || [user.role];

    // Check if user is admin or the doctor themselves
    if (userRoles.includes('admin') || user.id === doctorId) {
      // If setting as primary, unset other primary specializations
      if (is_primary) {
        await db.execute(sql`
          UPDATE doctor_specialization_assignments 
          SET is_primary = false, updated_at = CURRENT_TIMESTAMP
          WHERE doctor_id = ${doctorId} AND is_primary = true AND id != ${assignmentId}
        `);
      }

      // Build update query dynamically
      const updates = [];
      if (is_primary !== undefined) updates.push(sql`is_primary = ${is_primary}`);
      if (board_certified !== undefined) updates.push(sql`board_certified = ${board_certified}`);
      if (certification_date !== undefined) updates.push(sql`certification_date = ${certification_date}`);
      if (certification_body !== undefined) updates.push(sql`certification_body = ${certification_body}`);
      if (years_experience !== undefined) updates.push(sql`years_experience = ${years_experience}`);
      
      if (updates.length === 0) {
        throw new AppError('No fields to update', 400);
      }

      updates.push(sql`updated_at = CURRENT_TIMESTAMP`);

      // Update the assignment
      const result = await db.execute(sql`
        UPDATE doctor_specialization_assignments 
        SET ${sql.join(updates, sql`, `)}
        WHERE id = ${assignmentId} AND doctor_id = ${doctorId}
        RETURNING *
      `);

      if (result.length === 0) {
        throw new AppError('Specialization assignment not found', 404);
      }

      // Audit log
      logAuditEvent(
        user.id,
        'SPECIALIZATION_UPDATED',
        'doctor_specialization',
        {
          doctor_id: doctorId,
          assignment_id: assignmentId,
          changes: req.body,
          updated_by: user.id,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );

      res.json(result[0]);
    } else {
      throw new AppError('Unauthorized to update specializations', 403);
    }
  } catch (error) {
    logger.error('Error updating specialization assignment:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('Failed to update specialization assignment', 500);
  }
}));

// DELETE /api/specializations/doctor/:doctorId/:assignmentId - Remove doctor specialization assignment
router.delete('/doctor/:doctorId/:assignmentId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  try {
    const { doctorId, assignmentId } = req.params;
    const user = (req as any).user;
    const userRoles = user.roles || [user.role];

    // Check if user is admin or the doctor themselves
    if (userRoles.includes('admin') || user.id === doctorId) {
      // Soft delete the assignment
      const result = await db.execute(sql`
        UPDATE doctor_specialization_assignments 
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE id = ${assignmentId} AND doctor_id = ${doctorId}
        RETURNING *
      `);

      if (result.length === 0) {
        throw new AppError('Specialization assignment not found', 404);
      }

      // Audit log
      logAuditEvent(
        user.id,
        'SPECIALIZATION_REMOVED',
        'doctor_specialization',
        {
          doctor_id: doctorId,
          assignment_id: assignmentId,
          removed_by: user.id,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );

      res.json({ message: 'Specialization assignment removed successfully' });
    } else {
      throw new AppError('Unauthorized to remove specializations', 403);
    }
  } catch (error) {
    logger.error('Error removing specialization assignment:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('Failed to remove specialization assignment', 500);
  }
}));

// Admin-only routes for managing specializations and categories

// POST /api/specializations - Create new specialization (Admin only)
router.post('/', opalAuthMiddleware, requireResourceAccess('specializations', 'create'), asyncHandler(async (req: Request, res: Response) => {
  try {
    const { name, code, description, category_id, requires_board_certification = false } = req.body;
    const user = (req as any).user;

    const result = await db.execute(sql`
      INSERT INTO doctor_specializations (name, code, description, category_id, requires_board_certification)
      VALUES (${name}, ${code}, ${description}, ${category_id}, ${requires_board_certification})
      RETURNING *
    `);

    // Audit log
    logAuditEvent(
      user.id,
      'SPECIALIZATION_CREATED',
      'doctor_specialization',
      {
        name,
        code,
        category_id,
        created_by: user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.status(201).json(result[0]);
  } catch (error) {
    logger.error('Error creating specialization:', error);
    if (error.message?.includes('duplicate key') || error.message?.includes('unique constraint')) {
      throw new AppError('Specialization name or code already exists', 409);
    } else {
      throw new AppError('Failed to create specialization', 500);
    }
  }
}));

// PUT /api/specializations/:id - Update specialization (Admin only)
router.put('/:id', opalAuthMiddleware, requireResourceAccess('specializations', 'update'), asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, code, description, category_id, requires_board_certification, is_active } = req.body;
    const user = (req as any).user;

    // Build update query dynamically
    const updates = [];
    if (name !== undefined) updates.push(sql`name = ${name}`);
    if (code !== undefined) updates.push(sql`code = ${code}`);
    if (description !== undefined) updates.push(sql`description = ${description}`);
    if (category_id !== undefined) updates.push(sql`category_id = ${category_id}`);
    if (requires_board_certification !== undefined) updates.push(sql`requires_board_certification = ${requires_board_certification}`);
    if (is_active !== undefined) updates.push(sql`is_active = ${is_active}`);
    
    if (updates.length === 0) {
      throw new AppError('No fields to update', 400);
    }

    updates.push(sql`updated_at = CURRENT_TIMESTAMP`);

    const result = await db.execute(sql`
      UPDATE doctor_specializations 
      SET ${sql.join(updates, sql`, `)}
      WHERE id = ${id}
      RETURNING *
    `);

    if (result.length === 0) {
      throw new AppError('Specialization not found', 404);
    }

    // Audit log
    logAuditEvent(
      user.id,
      'SPECIALIZATION_UPDATED',
      'doctor_specialization',
      {
        specialization_id: id,
        changes: req.body,
        updated_by: user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json(result[0]);
  } catch (error) {
    logger.error('Error updating specialization:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('Failed to update specialization', 500);
  }
}));

// POST /api/specializations/categories - Create new category (Admin only)
router.post('/categories', opalAuthMiddleware, requireResourceAccess('specializations', 'create'), asyncHandler(async (req: Request, res: Response) => {
  try {
    const { name, description, parent_category_id } = req.body;
    const user = (req as any).user;

    const result = await db.execute(sql`
      INSERT INTO specialization_categories (name, description, parent_category_id)
      VALUES (${name}, ${description}, ${parent_category_id})
      RETURNING *
    `);

    // Audit log
    logAuditEvent(
      user.id,
      'SPECIALIZATION_CATEGORY_CREATED',
      'specialization_category',
      {
        name,
        parent_category_id,
        created_by: user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.status(201).json(result[0]);
  } catch (error) {
    logger.error('Error creating specialization category:', error);
    if (error.message?.includes('duplicate key') || error.message?.includes('unique constraint')) {
      throw new AppError('Category name already exists', 409);
    } else {
      throw new AppError('Failed to create specialization category', 500);
    }
  }
}));

export default router;