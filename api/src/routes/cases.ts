import express, { Request, Response, Router } from 'express';
import { extractUserId, extractUserRoles, extractPrimaryRole, logUserRoleInfo } from '../utils/userRoleExtractor.js';

import { z } from 'zod';
import { eq, and, desc, inArray } from 'drizzle-orm';

import { db } from '../db/index.js';
import { cases, medicalDocuments } from '../db/schema/cases.js';
import { caseDoctors } from '../db/schema/case-doctors.js';
import { caseNotes } from '../db/schema/case-notes.js';
import { noteTypes } from '../db/schema/note-types.js';
import { users, userProfiles } from '../db/schema/users.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { opalAuthMiddleware, requireCaseRead, requireCaseWrite } from '../middleware/opalAuth.js';
import { logAuditEvent } from '../utils/logger.js';
import { CaseDoctorService, CaseDoctorAssignment } from '../services/caseDoctorService.js';
import { requireDoctorAcceptance } from '../middleware/doctorAcceptanceMiddleware.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Validation schemas - now includes clinical data fields
const createCaseSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  urgencyLevel: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  specialtyRequired: z.string().optional(),
  // Clinical data fields - will be stored in case_notes table
  symptoms: z.string().optional(),
  medicalHistory: z.string().optional(),
  currentMedications: z.string().optional(),
  caseDescription: z.string().optional(),
});

// Relaxed validation schema for drafts - includes clinical data fields
const createDraftCaseSchema = z.object({
  title: z.string().min(1, 'Case title is required'),
  urgencyLevel: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  specialtyRequired: z.string().optional(),
  // Clinical data fields - will be stored in case_notes table
  symptoms: z.string().optional(),
  medicalHistory: z.string().optional(),
  currentMedications: z.string().optional(),
  caseDescription: z.string().optional(),
  isDraft: z.boolean().optional(), // Flag to indicate draft status
});

const updateCaseSchema = z.object({
  title: z.string().min(1).optional(),
  urgencyLevel: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  specialtyRequired: z.string().optional(),
  status: z.enum(['draft', 'submitted', 'in_review', 'assigned', 'completed', 'cancelled']).optional(),
  // Note: structuredContent removed - clinical data is stored in case_notes table
});

// Create a new case
router.post('/', opalAuthMiddleware, requireCaseWrite, asyncHandler(async (req: Request, res: Response) => {
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Create case route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Create case route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  // Use different validation schema based on whether it's a draft
  const isDraft = req.body.isDraft === true;
  const validatedData = isDraft 
    ? createDraftCaseSchema.parse(req.body)
    : createCaseSchema.parse(req.body);

  const [newCase] = await db
    .insert(cases)
    .values({
      patientId: userId,
      title: validatedData.title,
      urgencyLevel: validatedData.urgencyLevel,
      specialtyRequired: validatedData.specialtyRequired,
      status: 'draft',
    })
    .returning();

  // Create initial clinical notes if clinical data is provided
  const clinicalData = {
    symptoms: validatedData.symptoms,
    pastMedicalHistory: validatedData.medicalHistory,
    currentMedications: validatedData.currentMedications,
    caseDescription: validatedData.caseDescription,
  };

  // Filter out empty/undefined clinical data
  const filteredClinicalData = Object.fromEntries(
    Object.entries(clinicalData).filter(([_, value]) => value && value.trim() !== '')
  );

  // If there's clinical data, create an initial clinical note
  if (Object.keys(filteredClinicalData).length > 0) {
    const clinicalNotesTypeId = 'c6867d50-6dc7-443d-aa14-061ab153d685'; // Clinical Notes type

    await db
      .insert(caseNotes)
      .values({
        caseId: newCase.id,
        doctorId: userId, // Patient creates initial note
        noteTypeId: clinicalNotesTypeId,
        structuredContent: filteredClinicalData,
        version: 1,
        isActive: true,
        // Initialize collaborative editing fields
        yjsDocument: null, // Will be populated when collaborative editing starts
        canvasBlocks: [],
        medicalCodes: {},
        activeEditors: [],
        documentVersion: 1,
        collaborationEnabled: true,
      });
  }

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_CREATED',
    'cases',
    {
      caseId: newCase.id,
      urgencyLevel: newCase.urgencyLevel,
      hasInitialNotes: Object.keys(filteredClinicalData).length > 0,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.status(201).json({
    message: 'Case created successfully',
    case: newCase,
  });
}));

// Get all cases for current user
router.get('/', opalAuthMiddleware, requireCaseRead, asyncHandler(async (req: Request, res: Response) => {
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'guest');
  
  // Log user role information for debugging
  logger.info('Cases list route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Cases list route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });
  
  // Extract query parameters
  const { status, priority, search, page, limit } = req.query;

  let userCases: any[] = [];

  if (userRole === 'patient') {
    // Patients can only see their own cases with assignment information for UI filtering
    let whereConditions = [eq(cases.patientId, userId)];
    
    // Apply status filter if provided
    if (status) {
      whereConditions.push(eq(cases.status, status as any));
    }
    
    userCases = await db
      .select({
        id: cases.id,
        patientId: cases.patientId,
        title: cases.title,
        urgencyLevel: cases.urgencyLevel,
        specialtyRequired: cases.specialtyRequired,
        status: cases.status,
        submittedAt: cases.submittedAt,
        assignedAt: cases.assignedAt,
        completedAt: cases.completedAt,
        createdAt: cases.createdAt,
        updatedAt: cases.updatedAt,
        // Include doctor assignment information for UI filtering (may be null for unassigned cases)
        doctorAssignedAt: caseDoctors.assignedAt,
        doctorAssignedBy: caseDoctors.assignedBy,
        // Include doctor acceptance status for UI filtering (may be null for unassigned cases)
        doctorAcceptanceStatus: caseDoctors.acceptanceStatus,
        doctorAcceptedAt: caseDoctors.acceptedAt,
        // Include additional doctor activity information for UI filtering (may be null for unassigned cases)
        doctorTimeSpentMinutes: caseDoctors.timeSpentMinutes,
        doctorLastActivityAt: caseDoctors.lastActivityAt,
        doctorNotes: caseDoctors.notes,
      })
      .from(cases)
      .leftJoin(caseDoctors, and(
        eq(caseDoctors.caseId, cases.id),
        eq(caseDoctors.isActive, true)
      ))
      .where(and(...whereConditions))
      .orderBy(desc(cases.createdAt));
  } else if (userRole === 'doctor') {
    // Doctors can see cases assigned to them using the multi-doctor assignment system
    // Get all case IDs assigned to this doctor
    const assignedCaseIds = await CaseDoctorService.getCasesForDoctor(userId);
    
    if (assignedCaseIds.length === 0) {
      userCases = [];
    } else {
      // Create base query with doctor's acceptance status
      let whereConditions = [inArray(cases.id, assignedCaseIds)];
      
      // Apply status filter if provided
      if (status) {
        // Explicitly cast status to a valid case status type to fix TypeScript error
        const validStatus = status as 'draft' | 'submitted' | 'in_review' | 'assigned' | 'completed' | 'cancelled';
        whereConditions.push(eq(cases.status, validStatus));
      }
      
      // Get cases with doctor assignment information
      userCases = await db
        .select({
          id: cases.id,
          patientId: cases.patientId,
          title: cases.title,
          urgencyLevel: cases.urgencyLevel,
          specialtyRequired: cases.specialtyRequired,
          status: cases.status,
          submittedAt: cases.submittedAt,
          assignedAt: cases.assignedAt,
          completedAt: cases.completedAt,
          createdAt: cases.createdAt,
          updatedAt: cases.updatedAt,
          // Include doctor assignment information for UI filtering
          doctorAssignedAt: caseDoctors.assignedAt,
          doctorAssignedBy: caseDoctors.assignedBy,
          // Include doctor acceptance status for UI filtering
          doctorAcceptanceStatus: caseDoctors.acceptanceStatus,
          doctorAcceptedAt: caseDoctors.acceptedAt,
          // Include additional doctor activity information for UI filtering
          doctorTimeSpentMinutes: caseDoctors.timeSpentMinutes,
          doctorLastActivityAt: caseDoctors.lastActivityAt,
          doctorNotes: caseDoctors.notes,
        })
        .from(cases)
        .innerJoin(caseDoctors, and(
          eq(caseDoctors.caseId, cases.id),
          eq(caseDoctors.doctorId, userId),
          eq(caseDoctors.isActive, true)
        ))
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
        .orderBy(desc(cases.createdAt))
        .execute();
    }
  } else if (userRole === 'agent' || userRole === 'admin') {
    // Agents and admins can see all cases with assignment information for filtering
    let whereConditions = [];
    
    // Apply status filter if provided
    if (status) {
      // Explicitly cast status to a valid case status type to fix TypeScript error
      const validStatus = status as 'draft' | 'submitted' | 'in_review' | 'assigned' | 'completed' | 'cancelled';
      whereConditions.push(eq(cases.status, validStatus));
    }
    
    // Get all cases with doctor assignment information for UI filtering
    userCases = await db
      .select({
        id: cases.id,
        patientId: cases.patientId,
        title: cases.title,
        urgencyLevel: cases.urgencyLevel,
        specialtyRequired: cases.specialtyRequired,
        status: cases.status,
        submittedAt: cases.submittedAt,
        assignedAt: cases.assignedAt,
        completedAt: cases.completedAt,
        createdAt: cases.createdAt,
        updatedAt: cases.updatedAt,
        // Include doctor assignment information for UI filtering (may be null for unassigned cases)
        doctorAssignedAt: caseDoctors.assignedAt,
        doctorAssignedBy: caseDoctors.assignedBy,
        // Include doctor acceptance status for UI filtering (may be null for unassigned cases)
        doctorAcceptanceStatus: caseDoctors.acceptanceStatus,
        doctorAcceptedAt: caseDoctors.acceptedAt,
        // Include additional doctor activity information for UI filtering (may be null for unassigned cases)
        doctorTimeSpentMinutes: caseDoctors.timeSpentMinutes,
        doctorLastActivityAt: caseDoctors.lastActivityAt,
        doctorNotes: caseDoctors.notes,
      })
      .from(cases)
      .leftJoin(caseDoctors, and(
        eq(caseDoctors.caseId, cases.id),
        eq(caseDoctors.isActive, true)
      ))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(desc(cases.createdAt))
      .execute();
  } else {
    throw new AppError('Unauthorized to view cases', 403);
  }

  // Log audit event for HIPAA compliance - case access must be tracked
  logAuditEvent(
    userId,
    'CASES_ACCESSED',
    'cases',
    {
      userRole,
      casesCount: userCases.length,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    cases: userCases,
    total: userCases.length,
  });
}));

// Get a specific case by ID
router.get('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Case detail route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Case detail route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });
  
  // Check if doctor is assigned to case using case_doctors table - NO FALLBACK
  let isDoctorAssigned = false;
  if (userRoles?.includes('doctor')) {
    logger.info('Checking if doctor ${userId} is assigned to case ${caseId}', { requestId: 'context-needed' }, { data: undefined });
    isDoctorAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
    logger.info(`Doctor ${userId} assignment check for case ${caseId}: ${isDoctorAssigned ? 'ASSIGNED' : 'NOT ASSIGNED'}`, { requestId: 'context-needed' }, { data: undefined });
  }

  const [caseData] = await db
    .select({
      id: cases.id,
      patientId: cases.patientId,
      title: cases.title,
      urgencyLevel: cases.urgencyLevel,
      specialtyRequired: cases.specialtyRequired,
      status: cases.status,
      createdAt: cases.createdAt,
      updatedAt: cases.updatedAt,
      submittedAt: cases.submittedAt,
      assignedAt: cases.assignedAt,
      completedAt: cases.completedAt,
      // Patient demographic information
      patientGender: userProfiles.gender,
      patientDateOfBirth: userProfiles.dateOfBirth,
      patientCountry: userProfiles.country,
    })
    .from(cases)
    .leftJoin(userProfiles, eq(cases.patientId, userProfiles.userId))
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check authorization using multi-doctor assignment system
  const canAccess = 
    userRoles?.includes('admin') ||
    userRoles?.includes('agent') ||
    (userRoles?.includes('patient') && caseData.patientId === userId) ||
    (userRoles?.includes('doctor') && isDoctorAssigned);

  if (!canAccess) {
    throw new AppError('Unauthorized to access this case', 403);
  }

  // Get associated documents
  const documents = await db
    .select()
    .from(medicalDocuments)
    .where(eq(medicalDocuments.caseId, caseId))
    .orderBy(desc(medicalDocuments.createdAt))
    .execute(); // Add execute() to fix PgSelectBase error

  // Log audit event for HIPAA compliance - individual case access must be tracked
  logAuditEvent(
    userId,
    'CASE_ACCESSED',
    'cases',
    {
      caseId: caseData.id,
      patientId: caseData.patientId,
      userRole,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  // Get assigned doctors for this case using the multi-doctor system
  let assignedDoctorData: CaseDoctorAssignment[] = [];
  try {
    logger.debug(`Fetching assigned doctors for case ${caseId}`);
    assignedDoctorData = await CaseDoctorService.getAssignedDoctors(caseId) || [];
    logger.debug(`Found ${assignedDoctorData.length} assigned doctors for case ${caseId}`);
  } catch (error) {
    logger.error('Error fetching doctors for case ${caseId}:', error);
    // Continue with empty array if there's an error
  }

  // Format response
  const response = {
    ...caseData,
    assignedDoctors: Array.isArray(assignedDoctorData) ? assignedDoctorData.map(assignment => {
      // Enhanced debug logging for doctor assignment
      logger.debug('Debug output', undefined, { data: JSON.stringify({
        message: 'Doctor assignment details',
        id: assignment.id,
        doctorId: assignment.doctorId,
        hasDoctor: !!assignment.doctor,
        doctorFields: assignment.doctor ? Object.keys(assignment.doctor) : [],
        firstName: assignment.doctor?.firstName,
        lastName: assignment.doctor?.lastName
      }) });
      
      // Create a better fallback for doctor name
      let doctorName = 'Unknown';
      if (assignment.doctor) {
        if (assignment.doctor.firstName && assignment.doctor.lastName) {
          doctorName = `${assignment.doctor.firstName} ${assignment.doctor.lastName}`.trim();
        } else if (assignment.doctor.firstName) {
          doctorName = assignment.doctor.firstName;
        } else if (assignment.doctor.lastName) {
          doctorName = assignment.doctor.lastName;
        }
      }
      
      // If still unknown but we have a doctorId, log it for debugging
      if (doctorName === 'Unknown' && assignment.doctorId) {
        logger.debug(`Doctor name is unknown for ID: ${assignment.doctorId}`);
      }
      
      return {
        id: assignment.doctorId || '',
        name: doctorName,
        specialization: assignment.doctor?.specialization || '',
        assignedAt: assignment.assignedAt || new Date()
      };
    }) : [],
    documents: documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      fileName: doc.fileName,
      createdAt: doc.createdAt
    }))
  };

  res.json({
    case: response,
    documents: response.documents,
  });
}));

// Update a case
router.put('/:id', opalAuthMiddleware, requireCaseWrite, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Extract user data from OPAL
  const userId = opalUser?.id;
  const userRoles = opalUser?.roles;
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  const user = opalUser;
  
  // Log user role information for debugging
  logger.info('Update case route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Update case route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });
  const validatedData = updateCaseSchema.parse(req.body);

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }


  // Define field restrictions for patients
  let updateData = { ...validatedData };
  
  if (userRole === 'patient') {
    // Patients can only edit specific fields - legacy clinical fields moved to structured notes
    const allowedPatientFields = ['title', 'urgencyLevel', 'specialtyRequired', 'status'];
    // Note: structuredContent removed - clinical data is in case_notes table
    
    // Special handling for status field - patients can only change from draft to submitted
    if (updateData.status && updateData.status !== 'submitted') {
      delete updateData.status; // Only allow setting status to 'submitted'
    }
    
    if (updateData.status === 'submitted' && existingCase.status !== 'draft') {
      delete updateData.status; // Only allow changing from 'draft' to 'submitted'
    }
    
    // Remove any fields that patients are not allowed to edit
    updateData = Object.keys(updateData)
      .filter(key => allowedPatientFields.includes(key))
      .reduce<Record<string, any>>((obj, key) => {
        // Use type assertion to fix TypeScript error with string indexing
        obj[key] = (updateData as Record<string, any>)[key];
        return obj;
      }, {} as any);
    
    // Log if patient tried to update restricted fields
    const restrictedFields = Object.keys(validatedData).filter(key => !allowedPatientFields.includes(key));
    if (restrictedFields.length > 0) {
      logAuditEvent(
        userId,
        'CASE_UPDATE_RESTRICTED_FIELDS_ATTEMPTED',
        'cases',
        {
          caseId,
          restrictedFields,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );
    }
  }

  const [updatedCase] = await db
    .update(cases)
    .set({
      ...updateData,
      updatedAt: new Date(),
    })
    .where(eq(cases.id, caseId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_UPDATED',
    'cases',
    {
      caseId: updatedCase.id,
      updatedFields: Object.keys(validatedData),
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Case updated successfully',
    case: updatedCase,
  });
}));

// Submit a case for review
router.post('/:id/submit', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Submit case route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Submit case route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }

  // Only patients can submit their own cases
  if (!userRoles?.includes('patient') || existingCase.patientId !== userId) {
    throw new AppError('Unauthorized to submit this case', 403);
  }

  if (existingCase.status !== 'draft') {
    throw new AppError('Only draft cases can be submitted', 400);
  }

  const [updatedCase] = await db
    .update(cases)
    .set({
      status: 'submitted',
      submittedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(cases.id, caseId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_SUBMITTED',
    'cases',
    {
      caseId: updatedCase.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Case submitted successfully',
    case: updatedCase,
  });
}));

// Assign a case to a doctor (agent/admin only)
router.post('/:id/assign', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  const { doctorId } = req.body;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Assign case route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Assign case route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  // Check if user has agent, admin, or doctor role (doctors can assign other doctors)
  if (!userRoles?.includes('agent') && !userRoles?.includes('admin') && !userRoles?.includes('doctor')) {
    throw new AppError('Only agents, admins, and doctors can assign cases', 403);
  }

  // If user is a doctor, they can assign to cases they have access to (assigned, working on, or can view)
  if (userRoles?.includes('doctor') && !userRoles?.includes('admin') && !userRoles?.includes('agent')) {
    // Check if doctor has any relationship with this case
    const isDoctorAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
    const doctorCases = await CaseDoctorService.getCasesForDoctor(userId);
    const hasAccessToCase = isDoctorAssigned || doctorCases.includes(caseId);
    
    if (!hasAccessToCase) {
      throw new AppError('You can only assign doctors to cases you are working on or have access to', 403);
    }
  }

  if (!doctorId) {
    throw new AppError('Doctor ID is required', 400);
  }

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }

  // Check if doctor is already assigned to avoid duplicates
  const existingAssignment = await db
    .select()
    .from(caseDoctors)
    .where(
      and(
        eq(caseDoctors.caseId, caseId),
        eq(caseDoctors.doctorId, doctorId),
        eq(caseDoctors.isActive, true)
      )
    )
    .limit(1);

  if (existingAssignment.length > 0) {
    throw new AppError('Doctor is already assigned to this case', 400);
  }
  
  // Add the new doctor assignment using the service (don't remove existing ones)
  await CaseDoctorService.assignDoctorToCase(caseId, doctorId, userId);
  
  // Check if any doctors have already accepted this case
  const acceptedDoctors = await db
    .select()
    .from(caseDoctors)
    .where(
      and(
        eq(caseDoctors.caseId, caseId),
        eq(caseDoctors.acceptanceStatus, 'accepted'),
        eq(caseDoctors.isActive, true)
      )
    );

  // Only update case status to 'assigned' if no doctors have accepted it yet
  // If doctors have already accepted, keep the current status (likely 'in_review')
  let caseUpdateData: any = {
    updatedAt: new Date(),
  };

  if (acceptedDoctors.length === 0) {
    // No doctors have accepted yet, so we can set status to 'assigned'
    caseUpdateData.status = 'assigned';
    caseUpdateData.assignedAt = new Date();
  }
  // If doctors have already accepted, don't change the status

  const [updatedCase] = await db
    .update(cases)
    .set(caseUpdateData)
    .where(eq(cases.id, caseId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_ASSIGNED',
    'cases',
    {
      caseId: updatedCase.id,
      doctorId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Case assigned successfully',
    case: updatedCase,
  });
}));

// Accept a case (doctor only) - per-doctor acceptance
router.post('/:id/accept', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Accept case route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Accept case route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  // Only doctors can accept cases
  if (!userRoles?.includes('doctor')) {
    throw new AppError('Only doctors can accept cases', 403);
  }

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }

  // Check if doctor is assigned to this case
  const [doctorAssignment] = await db
    .select()
    .from(caseDoctors)
    .where(
      and(
        eq(caseDoctors.caseId, caseId),
        eq(caseDoctors.doctorId, userId),
        eq(caseDoctors.isActive, true)
      )
    )
    .limit(1);

  if (!doctorAssignment) {
    throw new AppError('You are not assigned to this case', 403);
  }

  // Check if doctor has already accepted this case
  if (doctorAssignment.acceptanceStatus === 'accepted') {
    throw new AppError('You have already accepted this case', 400);
  }

  // Only assigned cases can be accepted
  if (existingCase.status !== 'assigned' && existingCase.status !== 'in_review') {
    throw new AppError('Only assigned cases can be accepted', 400);
  }

  // Update doctor's acceptance status
  await db
    .update(caseDoctors)
    .set({
      acceptanceStatus: 'accepted',
      acceptedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(caseDoctors.id, doctorAssignment.id));

  // Update case status to 'in_review' only if it's not already there
  let caseUpdateData: any = {
    updatedAt: new Date(),
  };

  if (existingCase.status === 'assigned') {
    caseUpdateData.status = 'in_review';
  }

  const [updatedCase] = await db
    .update(cases)
    .set(caseUpdateData)
    .where(eq(cases.id, caseId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_ACCEPTED_BY_DOCTOR',
    'cases',
    {
      caseId: updatedCase.id,
      doctorId: userId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Case accepted successfully',
    case: updatedCase,
  });
}));

// Get doctor acceptance status for a case
router.get('/:id/doctor-acceptance', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  const userId = opalUser?.id;
  const userRoles = opalUser?.roles || [];
  
  // Only doctors need to check acceptance status
  if (!userRoles.includes('doctor')) {
    return res.json({
      isDoctor: false,
      hasAccepted: true, // Non-doctors don't need acceptance
      message: 'Not a doctor - no acceptance required'
    });
  }
  
  if (!userId) {
    throw new AppError('User ID is required', 401);
  }
  
  try {
    // Check if doctor is assigned to the case
    const isAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
    
    if (!isAssigned) {
      return res.json({
        isDoctor: true,
        isAssigned: false,
        hasAccepted: false,
        message: 'Doctor is not assigned to this case'
      });
    }
    
    // Check if doctor has accepted the case
    const hasAccepted = await CaseDoctorService.hasDoctorAcceptedCase(userId, caseId);
    
    res.json({
      isDoctor: true,
      isAssigned: true,
      hasAccepted,
      message: hasAccepted ? 'Doctor has accepted the case' : 'Doctor has not accepted the case yet'
    });
    
  } catch (error) {
    logger.error('Error checking doctor acceptance status:', error);
    res.json({
      isDoctor: true,
      isAssigned: false,
      hasAccepted: false,
      message: 'Error checking acceptance status'
    });
  }
}));

// Get structured content for a case
router.get('/:id/structured-content', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Get structured content route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Get structured content route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });
  
  // Check if doctor is assigned to case using case_doctors table
  let isDoctorAssigned = false;
  if (userRoles?.includes('doctor')) {
    isDoctorAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
  }

  const [caseData] = await db
    .select({
      id: cases.id,
      patientId: cases.patientId,
      // Note: structuredContent removed - clinical data is in case_notes table
    })
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  const canAccess = 
    (userRole === 'admin') ||
    (userRole === 'agent') ||
    (userRole === 'patient' && caseData.patientId === userId) ||
    (userRole === 'doctor' && isDoctorAssigned);

  if (!canAccess) {
    throw new AppError('Unauthorized to access this case', 403);
  }

  // Fetch clinical notes for this case
  const notes = await db
    .select({
      id: caseNotes.id,
      structuredContent: caseNotes.structuredContent,
      // rawContent field removed
    })
    .from(caseNotes)
    .where(eq(caseNotes.caseId, caseId))
    .orderBy(desc(caseNotes.createdAt));

  // Get the most recent note's structured content
  const structuredContent = notes.length > 0 ? notes[0].structuredContent || {} : {};

  res.json({
    structuredContent,
  });
}));

// Get a specific section of case clinical notes
router.get('/:id/structured-content/:section', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  const section = req.params.section;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Get structured content route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Get structured content route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  // First check case access permissions
  const [caseData] = await db
    .select({
      id: cases.id,
      patientId: cases.patientId,
    })
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  const canAccess = 
    (userRole === 'admin') ||
    (userRole === 'agent') ||
    (userRole === 'patient' && caseData.patientId === userId) ||
    (userRole === 'doctor' && await CaseDoctorService.isDoctorAssignedToCase(userId, caseId));

  if (!canAccess) {
    throw new AppError('Unauthorized to access this case', 403);
  }

  // Fetch clinical notes for this case
  const notes = await db
    .select({
      id: caseNotes.id,
      structuredContent: caseNotes.structuredContent,
      // rawContent field removed
    })
    .from(caseNotes)
    .where(eq(caseNotes.caseId, caseId))
    .orderBy(desc(caseNotes.createdAt));

  // Extract the requested section from clinical notes
  let sectionData = '';
  let structuredData = {};
  
  if (notes.length > 0) {
    // Get the most recent note's structured content
    const latestNote = notes[0];
    const noteStructuredContent = latestNote.structuredContent || {};
    
    // Map common section names to structured content fields
    const sectionMapping: Record<string, string> = {
      'medicalHistory': 'pastMedicalHistory',
      'currentMedications': 'medications',
      'caseDescription': 'historyOfPresentIllness',
      'physicalExam': 'physicalExam',
      'assessment': 'assessment',
      'plan': 'plan'
    };
    
    const mappedField = sectionMapping[section] || section;
    // Use type assertion to avoid TypeScript error with string indexing
    sectionData = (noteStructuredContent as Record<string, any>)[mappedField] || '';
    structuredData = noteStructuredContent;
  }

  const responseData = {
    section,
    content: sectionData,
    // Only return full structuredData if specifically requested, otherwise just the section data
    structuredData: section === 'all' ? structuredData : { [section]: sectionData }
  };

  res.json(responseData);
}));

// Get all clinical notes for a case (optimized single call)
router.get('/:id/notes', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Get all notes route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Get all notes route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  // First check case access permissions
  const [caseData] = await db
    .select({
      id: cases.id,
      patientId: cases.patientId,
    })
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  const canAccess = 
    (userRole === 'admin') ||
    (userRole === 'agent') ||
    (userRole === 'patient' && caseData.patientId === userId) ||
    (userRole === 'doctor' && await CaseDoctorService.isDoctorAssignedToCase(userId, caseId));

  if (!canAccess) {
    throw new AppError('Unauthorized to access this case', 403);
  }

  // Fetch clinical notes for this case
  const notes = await db
    .select({
      id: caseNotes.id,
      structuredContent: caseNotes.structuredContent,
      // rawContent field removed
    })
    .from(caseNotes)
    .where(eq(caseNotes.caseId, caseId))
    .orderBy(desc(caseNotes.createdAt));

  // Simplified response format - extract content directly from structured data
  let clinicalData = {};
  
  if (notes.length > 0) {
    const latestNote = notes[0];
    const structured = latestNote.structuredContent as Record<string, any> || {};
    
    // Simple format: extract content strings directly
    clinicalData = {
      symptoms: typeof structured.symptoms === 'object' ? structured.symptoms?.content || '' : structured.symptoms || '',
      medicalHistory: typeof structured.medicalHistory === 'object' ? structured.medicalHistory?.content || '' : structured.medicalHistory || structured.pastMedicalHistory || '',
      currentMedications: typeof structured.currentMedications === 'object' ? structured.currentMedications?.content || '' : structured.currentMedications || '',
      caseDescription: typeof structured.caseDescription === 'object' ? structured.caseDescription?.content || '' : structured.caseDescription || '',
    };
  }

  const responseData = {
    caseId,
    notesCount: notes.length,
    clinicalData,
    lastUpdated: notes.length > 0 ? notes[0].id : null
  };

  res.json(responseData);
}));

// Update a specific section of case structured content
router.put('/:id/structured-content/:section', opalAuthMiddleware, requireDoctorAcceptance, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  const section = req.params.section;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Update structured content route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Update structured content route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });
  
  const { content, structuredData } = req.body;

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }

  // Enhanced authorization logic with proper audit logging
  let canUpdate = false;
  let authorizationReason = '';

  // Check if user is admin - admins can always update
  if (userRoles?.includes('admin')) {
    canUpdate = true;
    authorizationReason = 'admin_access';
  }
  // Check if user is a doctor assigned to this case
  else if (userRoles?.includes('doctor')) {
    const isDoctorAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
    if (isDoctorAssigned) {
      canUpdate = true;
      authorizationReason = 'doctor_assigned';
    } else {
      authorizationReason = 'doctor_not_assigned';
    }
  }
  // Check if user is patient and owns the case
  else if (userRoles?.includes('patient')) {
    if (existingCase.patientId === userId) {
      // Patients can only update their own cases if they are in draft status
      if (existingCase.status === 'draft') {
        canUpdate = true;
        authorizationReason = 'patient_own_draft';
      } else {
        authorizationReason = 'patient_own_not_draft';
      }
    } else {
      authorizationReason = 'patient_not_owner';
    }
  }
  else {
    authorizationReason = 'insufficient_role';
  }

  // Log authorization attempt for audit purposes
  logAuditEvent(
    userId || '',
    canUpdate ? 'CASE_NOTE_UPDATE_AUTHORIZED' : 'CASE_NOTE_UPDATE_DENIED',
    'cases',
    {
      caseId: existingCase.id,
      patientId: existingCase.patientId,
      section,
      userRole,
      authorizationReason,
      caseStatus: existingCase.status,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  if (!canUpdate) {
    // Provide specific error messages based on the reason
    let errorMessage = 'Unauthorized to update this case';
    switch (authorizationReason) {
      case 'doctor_not_assigned':
        errorMessage = 'You are not assigned to this case. Contact an administrator for access.';
        break;
      case 'patient_own_not_draft':
        errorMessage = 'Case notes can only be edited while the case is in draft status. This case has been submitted and is now read-only.';
        break;
      case 'patient_not_owner':
        errorMessage = 'You can only edit your own case notes.';
        break;
      case 'insufficient_role':
        errorMessage = 'Your account does not have permission to edit case notes.';
        break;
    }
    
    throw new AppError(errorMessage, 403);
  }

  // Get latest case note with structured content
  const latestNotes = await db
    .select({
      id: caseNotes.id,
      structuredContent: caseNotes.structuredContent,
      version: caseNotes.version,
      // rawContent field removed
    })
    .from(caseNotes)
    .where(eq(caseNotes.caseId, caseId))
    .orderBy(desc(caseNotes.createdAt))
    .limit(1);
    
  // Get current structured content from latest note or create empty object
  const currentStructured = latestNotes.length > 0 ? latestNotes[0].structuredContent || {} : {};
  
  // Simple format: store content directly as string
  const updatedStructured = {
    ...currentStructured,
    [section]: content || '', // Store content directly as string, not as nested object
    [`${section}_lastUpdated`]: new Date().toISOString(),
    [`${section}_updatedBy`]: userId
  };

   // Use the same clinical notes type ID as in case creation
  const clinicalNotesTypeId = 'c6867d50-6dc7-443d-aa14-061ab153d685'; // Clinical Notes type
  
  // For debugging, log the note type ID
  logger.debug('Debug output', undefined, { data: 'Using clinical notes type ID:', clinicalNotesTypeId });
    
  // Mark any existing notes as inactive (versioning system)
  if (latestNotes.length > 0) {
    await db
      .update(caseNotes)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(caseNotes.id, latestNotes[0].id));
  }

  // Create a new case note with updated structured content
  const [updatedNote] = await db
    .insert(caseNotes)
    .values({
      caseId,
      doctorId: userId || '', // Using userId as doctorId for now
      noteTypeId: clinicalNotesTypeId,
      structuredContent: updatedStructured,
      version: latestNotes.length > 0 ? (latestNotes[0].version || 1) + 1 : 1, // Increment version
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(), // Explicitly set the updated timestamp
      // Initialize collaborative editing fields
      yjsDocument: null, // Will be populated when collaborative editing starts
      canvasBlocks: [],
      medicalCodes: {},
      activeEditors: [],
      documentVersion: 1,
      collaborationEnabled: true,
    })
    .returning();
    
  // Update the case's updatedAt timestamp
  const [updatedCase] = await db
    .update(cases)
    .set({
      updatedAt: new Date(),
    })
    .where(eq(cases.id, caseId))
    .returning();

  // Log audit event
  logAuditEvent(
    userId || '',  // Ensure userId is not undefined
    'CASE_SECTION_UPDATED',
    'cases',
    {
      caseId: updatedCase.id,
      section,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Case section updated successfully',
    structuredContent: updatedStructured,
  });
}));

// Remove a doctor from a case (admin/agent/assigned doctor only)
router.delete('/:id/doctors/:doctorId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  const doctorIdToRemove = req.params.doctorId;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Remove doctor route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Remove doctor route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  // Check if user has permission to remove doctors
  if (!userRoles?.includes('agent') && !userRoles?.includes('admin') && !userRoles?.includes('doctor')) {
    throw new AppError('Only agents, admins, and doctors can remove doctor assignments', 403);
  }

  // If user is a doctor, they can remove doctors from cases they have access to (assigned, working on, or can view)
  if (userRoles?.includes('doctor') && !userRoles?.includes('admin') && !userRoles?.includes('agent')) {
    // Check if doctor has any relationship with this case
    const isDoctorAssigned = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
    const doctorCases = await CaseDoctorService.getCasesForDoctor(userId);
    const hasAccessToCase = isDoctorAssigned || doctorCases.includes(caseId);
    
    if (!hasAccessToCase) {
      throw new AppError('You can only remove doctors from cases you are working on or have access to', 403);
    }
  }

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }

  // Use the CaseDoctorService to remove the doctor
  try {
    await CaseDoctorService.removeDoctorFromCase(caseId, doctorIdToRemove, userId);
    
    // Log audit event
    logAuditEvent(
      userId,
      'DOCTOR_REMOVED_FROM_CASE',
      'cases',
      {
        caseId: existingCase.id,
        removedDoctorId: doctorIdToRemove,
        removedBy: userId,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }
    );

    res.json({
      message: 'Doctor removed from case successfully',
    });
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('Failed to remove doctor from case', 500);
  }
}));

// Update time spent on a case by a doctor
router.put('/:id/time-tracking', opalAuthMiddleware, requireDoctorAcceptance, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  const { timeSpentMinutes, notes } = req.body;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Update time tracking route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Update time tracking route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  // Only doctors can track time
  if (!userRoles?.includes('doctor')) {
    throw new AppError('Only doctors can track time on cases', 403);
  }

  if (!timeSpentMinutes || timeSpentMinutes < 0) {
    throw new AppError('Valid time spent in minutes is required', 400);
  }

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }

  // Check if doctor is assigned to this case
  const [doctorAssignment] = await db
    .select()
    .from(caseDoctors)
    .where(
      and(
        eq(caseDoctors.caseId, caseId),
        eq(caseDoctors.doctorId, userId),
        eq(caseDoctors.isActive, true)
      )
    )
    .limit(1);

  if (!doctorAssignment) {
    throw new AppError('You are not assigned to this case', 403);
  }

  // Note: timeSpentMinutes, lastActivityAt, notes fields don't exist in current schema
  const [updatedAssignment] = await db
    .update(caseDoctors)
    .set({
      updatedAt: new Date(),
    })
    .where(eq(caseDoctors.id, doctorAssignment.id))
    .returning();

  // Log audit event
  logAuditEvent(
    userId,
    'CASE_TIME_TRACKED',
    'cases',
    {
      caseId: existingCase.id,
      doctorId: userId,
      timeSpentMinutes,
      totalTimeSpent: 0, // Field doesn't exist in current schema
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'Time tracking updated successfully',
    timeSpent: 0, // Field doesn't exist in current schema
    lastActivity: null, // Field doesn't exist in current schema
  });
}));

// Get time tracking summary for a case
router.get('/:id/time-tracking', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.id;
  
  // Get user data from OPAL middleware
  const opalUser = (req as any).user;
  
  // Use OPAL user data directly
  const userId = opalUser?.id || extractUserId(req);
  const userRoles = opalUser?.roles || extractUserRoles(req);
  const userRole = userRoles?.[0] || extractPrimaryRole(req, 'patient');
  
  // Log user role information for debugging
  logger.info('Get time tracking route - OPAL user:', { requestId: 'context-needed' }, { data: opalUser });
  logger.info('Get time tracking route - User ID:', { requestId: 'context-needed' }, { data: { userId, userRoles, userRole } });

  const [existingCase] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!existingCase) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  const canAccess =
    userRoles?.includes('admin') ||
    userRoles?.includes('agent') ||
    (userRoles?.includes('patient') && existingCase.patientId === userId) ||
    (userRoles?.includes('doctor') && await CaseDoctorService.isDoctorAssignedToCase(userId, caseId));

  if (!canAccess) {
    throw new AppError('Unauthorized to access this case', 403);
  }

  // Get time tracking data for all doctors on this case (simplified for current schema)
  const timeTrackingData = await db
    .select({
      doctorId: caseDoctors.doctorId,
      assignedAt: caseDoctors.assignedAt,
      doctorFirstName: users.firstName,
      doctorLastName: users.lastName,
      doctorBio: userProfiles.bio,
    })
    .from(caseDoctors)
    .leftJoin(users, eq(caseDoctors.doctorId, users.id))
    .leftJoin(userProfiles, eq(caseDoctors.doctorId, userProfiles.userId))
    .where(
      and(
        eq(caseDoctors.caseId, caseId),
        eq(caseDoctors.isActive, true)
      )
    )
    .orderBy(desc(caseDoctors.assignedAt));

  // Calculate total time spent across all doctors (field doesn't exist, return 0)
  const totalTimeSpent = 0;

  // Format response
  const response = {
    caseId,
    totalTimeSpent,
    doctorTimeTracking: timeTrackingData.map(doctor => ({
      doctorId: doctor.doctorId,
      doctorName: `${doctor.doctorFirstName || ''} ${doctor.doctorLastName || ''}`.trim() || 'Unknown',
      bio: doctor.doctorBio || '',
      timeSpentMinutes: 0, // Field doesn't exist in current schema
      timeSpentHours: 0, // Field doesn't exist in current schema
      lastActivityAt: null, // Field doesn't exist in current schema
      notes: null, // Field doesn't exist in current schema
      acceptanceStatus: 'pending', // Field doesn't exist in current schema
      acceptedAt: null, // Field doesn't exist in current schema
      assignedAt: doctor.assignedAt,
    })),
  };

  res.json(response);
}));

export { router as casesRoutes };
