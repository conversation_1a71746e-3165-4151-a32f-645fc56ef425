import { Router, Request, Response } from 'express';
import { db } from '../db/index.js';
import { auditLogs } from '../db/schema/audit.js';
import { users } from '../db/schema/users.js';
import { desc, eq, gte, lte, like, count, and } from 'drizzle-orm';
import { opalAuthMiddleware, requireRole } from '../middleware/opalAuth.js';
import { AppError } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Get audit logs - Admin (all logs) or Patient (filtered to their records)
router.get('/logs', opalAuthMiddleware, async (req, res) => {
  try {
    const { 
      userId, 
      action, 
      resource, 
      startDate, 
      endDate, 
      page = 1, 
      limit = 10 
    } = req.query;

    // Log audit event for accessing audit logs
    // logAuditEvent(
    //   req.user?.id || null,
    //   'READ',
    //   'audit_logs',
    //   {
    //     ip: req.ip,
    //     userAgent: req.get('User-Agent'),
    //     filters: { userId, action, resource, startDate, endDate, page, limit }
    //   }
    // );

    // Build where conditions
    const conditions = [];
    
    // If user is a patient, only show their own audit records
    if (req.user?.roles.includes('patient')) {
      conditions.push(eq(auditLogs.userId, req.user.id));
    } else if (userId) {
      // Admin can filter by specific userId
      conditions.push(eq(auditLogs.userId, userId as string));
    }
    
    if (action) {
      conditions.push(like(auditLogs.action, `%${action}%`));
    }
    
    if (resource) {
      conditions.push(like(auditLogs.resource, `%${resource}%`));
    }
    
    if (startDate) {
      conditions.push(gte(auditLogs.timestamp, new Date(startDate as string)));
    }
    
    if (endDate) {
      conditions.push(lte(auditLogs.timestamp, new Date(endDate as string)));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Calculate offset for pagination
    const pageNum = parseInt(page as string) || 1;
    const limitNum = parseInt(limit as string) || 10;
    const offset = (pageNum - 1) * limitNum;

    // Get total count for pagination
    const [totalResult] = await db
      .select({ count: count() })
      .from(auditLogs)
      .where(whereClause);

    const total = totalResult?.count || 0;

    // Get audit logs with user information
    const logs = await db
      .select({
        id: auditLogs.id,
        userId: auditLogs.userId,
        username: users.email, // Use email as username
        firstName: users.firstName,
        lastName: users.lastName,
        action: auditLogs.action,
        resource: auditLogs.resource,
        resourceId: auditLogs.resourceId,
        ipAddress: auditLogs.ipAddress,
        userAgent: auditLogs.userAgent,
        metadata: auditLogs.metadata,
        timestamp: auditLogs.timestamp,
      })
      .from(auditLogs)
      .leftJoin(users, eq(auditLogs.userId, users.id))
      .where(whereClause)
      .orderBy(desc(auditLogs.timestamp))
      .limit(limitNum)
      .offset(offset);

    // Transform the data to match the frontend interface
    const transformedLogs = logs.map(log => {
      // Handle metadata - convert object to string if it exists
      let details = `${log.action} on ${log.resource}${log.resourceId ? ` (ID: ${log.resourceId})` : ''}`;
      
      if (log.metadata && typeof log.metadata === 'object') {
        // Convert metadata object to a readable string
        try {
          const metadataStr = JSON.stringify(log.metadata, null, 2);
          details = `${details} - Metadata: ${metadataStr}`;
        } catch (e) {
          // If JSON.stringify fails, just use the basic details
          logger.warn('Failed to stringify metadata:', undefined, { data: e });
        }
      } else if (log.metadata && typeof log.metadata === 'string') {
        details = `${details} - ${log.metadata}`;
      }
      
      return {
        id: log.id,
        userId: log.userId || 'system',
        username: log.username || `${log.firstName || ''} ${log.lastName || ''}`.trim() || 'System User',
        action: log.action,
        resource: log.resource,
        details: details,
        timestamp: log.timestamp.toISOString(),
      };
    });

    res.json({
      success: true,
      data: transformedLogs,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum)
      }
    });

  } catch (error) {
    logger.error('Error fetching audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit logs',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get audit log statistics - Admin only
router.get('/stats', opalAuthMiddleware, requireRole(['admin']), async (req, res) => {
  try {
    // Log audit event
    logAuditEvent(
      req.user?.id || null,
      'READ',
      'audit_stats',
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    // Get basic statistics
    const [totalLogs] = await db
      .select({ count: count() })
      .from(auditLogs);

    // Get logs from last 24 hours
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const [recentLogs] = await db
      .select({ count: count() })
      .from(auditLogs)
      .where(gte(auditLogs.timestamp, yesterday));

    // Get most common actions
    const actionStats = await db
      .select({
        action: auditLogs.action,
        count: count()
      })
      .from(auditLogs)
      .groupBy(auditLogs.action)
      .orderBy(desc(count()))
      .limit(10);

    // Get most active users
    const userStats = await db
      .select({
        userId: auditLogs.userId,
        username: users.email,
        count: count()
      })
      .from(auditLogs)
      .leftJoin(users, eq(auditLogs.userId, users.id))
      .where(gte(auditLogs.timestamp, yesterday))
      .groupBy(auditLogs.userId, users.email)
      .orderBy(desc(count()))
      .limit(10);

    res.json({
      success: true,
      data: {
        totalLogs: totalLogs?.count || 0,
        recentLogs: recentLogs?.count || 0,
        topActions: actionStats,
        activeUsers: userStats.filter(u => u.userId) // Filter out null userIds
      }
    });

  } catch (error) {
    logger.error('Error fetching audit statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

export default router;
