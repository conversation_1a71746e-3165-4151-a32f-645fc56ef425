import { Router, Request, Response } from 'express';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import { db } from '../db/index.js';
import { users, socialLogins, otpVerifications } from '../db/schema/users.js';
import { roles as rolesTable, userRoles } from '../db/schema/permissions.js';
import { eq, and, or } from 'drizzle-orm';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { sessionManager } from '../middleware/opalAuth.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Validation schemas
const googleOneTapSchema = z.object({
  credential: z.string().min(1, 'Google credential is required'),
  clientId: z.string().min(1, 'Client ID is required'),
});

const whatsappOtpRequestSchema = z.object({
  phoneNumber: z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format'),
});

const whatsappOtpVerifySchema = z.object({
  phoneNumber: z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format'),
  otp: z.string().length(6, 'OTP must be 6 digits'),
});

const usernamePasswordSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
});

// JWT utility functions
function generateToken(userId: string, email: string, roles: string[], sessionId: string): string {
  if (!process.env.JWT_SECRET) {
    throw new AppError('JWT_SECRET not configured', 500);
  }
  
  return jwt.sign(
    { 
      userId, 
      email, 
      roles, 
      sessionId 
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
}

// Verify Google JWT token
async function verifyGoogleToken(credential: string, clientId: string) {
  try {
    // In production, you would verify the JWT with Google's public keys
    // For now, we'll decode it without verification (NOT SECURE - implement proper verification)
    const decoded = jwt.decode(credential) as any;
    
    if (!decoded || !decoded.email || !decoded.sub) {
      throw new Error('Invalid Google token');
    }

    return {
      googleId: decoded.sub,
      email: decoded.email,
      firstName: decoded.given_name || '',
      lastName: decoded.family_name || '',
      profilePicture: decoded.picture || null,
      emailVerified: decoded.email_verified || false,
    };
  } catch (error) {
    throw new AppError('Invalid Google credential', 400);
  }
}

// Generate OTP
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Send WhatsApp OTP (mock implementation)
async function sendWhatsAppOTP(phoneNumber: string, otp: string): Promise<boolean> {
  // TODO: Implement actual WhatsApp Business API integration
  logger.debug(`Sending OTP ${otp} to ${phoneNumber} via WhatsApp`);
  
  // Mock success for development
  return true;
}

// Google One-Tap authentication
router.post('/google-one-tap', asyncHandler(async (req: Request, res: Response) => {
  const validatedData = googleOneTapSchema.parse(req.body);
  
  // Verify Google token
  const googleData = await verifyGoogleToken(validatedData.credential, validatedData.clientId);
  
  // Check if user exists by email
  let [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.email, googleData.email))
    .limit(1);

  let userId: string;
  let isNewUser = false;

  if (existingUser) {
    // User exists, update last login
    await db
      .update(users)
      .set({ 
        lastLoginAt: new Date(),
        isEmailVerified: googleData.emailVerified || existingUser.isEmailVerified,
        profilePictureUrl: googleData.profilePicture || existingUser.profilePictureUrl,
      })
      .where(eq(users.id, existingUser.id));
    
    userId = existingUser.id;
  } else {
    // Create new user
    const [newUser] = await db
      .insert(users)
      .values({
        email: googleData.email,
        firstName: googleData.firstName || 'User',
        lastName: googleData.lastName || '',
        role: 'patient',
        isEmailVerified: googleData.emailVerified,
        profilePictureUrl: googleData.profilePicture,
        lastLoginAt: new Date(),
      })
      .returning({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
      });

    userId = newUser.id;
    existingUser = newUser as any;
    isNewUser = true;

    // Assign patient role
    const [patientRole] = await db
      .select()
      .from(rolesTable)
      .where(eq(rolesTable.name, 'patient'))
      .limit(1);

    if (patientRole) {
      await db
        .insert(userRoles)
        .values({
          userId: newUser.id,
          roleId: patientRole.id,
          assignedBy: newUser.id,
          assignedAt: new Date(),
        });
    }

    // Campaign functionality removed
  }

  // Check if Google social login exists
  let [socialLogin] = await db
    .select()
    .from(socialLogins)
    .where(and(
      eq(socialLogins.userId, userId),
      eq(socialLogins.provider, 'google'),
      eq(socialLogins.providerId, googleData.googleId)
    ))
    .limit(1);

  if (socialLogin) {
    // Update existing social login
    await db
      .update(socialLogins)
      .set({
        lastUsedAt: new Date(),
        providerEmail: googleData.email,
        isVerified: googleData.emailVerified,
      })
      .where(eq(socialLogins.id, socialLogin.id));
  } else {
    // Create new social login record
    await db
      .insert(socialLogins)
      .values({
        userId,
        provider: 'google',
        providerId: googleData.googleId,
        providerEmail: googleData.email,
        providerData: JSON.stringify(googleData),
        isVerified: googleData.emailVerified,
        lastUsedAt: new Date(),
      });
  }

  // Create session
  const sessionId = sessionManager.createSession(userId);
  const roles = [existingUser.role];
  const token = generateToken(userId, existingUser.email, roles, sessionId);

  // Log audit event
  logAuditEvent(
    userId,
    isNewUser ? 'GOOGLE_REGISTRATION' : 'GOOGLE_LOGIN',
    'users',
    {
      provider: 'google',
      email: googleData.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  // Set HTTP-only cookie
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost'
  });

  res.status(isNewUser ? 201 : 200).json({
    message: isNewUser ? 'User registered successfully with Google' : 'Login successful with Google',
    user: {
      id: existingUser.id,
      email: existingUser.email,
      firstName: existingUser.firstName,
      lastName: existingUser.lastName,
      role: existingUser.role,
      profilePictureUrl: googleData.profilePicture,
    },
    token,
    isNewUser,
  });
}));

// WhatsApp OTP request
router.post('/whatsapp/request-otp', asyncHandler(async (req: Request, res: Response) => {
  const validatedData = whatsappOtpRequestSchema.parse(req.body);
  
  // Generate OTP
  const otp = generateOTP();
  const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  // Store OTP in database
  await db
    .insert(otpVerifications)
    .values({
      identifier: validatedData.phoneNumber,
      identifierType: 'phone',
      otp,
      purpose: 'login',
      expiresAt,
    });

  // Send OTP via WhatsApp
  const sent = await sendWhatsAppOTP(validatedData.phoneNumber, otp);
  
  if (!sent) {
    throw new AppError('Failed to send OTP', 500);
  }

  // Log audit event
  logAuditEvent(
    null,
    'WHATSAPP_OTP_REQUESTED',
    'otp_verifications',
    {
      phoneNumber: validatedData.phoneNumber,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: 'OTP sent successfully to WhatsApp',
    expiresIn: 600, // 10 minutes in seconds
  });
}));

// WhatsApp OTP verification
router.post('/whatsapp/verify-otp', asyncHandler(async (req: Request, res: Response) => {
  const validatedData = whatsappOtpVerifySchema.parse(req.body);
  
  // Find valid OTP
  const [otpRecord] = await db
    .select()
    .from(otpVerifications)
    .where(and(
      eq(otpVerifications.identifier, validatedData.phoneNumber),
      eq(otpVerifications.identifierType, 'phone'),
      eq(otpVerifications.otp, validatedData.otp),
      eq(otpVerifications.isUsed, false),
      eq(otpVerifications.purpose, 'login')
    ))
    .limit(1);

  if (!otpRecord) {
    throw new AppError('Invalid or expired OTP', 400);
  }

  // Check if OTP is expired
  if (new Date() > otpRecord.expiresAt) {
    throw new AppError('OTP has expired', 400);
  }

  // Mark OTP as used
  await db
    .update(otpVerifications)
    .set({ isUsed: true, updatedAt: new Date() })
    .where(eq(otpVerifications.id, otpRecord.id));

  // Check if user exists with this phone number
  let [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.phoneNumber, validatedData.phoneNumber))
    .limit(1);

  let userId: string;
  let isNewUser = false;

  if (existingUser) {
    // User exists, update last login
    await db
      .update(users)
      .set({ lastLoginAt: new Date() })
      .where(eq(users.id, existingUser.id));
    
    userId = existingUser.id;
  } else {
    // Create new user with phone number
    const [newUser] = await db
      .insert(users)
      .values({
        email: `${validatedData.phoneNumber.replace('+', '')}@whatsapp.temp`, // Temporary email
        firstName: 'WhatsApp',
        lastName: 'User',
        phoneNumber: validatedData.phoneNumber,
        role: 'patient',
        isEmailVerified: false,
        lastLoginAt: new Date(),
      })
      .returning({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
        phoneNumber: users.phoneNumber,
      });

    userId = newUser.id;
    existingUser = newUser as any;
    isNewUser = true;

    // Assign patient role
    const [patientRole] = await db
      .select()
      .from(rolesTable)
      .where(eq(rolesTable.name, 'patient'))
      .limit(1);

    if (patientRole) {
      await db
        .insert(userRoles)
        .values({
          userId: newUser.id,
          roleId: patientRole.id,
          assignedBy: newUser.id,
          assignedAt: new Date(),
        });
    }

    // Campaign functionality removed
  }

  // Create or update WhatsApp social login
  let [socialLogin] = await db
    .select()
    .from(socialLogins)
    .where(and(
      eq(socialLogins.userId, userId),
      eq(socialLogins.provider, 'whatsapp'),
      eq(socialLogins.providerId, validatedData.phoneNumber)
    ))
    .limit(1);

  if (socialLogin) {
    // Update existing social login
    await db
      .update(socialLogins)
      .set({
        lastUsedAt: new Date(),
        isVerified: true,
      })
      .where(eq(socialLogins.id, socialLogin.id));
  } else {
    // Create new social login record
    await db
      .insert(socialLogins)
      .values({
        userId,
        provider: 'whatsapp',
        providerId: validatedData.phoneNumber,
        providerData: JSON.stringify({ phoneNumber: validatedData.phoneNumber }),
        isVerified: true,
        lastUsedAt: new Date(),
      });
  }

  // Create session
  const sessionId = sessionManager.createSession(userId);
  const roles = [existingUser.role];
  const token = generateToken(userId, existingUser.email, roles, sessionId);

  // Log audit event
  logAuditEvent(
    userId,
    isNewUser ? 'WHATSAPP_REGISTRATION' : 'WHATSAPP_LOGIN',
    'users',
    {
      provider: 'whatsapp',
      phoneNumber: validatedData.phoneNumber,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  // Set HTTP-only cookie
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost'
  });

  res.status(isNewUser ? 201 : 200).json({
    message: isNewUser ? 'User registered successfully with WhatsApp' : 'Login successful with WhatsApp',
    user: {
      id: existingUser.id,
      email: existingUser.email,
      firstName: existingUser.firstName,
      lastName: existingUser.lastName,
      role: existingUser.role,
      phoneNumber: existingUser.phoneNumber,
    },
    token,
    isNewUser,
    requiresEmailUpdate: isNewUser, // Flag to indicate user should update their email
  });
}));

// Username/Password fallback registration
router.post('/username-password/register', asyncHandler(async (req: Request, res: Response) => {
  const validatedData = usernamePasswordSchema.parse(req.body);
  
  // Check if user already exists
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.email, validatedData.email))
    .limit(1);

  if (existingUser) {
    throw new AppError('User already exists with this email', 409);
  }

  // Hash password
  const bcrypt = await import('bcryptjs');
  const saltRounds = 12;
  const passwordHash = await bcrypt.default.hash(validatedData.password, saltRounds);

  // Create user
  const [newUser] = await db
    .insert(users)
    .values({
      email: validatedData.email,
      passwordHash,
      firstName: validatedData.firstName,
      lastName: validatedData.lastName,
      role: 'patient',
      lastLoginAt: new Date(),
    })
    .returning({
      id: users.id,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      role: users.role,
    });

  // Assign patient role
  const [patientRole] = await db
    .select()
    .from(rolesTable)
    .where(eq(rolesTable.name, 'patient'))
    .limit(1);

  if (patientRole) {
    await db
      .insert(userRoles)
      .values({
        userId: newUser.id,
        roleId: patientRole.id,
        assignedBy: newUser.id,
        assignedAt: new Date(),
      });
  }

  // Create session
  const sessionId = sessionManager.createSession(newUser.id);
  const roles = [newUser.role];
  const token = generateToken(newUser.id, newUser.email, roles, sessionId);

  // Log audit event
  logAuditEvent(
    newUser.id,
    'USERNAME_PASSWORD_REGISTRATION',
    'users',
    {
      email: newUser.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  // Campaign functionality removed

  // Set HTTP-only cookie
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost'
  });

  res.status(201).json({
    message: 'User registered successfully',
    user: newUser,
    token,
    isNewUser: true,
  });
}));

export { router as socialAuthRoutes };