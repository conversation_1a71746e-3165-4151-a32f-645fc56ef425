# Pre-commit hooks configuration
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: mixed-line-ending
        args: ['--fix=lf']

  # TypeScript/JavaScript linting
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        files: \.(js|jsx|ts|tsx)$
        types: [file]
        additional_dependencies:
          - eslint@^8.44.0
          - '@typescript-eslint/eslint-plugin@^6.0.0'
          - '@typescript-eslint/parser@^6.0.0'

  # TypeScript type checking
  - repo: local
    hooks:
      - id: tsc-api
        name: TypeScript check (API)
        entry: bash -c 'cd api && npm run type-check'
        language: system
        files: ^api/.*\.(ts|tsx)$
        pass_filenames: false

      - id: tsc-care
        name: TypeScript check (Care)
        entry: bash -c 'cd care && npm run type-check'
        language: system
        files: ^care/.*\.(ts|tsx)$
        pass_filenames: false

      - id: tsc-desk
        name: TypeScript check (Desk)
        entry: bash -c 'cd desk && npm run type-check'
        language: system
        files: ^desk/.*\.(ts|tsx)$
        pass_filenames: false

  # Security scanning
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: package-lock.json

  # Docker linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: ['--ignore', 'DL3008', '--ignore', 'DL3009']

  # SQL formatting (for migration files)
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.1.2
    hooks:
      - id: sqlfluff-lint
        files: \.sql$
        args: ['--dialect', 'postgres']

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.35.0
    hooks:
      - id: markdownlint
        args: ['--fix']
        files: \.md$

  # Custom hooks for project-specific checks
  - repo: local
    hooks:
      - id: check-unused-imports
        name: Check for unused imports
        entry: bash -c 'find api/src care/src desk/src -name "*.ts" -o -name "*.tsx" | xargs grep -l "import.*from.*;" | xargs -I {} sh -c "echo \"Checking {}\" && grep -n \"import.*from\" {} || true"'
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false

      - id: check-missing-middleware
        name: Check for missing middleware imports
        entry: bash -c 'find api/src -name "*.ts" | xargs grep -l "authMiddleware" | xargs -I {} sh -c "echo \"Found authMiddleware usage in {}\" && exit 1" || true'
        language: system
        files: ^api/.*\.ts$
        pass_filenames: false

      - id: check-console-logs
        name: Check for console.log statements
        entry: bash -c 'find api/src care/src desk/src -name "*.ts" -o -name "*.tsx" | xargs grep -n "console\.log" && exit 1 || true'
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false

      - id: check-todo-comments
        name: Check for TODO comments
        entry: bash -c 'find api/src care/src desk/src -name "*.ts" -o -name "*.tsx" | xargs grep -n "TODO:" | head -10'
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false
        verbose: true