#!/bin/bash

# SSL Certificate Installation Script for Continuia Healthcare Platform
# This script adds the self-signed certificate to the system's trust store

set -e

CERT_FILE="./certs/continuia.health.crt"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CERT_PATH="$SCRIPT_DIR/$CERT_FILE"

echo "🔐 Installing SSL Certificate for Continuia Healthcare Platform"
echo "=================================================="

# Check if certificate file exists
if [ ! -f "$CERT_PATH" ]; then
    echo "❌ Certificate file not found: $CERT_PATH"
    echo "Please ensure you're running this script from the my.continuia directory"
    exit 1
fi

# Detect operating system
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🐧 Detected Linux system"
    
    # Check if running as root
    if [ "$EUID" -ne 0 ]; then
        echo "⚠️  This script needs to be run with sudo privileges"
        echo "Please run: sudo ./install-ssl-cert.sh"
        exit 1
    fi
    
    # Install certificate on Linux
    echo "📋 Installing certificate to system trust store..."
    cp "$CERT_PATH" /usr/local/share/ca-certificates/continuia.health.crt
    update-ca-certificates
    
    echo "✅ Certificate installed successfully!"
    echo "🔄 You may need to restart your browser for changes to take effect"
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Detected macOS system"
    
    # Install certificate on macOS
    echo "📋 Installing certificate to system keychain..."
    echo "⚠️  You may be prompted for your password"
    
    sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain "$CERT_PATH"
    
    echo "✅ Certificate installed successfully!"
    echo "🔄 You may need to restart your browser for changes to take effect"
    
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    echo "🪟 Detected Windows system"
    echo "⚠️  For Windows, please manually install the certificate:"
    echo "1. Double-click on: $(realpath "$CERT_PATH")"
    echo "2. Click 'Install Certificate...'"
    echo "3. Select 'Local Machine' and click 'Next'"
    echo "4. Select 'Place all certificates in the following store'"
    echo "5. Click 'Browse' and select 'Trusted Root Certification Authorities'"
    echo "6. Click 'Next' and then 'Finish'"
    echo "7. Restart your browser"
    
else
    echo "❓ Unknown operating system: $OSTYPE"
    echo "Please manually add the certificate to your system's trust store"
    echo "Certificate location: $(realpath "$CERT_PATH")"
fi

echo ""
echo "🌐 After installation, you should be able to access:"
echo "   • https://continuia.health"
echo "   • https://care.continuia.health"
echo "   • https://desk.continuia.health"
echo "   • https://api.continuia.health"
echo ""
echo "🔒 Without browser security warnings!"